# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# C extensions
*.so

# Distribution / packaging
.Python
env/
.venv
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg'
venv

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover

# Translations
*.mo
*.pot

# Django stuff:
*.log

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# DotEnv configuration
.env

# Database
*.db
*.rdb

# Pycharm
../.idea
.idea/

# VS Code
.vscode/

# VS Code Counter
.VSCodeCounter/

# Spyder
.spyproject/

# Jupyter NB Checkpoints
.ipynb_checkpoints/

# Mac OS-specific storage files
.DS_Store

# vim
*.swp
*.swo

# Caches
.mypy_cache/
.pytest_cache/
.ruff_cache/

# Hydra logs
outputs

# data folder
data/**/*.json
data/**/*.pdf
data/*
examples/*.json
data/**/*.docx
data

# Explicitly ignore root-level data directory
/data/
/samples/

.history
change_log.txt
*.docx
*.pdf