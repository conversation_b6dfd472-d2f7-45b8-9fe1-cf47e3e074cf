#!/usr/bin/env python3
"""
Script to extract file paths from a folder and populate them in a CSV file.
Creates a CSV file called 'nz_csp_samples_DI.csv' with a 'DocumentPath' column.
"""

import os
import csv
from pathlib import Path
from typing import List

# Configuration Variables - Edit these as needed
FOLDER_PATH = "/home/<USER>/repos/OCR_in_house/data/samples/nz_csp_samples_DI"  # Path to folder to scan
OUTPUT_CSV = "/home/<USER>/repos/OCR_in_house/data/nz_csp_samples_DI.csv"  # Output CSV filename
RECURSIVE_SCAN = True  # Whether to scan subdirectories recursively
USE_RELATIVE_PATHS = False  # Whether to use relative paths instead of absolute paths


def get_all_file_paths(folder_path: str, recursive: bool = True) -> List[str]:
    """
    Extract all file paths from a given folder.
    
    Args:
        folder_path (str): Path to the folder to scan
        recursive (bool): Whether to scan subdirectories recursively
    
    Returns:
        List[str]: List of file paths
    """
    file_paths = []
    folder_path = Path(folder_path)
    
    if not folder_path.exists():
        raise FileNotFoundError(f"Folder not found: {folder_path}")
    
    if not folder_path.is_dir():
        raise ValueError(f"Path is not a directory: {folder_path}")
    
    if recursive:
        # Get all files recursively
        for file_path in folder_path.rglob('*'):
            if file_path.is_file():
                file_paths.append(str(file_path.absolute()))
    else:
        # Get files only in the current directory
        for item in folder_path.iterdir():
            if item.is_file():
                file_paths.append(str(item.absolute()))
    
    return sorted(file_paths)


def create_csv_with_file_paths(folder_path: str, output_csv: str = "nz_csp_samples_DI.csv", 
                             recursive: bool = True, relative_paths: bool = False):
    """
    Create a CSV file with file paths from a given folder.
    
    Args:
        folder_path (str): Path to the folder to scan
        output_csv (str): Name of the output CSV file
        recursive (bool): Whether to scan subdirectories recursively
        relative_paths (bool): Whether to use relative paths instead of absolute paths
    """
    try:
        # Get all file paths
        file_paths = get_all_file_paths(folder_path, recursive=recursive)
        
        if not file_paths:
            print(f"No files found in folder: {folder_path}")
            return
        
        # Convert to relative paths if requested
        if relative_paths:
            base_path = Path(folder_path)
            file_paths = [str(Path(fp).relative_to(base_path.parent)) for fp in file_paths]
        
        # Create CSV file
        with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(['DocumentPath'])
            
            # Write file paths
            for file_path in file_paths:
                writer.writerow([file_path])
        
        print(f"Successfully created {output_csv} with {len(file_paths)} file paths")
        print(f"Output location: {os.path.abspath(output_csv)}")
        
        # Show first few entries as preview
        if len(file_paths) > 0:
            print("\nPreview of first few entries:")
            for i, path in enumerate(file_paths[:5]):
                print(f"  {i+1}. {path}")
            if len(file_paths) > 5:
                print(f"  ... and {len(file_paths) - 5} more files")
    
    except Exception as e:
        print(f"Error: {e}")


def main():
    print(f"Scanning folder: {FOLDER_PATH}")
    print(f"Output CSV: {OUTPUT_CSV}")
    print(f"Recursive scan: {RECURSIVE_SCAN}")
    print(f"Relative paths: {USE_RELATIVE_PATHS}")
    print("-" * 50)
    
    create_csv_with_file_paths(
        folder_path=FOLDER_PATH,
        output_csv=OUTPUT_CSV,
        recursive=RECURSIVE_SCAN,
        relative_paths=USE_RELATIVE_PATHS
    )


if __name__ == "__main__":
    main()