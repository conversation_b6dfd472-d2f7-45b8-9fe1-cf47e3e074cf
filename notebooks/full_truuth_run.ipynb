{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import pandas as pd\n", "from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient\n", "from datetime import datetime, timedelta\n", "import sys\n", "import os\n", "from pathlib import Path\n", "\n", "\n", "# Adjust the path to properly include the src directory\n", "module_path = os.path.abspath(os.path.join(\"..\"))  # Go up two levels to reach project root\n", "if module_path not in sys.path:\n", "    sys.path.append(module_path)\n", "\n", "from src.sample_collection.sql_engine import *\n", "from dotenv import load_dotenv\n", "\n", "# Explicitly load the .env file\n", "load_dotenv()\n", "ROOTDIR = Path(\"/home/<USER>/repos/OCR_in_house\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# sample prefix\n", "sample_prefix = \"test\"\n", "# Data location\n", "DATA_FOLDER = ROOTDIR / f\"data/samples/{sample_prefix}_samples_DI\"\n", "RAW_DATA_FILE = ROOTDIR / f\"data/input/{sample_prefix}_samples_DI.csv\"\n", "OUTPUT_DATA_FOLDER = ROOTDIR / f\"data/di/{sample_prefix}_samples_DI_res\"\n", "UPM_GROUND_TRUTH_PATH = ROOTDIR / f\"data/sot/{sample_prefix}_SourceOfTruth.xlsx\"\n", "PADDLEOCR_RES_PATH = ROOTDIR / f\"data/paddleocr/{sample_prefix}_samples_paddleocr.csv\"\n", "TRUUTH_PATH = ROOTDIR / f\"data/truuth/{sample_prefix}_samples_truuth.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sample Colection"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### SQL Engine Connection"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["claryt\n", "10.3.0.90\n", "BIA\n"]}], "source": ["# engine = Engine.disconnect()\n", "engine = Engine.get_engine(server=\"10.3.0.90\", database=\"BIA\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Read Selected Sample Invoices from Spreadsheet"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["samples_di = pd.read_csv(RAW_DATA_FILE)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Retrieve and download document path"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def load_claim_doc_path(df, engine):\n", "    if not df.empty:\n", "        claim_numbers = \"','\".join(\n", "            df[\"ClaimNo\"].astype(str).apply(lambda x: x.split(\"-\")[0]).unique().tolist()\n", "        )\n", "\n", "        sql_query_policy = rf\"\"\"\n", "\n", "        SELECT\n", "            <PERSON><PERSON><PERSON><PERSON>,\n", "            d.*\n", "        FROM [COS].[dbo].[Document] d\n", "            LEFT JOIN [COS].[dbo].[Claim] c\n", "            ON d.ClaimRefNumber = c.ClaimRefNumber\n", "        WHERE [DocumentPath] LIKE '%csp%'\n", "            AND [DocumentType] = 'ClaimInvoice'\n", "            AND c<PERSON><PERSON>\n", "                IN ('{claim_numbers}')\n", "        \"\"\"\n", "        df_sot = pd.read_sql(sql_query_policy, engine)\n", "\n", "    # Return both DataFrames\n", "    return df_sot"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["doc_path = load_claim_doc_path(samples_di, engine)\n", "\n", "# Separate document container and document file\n", "doc_path['DocContainer'] = doc_path['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[0]\n", "doc_path['DocFile'] = doc_path['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[1]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# define credentials\n", "    \n", "source_key = 'ju9EsJDjAagISO4SHE+y9aaFFm80hb51pVn2vAog4IHeA4lJ4IhhvR2kUYweeknCjfwQMd618JYqHENoWz4moQ=='\n", "\n", "source_account_name = 'p3storageprod'  #'p3storagearchive'\n", "\n", "sas_token = generate_account_sas(account_name=source_account_name, account_key=source_key,\n", "                                     resource_types=ResourceTypes(\n", "                                         service=True, container=True, object=True),\n", "permission=AccountSasPermissions(read=True),\n", "                                     expiry=datetime.utcnow() + <PERSON><PERSON><PERSON>(hours=1))\n", "\n", "source_blob_service_client = BlobServiceClient(account_url=f'https://{source_account_name}.blob.core.windows.net/', credential=source_key)\n", "\n", "# Create download function\n", "def download(blob_service_client: BlobServiceClient, container_name: str, file_name: str, dest_path):\n", "        container_client = blob_service_client.get_container_client(container_name)\n", "        blob_client = container_client.get_blob_client(file_name)\n", "        with open(dest_path, \"wb\") as f:\n", "            f.write(blob_client.download_blob().readall())\n", "\n", "# Create output directory if it doesn't exist\n", "if not Path(DATA_FOLDER).exists():\n", "    os.makedirs(DATA_FOLDER)\n", "\n", "# Download documents from blob storage to local folder called pdf             \n", "for idx in doc_path.index:\n", "    path =  DATA_FOLDER +'/' + doc_path['DocFile'][idx]\n", "    download(source_blob_service_client,doc_path['DocContainer'].iloc[idx],doc_path['DocFile'].iloc[idx],path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Retrieve Truth Output"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["def load_claim_truuth(df, engine):\n", "    if not df.empty:\n", "        claim_numbers = \"','\".join(\n", "            df[\"ClaimNo\"].astype(str).apply(lambda x: x.split(\"-\")[0]).unique().tolist()\n", "        )\n", "\n", "        sql_query_policy = rf\"\"\"\n", "\n", "        SELECT\n", "            <PERSON><PERSON><PERSON><PERSON>,\n", "            cc.CustomerId,\n", "            cc.ClaimPolicyNumber,\n", "            cc.CosReference,\n", "            cc.OCRDocumentConfidence,\n", "            cc.OCRResponseObject\n", "\n", "        FROM [CSP_ODS_PROD].[dbo].[CustomerClaim] cc\n", "            LEFT JOIN [COS].[dbo].[Claim] c\n", "            ON cc.COSReference = c.ClaimRefNumber COLLATE DATABASE_DEFAULT\n", "        WHER<PERSON> c<PERSON>\n", "                IN ('{claim_numbers}')\n", "                AND cc.OCRReferenceId IS NOT NULL\n", "        \"\"\"\n", "        df_truuth = pd.read_sql(sql_query_policy, engine)\n", "\n", "    # Return both DataFrames\n", "    return df_truuth"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["df_truuth = load_claim_truuth(samples_di, engine)"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/plain": ["159"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df_truuth)"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"ename": "PermissionError", "evalue": "[Errno 13] Permission denied: '/home/<USER>/repos/OCR_in_house/data/truuth/test_samples_truuth.csv'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mPermissionError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[68], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mdf_truuth\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mTRUUTH_PATH\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/repos/OCR_in_house/.venv/lib/python3.10/site-packages/pandas/util/_decorators.py:333\u001b[0m, in \u001b[0;36mdeprecate_nonkeyword_arguments.<locals>.decorate.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    327\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(args) \u001b[38;5;241m>\u001b[39m num_allow_args:\n\u001b[1;32m    328\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m    329\u001b[0m         msg\u001b[38;5;241m.\u001b[39mformat(arguments\u001b[38;5;241m=\u001b[39m_format_argument_list(allow_args)),\n\u001b[1;32m    330\u001b[0m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[1;32m    331\u001b[0m         stacklevel\u001b[38;5;241m=\u001b[39mfind_stack_level(),\n\u001b[1;32m    332\u001b[0m     )\n\u001b[0;32m--> 333\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/repos/OCR_in_house/.venv/lib/python3.10/site-packages/pandas/core/generic.py:3967\u001b[0m, in \u001b[0;36mNDFrame.to_csv\u001b[0;34m(self, path_or_buf, sep, na_rep, float_format, columns, header, index, index_label, mode, encoding, compression, quoting, quotechar, lineterminator, chunksize, date_format, doublequote, escapechar, decimal, errors, storage_options)\u001b[0m\n\u001b[1;32m   3956\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(\u001b[38;5;28mself\u001b[39m, ABCDataFrame) \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mto_frame()\n\u001b[1;32m   3958\u001b[0m formatter \u001b[38;5;241m=\u001b[39m DataFrameFormatter(\n\u001b[1;32m   3959\u001b[0m     frame\u001b[38;5;241m=\u001b[39mdf,\n\u001b[1;32m   3960\u001b[0m     header\u001b[38;5;241m=\u001b[39mheader,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   3964\u001b[0m     decimal\u001b[38;5;241m=\u001b[39mdecimal,\n\u001b[1;32m   3965\u001b[0m )\n\u001b[0;32m-> 3967\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mDataFrameRenderer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mformatter\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_csv\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   3968\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpath_or_buf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3969\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlineterminator\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlineterminator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3970\u001b[0m \u001b[43m    \u001b[49m\u001b[43msep\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msep\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3971\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3972\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3973\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcompression\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3974\u001b[0m \u001b[43m    \u001b[49m\u001b[43mquoting\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquoting\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3975\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3976\u001b[0m \u001b[43m    \u001b[49m\u001b[43mindex_label\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindex_label\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3977\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3978\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunksize\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunksize\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3979\u001b[0m \u001b[43m    \u001b[49m\u001b[43mquotechar\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquotechar\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3980\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdate_format\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdate_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3981\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdoublequote\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdoublequote\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3982\u001b[0m \u001b[43m    \u001b[49m\u001b[43mescapechar\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mescapechar\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3983\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3984\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/repos/OCR_in_house/.venv/lib/python3.10/site-packages/pandas/io/formats/format.py:1014\u001b[0m, in \u001b[0;36mDataFrameRenderer.to_csv\u001b[0;34m(self, path_or_buf, encoding, sep, columns, index_label, mode, compression, quoting, quotechar, lineterminator, chunksize, date_format, doublequote, escapechar, errors, storage_options)\u001b[0m\n\u001b[1;32m    993\u001b[0m     created_buffer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[1;32m    995\u001b[0m csv_formatter \u001b[38;5;241m=\u001b[39m CSVFormatter(\n\u001b[1;32m    996\u001b[0m     path_or_buf\u001b[38;5;241m=\u001b[39mpath_or_buf,\n\u001b[1;32m    997\u001b[0m     lineterminator\u001b[38;5;241m=\u001b[39mlineterminator,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1012\u001b[0m     formatter\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfmt,\n\u001b[1;32m   1013\u001b[0m )\n\u001b[0;32m-> 1014\u001b[0m \u001b[43mcsv_formatter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msave\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1016\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m created_buffer:\n\u001b[1;32m   1017\u001b[0m     \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(path_or_buf, StringIO)\n", "File \u001b[0;32m~/repos/OCR_in_house/.venv/lib/python3.10/site-packages/pandas/io/formats/csvs.py:251\u001b[0m, in \u001b[0;36mCSVFormatter.save\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    247\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    248\u001b[0m \u001b[38;5;124;03mCreate the writer & save.\u001b[39;00m\n\u001b[1;32m    249\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    250\u001b[0m \u001b[38;5;66;03m# apply compression and byte/text conversion\u001b[39;00m\n\u001b[0;32m--> 251\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    252\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    253\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    254\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    255\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    256\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcompression\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    257\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    258\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m handles:\n\u001b[1;32m    259\u001b[0m     \u001b[38;5;66;03m# Note: self.encoding is irrelevant here\u001b[39;00m\n\u001b[1;32m    260\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mwriter \u001b[38;5;241m=\u001b[39m csvlib\u001b[38;5;241m.\u001b[39mwriter(\n\u001b[1;32m    261\u001b[0m         handles\u001b[38;5;241m.\u001b[39mhandle,\n\u001b[1;32m    262\u001b[0m         lineterminator\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlineterminator,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    267\u001b[0m         quotechar\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mquotechar,\n\u001b[1;32m    268\u001b[0m     )\n\u001b[1;32m    270\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_save()\n", "File \u001b[0;32m~/repos/OCR_in_house/.venv/lib/python3.10/site-packages/pandas/io/common.py:873\u001b[0m, in \u001b[0;36mget_handle\u001b[0;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[1;32m    868\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[1;32m    869\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[1;32m    870\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[1;32m    871\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[1;32m    872\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[0;32m--> 873\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m    874\u001b[0m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    875\u001b[0m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    876\u001b[0m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    877\u001b[0m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    878\u001b[0m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    879\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    880\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    881\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[1;32m    882\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[0;31mPermissionError\u001b[0m: [<PERSON>rr<PERSON> 13] Permission denied: '/home/<USER>/repos/OCR_in_house/data/truuth/test_samples_truuth.csv'"]}], "source": ["df_truuth.to_csv(TRUUTH_PATH, index=False)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNumber</th>\n", "      <th>CustomerId</th>\n", "      <th>ClaimPolicyNumber</th>\n", "      <th>CosReference</th>\n", "      <th>OCRDocumentConfidence</th>\n", "      <th>OCRResponseObject</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>*********</td>\n", "      <td>**********</td>\n", "      <td>C202505304503</td>\n", "      <td>0</td>\n", "      <td>{\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>*********</td>\n", "      <td>**********</td>\n", "      <td>C202505304508</td>\n", "      <td>0.92</td>\n", "      <td>{\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C8782000</td>\n", "      <td>CT0799314</td>\n", "      <td>RS09930722</td>\n", "      <td>C202505304519</td>\n", "      <td>0.41</td>\n", "      <td>{\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C8782327</td>\n", "      <td>CT1436555</td>\n", "      <td>RS17717800</td>\n", "      <td>C202505304845</td>\n", "      <td>0.52</td>\n", "      <td>{\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C8782368</td>\n", "      <td>CT1649734</td>\n", "      <td>RS21605452</td>\n", "      <td>C202505304888</td>\n", "      <td>0.44</td>\n", "      <td>{\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  ClaimNumber CustomerId ClaimPolicyNumber   CosReference  \\\n", "0    ********  *********        **********  C202505304503   \n", "1    ********  *********        **********  C202505304508   \n", "2    C8782000  CT0799314        RS09930722  C202505304519   \n", "3    C8782327  CT1436555        RS17717800  C202505304845   \n", "4    C8782368  CT1649734        RS21605452  C202505304888   \n", "\n", "  OCRDocumentConfidence                                  OCRResponseObject  \n", "0                     0  {\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...  \n", "1                  0.92  {\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...  \n", "2                  0.41  {\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...  \n", "3                  0.52  {\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...  \n", "4                  0.44  {\"VetXmlClaim\":{\"InfoFromPolicyHolder\":{\"Anima...  "]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["df_truuth.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Retrieve Source of Truth UPM database"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def load_upm_sot(df, engine):\n", "    if not df.empty:\n", "        claim_numbers = \"','\".join(\n", "            df[\"ClaimNo\"]\n", "            .astype(str)\n", "            .apply(lambda x: x.split(\"-\")[0])\n", "            .unique()\n", "            .tolist()\n", "        )\n", "\n", "        sql_query_policy = rf\"\"\"\n", "\n", "        SELECT\n", "            <PERSON><PERSON>,\n", "            t.<PERSON>vo<PERSON>,\n", "            <PERSON><PERSON>,\n", "            <PERSON><PERSON>,\n", "            ci.DateInvoice,\n", "            t.Date<PERSON>,\n", "            t.<PERSON>aw AS TreatmentDrugDescription,\n", "            t.<PERSON>nclVat,\n", "            SUM(t.AmountInclVat) OVER (PARTITION BY t.InvoiceNo) AS InvoiceAmount\n", "\n", "        FROM [PS-PRD-AZS-DWH1].[BIA].[model].[XML-ClaimTreatment] t\n", "            LEFT JOIN [BIA].[model].[XML-ClaimPhysical] p\n", "                ON t.ClaimNo = p.ClaimNo\n", "            LEFT JOIN [BIA].[dbo].[Ref_VetServiceProvider] s\n", "                ON p.ServiceProviderNo = s.Service<PERSON>rovider<PERSON>o\n", "            LEFT JOIN [BIA].[model].[XML-ClaimInvoice] ci\n", "                ON t.InvoiceNo = ci.InvoiceNo AND t.ClaimNo = ci.ClaimNo\n", "\n", "        WHERE t.C<PERSON>o\n", "                IN ('{claim_numbers}')\n", "        \"\"\"\n", "        df_sot = pd.read_sql(sql_query_policy, engine)\n", "\n", "    # Return both DataFrames\n", "    return df_sot"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["df_sot = load_upm_sot(samples_di, engine)"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"data": {"text/plain": ["383"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df_sot)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["117"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# Show SOT without invoice no and invoice date from XML\n", "len(df_sot[\"InvoiceNo\"].unique())"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["df_sot.to_excel(UPM_GROUND_TRUTH_PATH, index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run Document Intelligence"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["# import libraries \n", "from loguru import logger\n", "from azure.ai.formrecognizer import DocumentAnalysisClient\n", "from azure.core.credentials import AzureKeyCredential\n", "from azure.core.exceptions import HttpResponseError\n", "import glob\n", "import time\n", "\n", "# set `<your-endpoint>` and `<your-key>` variables with the values from the Azure portal\n", "endpoint = os.getenv(\"AZURE_DOCUMENT_INTELLIGENCE_ENDPONT\")\n", "key = os.getenv(\"AZURE_DOCUMENT_INTELLIGENCE_KEY\")\n", "document_analysis_client = DocumentAnalysisClient(\n", "        endpoint=endpoint, credential=AzureKeyCredential(key)\n", "    )"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [], "source": ["# Create output directory if it doesn't exist\n", "if not Path(OUTPUT_DATA_FOLDER).exists():\n", "    os.makedirs(OUTPUT_DATA_FOLDER)\n", "    \n", "MAX_RETRY = 3\n", "ans = []\n", "doc_path_list = sorted(os.listdir(DATA_FOLDER))"]}, {"cell_type": "code", "execution_count": 157, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-06-13 01:38:11.078\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 0983b113-2772-4a1d-bba6-3b8f823648c3.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:38:22.079\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 0983b113-2772-4a1d-bba6-3b8f823648c3.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:38:22.080\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 0ea11fff-e5bb-48b1-b34e-42f8feb7339a.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:38:32.675\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 0ea11fff-e5bb-48b1-b34e-42f8feb7339a.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:38:32.676\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 1062b12f-027c-44bd-b8d0-53d00d5f20cb.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:38:43.325\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 1062b12f-027c-44bd-b8d0-53d00d5f20cb.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:38:43.326\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 113ddc44-be51-4cae-9f59-d419dc9f61a6.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:38:53.686\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 113ddc44-be51-4cae-9f59-d419dc9f61a6.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:38:53.687\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 119ffa18-93ab-47e2-8c44-87bcbe4dd9b5.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:39:04.270\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 119ffa18-93ab-47e2-8c44-87bcbe4dd9b5.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:39:04.271\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 1374dd27-21f2-4aea-b317-eeaa5639b49b.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:39:14.592\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 1374dd27-21f2-4aea-b317-eeaa5639b49b.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:39:14.593\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 15155ac2-737c-4fd0-a896-823316c0b754.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:39:25.191\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 15155ac2-737c-4fd0-a896-823316c0b754.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:39:25.192\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 16a6ba20-9d94-4ae0-aa4e-a4972fa18cab.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:39:36.132\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 16a6ba20-9d94-4ae0-aa4e-a4972fa18cab.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:39:36.133\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 16c87632-8071-4110-aa04-81965fbe6670.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:39:46.647\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 16c87632-8071-4110-aa04-81965fbe6670.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:39:46.648\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 1b3def1d-4b23-4bd4-a94e-61d62bdc5c23.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:39:56.944\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 1b3def1d-4b23-4bd4-a94e-61d62bdc5c23.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:39:56.945\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 1c2502de-8e09-46af-ac2b-0244d7c4b129.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:40:07.493\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 1c2502de-8e09-46af-ac2b-0244d7c4b129.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:40:07.494\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 219df155-ae50-4cdc-8439-025d2154615e.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:40:18.166\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 219df155-ae50-4cdc-8439-025d2154615e.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:40:18.167\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 23136292-93ae-4215-bc52-068cb578379a.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:40:28.663\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 23136292-93ae-4215-bc52-068cb578379a.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:40:28.664\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 239e095a-6128-4b18-8675-c417a402e0c9.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:40:42.264\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 239e095a-6128-4b18-8675-c417a402e0c9.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:40:42.265\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 25714a32-883c-4fa2-bc07-81087ac14c73.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:40:52.751\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 25714a32-883c-4fa2-bc07-81087ac14c73.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:40:52.752\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 263bb02a-0a2d-456e-828d-baad0d413431.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:41:03.030\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 263bb02a-0a2d-456e-828d-baad0d413431.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:41:03.031\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2648ba8e-b09f-42be-9fa8-df7dd4ab23a2.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:41:13.349\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2648ba8e-b09f-42be-9fa8-df7dd4ab23a2.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:41:13.351\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 26695410-7ebe-4050-b310-5d4bbda59f42.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:41:23.915\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 26695410-7ebe-4050-b310-5d4bbda59f42.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:41:23.916\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2734ddd6-df57-452a-aba7-167c5574737b.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:41:34.301\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2734ddd6-df57-452a-aba7-167c5574737b.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:41:34.301\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2741d777-80a3-4181-b726-f656c71d3528.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:41:44.920\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2741d777-80a3-4181-b726-f656c71d3528.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:41:44.921\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 28a991aa-d832-45f3-9c81-f75cc0055008.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:41:55.249\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 28a991aa-d832-45f3-9c81-f75cc0055008.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:41:55.250\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 29477e5a-06c4-44b5-be2f-b6a71d646ff6.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:42:05.594\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 29477e5a-06c4-44b5-be2f-b6a71d646ff6.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:42:05.596\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 299afb8e-49f6-4abd-af9a-5342f3f61472.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:42:16.025\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 299afb8e-49f6-4abd-af9a-5342f3f61472.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:42:16.026\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 29d0199c-2a2a-47bf-889f-0202a41c5df2.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:42:26.696\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 29d0199c-2a2a-47bf-889f-0202a41c5df2.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:42:26.697\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2ae1e421-be3f-4a2a-b668-8ce542a8d9e0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:42:37.115\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2ae1e421-be3f-4a2a-b668-8ce542a8d9e0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:42:37.116\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2bb165c6-6ed1-47b2-912a-6ff482aa2a68.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:42:47.418\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2bb165c6-6ed1-47b2-912a-6ff482aa2a68.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:42:47.419\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2c84a716-09aa-41ae-9d3d-b1f040777c1e.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:42:57.891\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2c84a716-09aa-41ae-9d3d-b1f040777c1e.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:42:57.893\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2f2a772a-2ce6-4865-bdc0-299fe1ec47eb.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:43:08.587\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2f2a772a-2ce6-4865-bdc0-299fe1ec47eb.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:43:08.588\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2f2e5944-f6d3-4512-85dc-d69be503c632.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:43:18.888\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2f2e5944-f6d3-4512-85dc-d69be503c632.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:43:18.889\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 30467500-9f9e-47ff-b00a-d49acd7083d3.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:43:29.666\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 30467500-9f9e-47ff-b00a-d49acd7083d3.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:43:29.667\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 308073fb-7e2c-4270-a6c7-6547def20a2d.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:43:39.950\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 308073fb-7e2c-4270-a6c7-6547def20a2d.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:43:39.951\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 30b75c3d-062e-4ca1-96ef-e38c8604e886.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:43:50.406\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 30b75c3d-062e-4ca1-96ef-e38c8604e886.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:43:50.407\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 33531129-818c-47d8-977f-0820eb76d2e9.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:44:00.731\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 33531129-818c-47d8-977f-0820eb76d2e9.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:44:00.732\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 335dbabf-e1ec-41ab-90db-3a299b5bdc6e.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:44:11.028\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 335dbabf-e1ec-41ab-90db-3a299b5bdc6e.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:44:11.029\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 34193ed0-3c82-4c5c-a90f-d2a2e8a2ba29.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:44:21.374\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 34193ed0-3c82-4c5c-a90f-d2a2e8a2ba29.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:44:21.375\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 34fb275e-1faa-4e3d-bb49-edfcd006ddcf.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:44:31.760\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 34fb275e-1faa-4e3d-bb49-edfcd006ddcf.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:44:31.761\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 35795076-115e-40be-87b8-fcb61376f29d.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:44:42.088\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 35795076-115e-40be-87b8-fcb61376f29d.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:44:42.088\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 360e641e-9e01-4b6c-81fa-7e2388fa66c5.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:44:52.403\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 360e641e-9e01-4b6c-81fa-7e2388fa66c5.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:44:52.405\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 36f6e764-7e8d-4ac8-9e8b-bb07ed1506e9.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:45:02.905\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 36f6e764-7e8d-4ac8-9e8b-bb07ed1506e9.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:45:02.906\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 3940785d-266f-403d-912f-ed816c76bc73.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:45:13.306\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 3940785d-266f-403d-912f-ed816c76bc73.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:45:13.307\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 3a91c57a-ed49-479a-8472-b0f0ff3e3adb.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:45:23.680\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 3a91c57a-ed49-479a-8472-b0f0ff3e3adb.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:45:23.681\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 3a9518c8-ce07-474d-8d5b-ee55b196cc9b.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:45:34.291\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 3a9518c8-ce07-474d-8d5b-ee55b196cc9b.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:45:34.292\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 3c1d9906-788a-48c0-a346-e89110928f8d.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:45:44.645\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 3c1d9906-788a-48c0-a346-e89110928f8d.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:45:44.646\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 3dbceb2b-88c6-4ae9-b040-941a94c74ce0.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:45:55.070\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 3dbceb2b-88c6-4ae9-b040-941a94c74ce0.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:45:55.071\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 3f5d87b9-aa25-40b5-9dcd-56d160d5cb30.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:46:05.394\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 3f5d87b9-aa25-40b5-9dcd-56d160d5cb30.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:46:05.395\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 41f46159-bfd0-449f-8907-5f2133117fe8.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:46:18.727\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 41f46159-bfd0-449f-8907-5f2133117fe8.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:46:18.728\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 42d81376-1947-4da8-af88-1232ba21d49a.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:46:29.113\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 42d81376-1947-4da8-af88-1232ba21d49a.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:46:29.114\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 47439fba-95e7-4fcb-9671-1c8952b9729f.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:46:39.670\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 47439fba-95e7-4fcb-9671-1c8952b9729f.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:46:39.672\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 482ca2ba-f597-4403-aecc-06fc22480999.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:46:50.015\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 482ca2ba-f597-4403-aecc-06fc22480999.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:46:50.016\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4a1fc5f6-133c-4dee-95b2-344c5d5cd27a.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:47:00.708\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4a1fc5f6-133c-4dee-95b2-344c5d5cd27a.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:47:00.709\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4b330b52-6c0d-4d0c-8397-ba10d407c24b.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:47:11.031\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4b330b52-6c0d-4d0c-8397-ba10d407c24b.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:47:11.032\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4bda80bc-80e8-4321-87ce-c96f1f78f412.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:47:23.490\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4bda80bc-80e8-4321-87ce-c96f1f78f412.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:47:23.491\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4f402f17-5f37-475b-be4a-40d67710ed93.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:47:34.450\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4f402f17-5f37-475b-be4a-40d67710ed93.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:47:34.451\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 50813bb1-80cb-4ef4-8132-f25b97b0a875.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:47:44.835\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 50813bb1-80cb-4ef4-8132-f25b97b0a875.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:47:44.836\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 508c5dfc-94b5-419e-b60d-dae495437f4a.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:47:55.113\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 508c5dfc-94b5-419e-b60d-dae495437f4a.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:47:55.114\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 523c2af3-006a-4cb7-b620-ec4928211e8d.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:48:05.399\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 523c2af3-006a-4cb7-b620-ec4928211e8d.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:48:05.400\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 599e2a45-ccd2-4116-a648-8376191fef83.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:48:15.738\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 599e2a45-ccd2-4116-a648-8376191fef83.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:48:15.739\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 5cbfccd2-a555-40c9-9fd9-08bbc48e7a0d.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:48:26.096\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 5cbfccd2-a555-40c9-9fd9-08bbc48e7a0d.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:48:26.097\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 5cf82189-efc0-400e-b369-c4c71e7379dc.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:48:36.390\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 5cf82189-efc0-400e-b369-c4c71e7379dc.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:48:36.391\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 623b3d0d-66ae-4566-879c-740717699b58.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:48:46.685\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 623b3d0d-66ae-4566-879c-740717699b58.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:48:46.686\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 685f8482-ea8e-428c-9810-97f9c5f39b47.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:48:57.083\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 685f8482-ea8e-428c-9810-97f9c5f39b47.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:48:57.084\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 68c2f8b4-b5ce-4d2c-8ae7-c88dd593da1b.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:49:07.354\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 68c2f8b4-b5ce-4d2c-8ae7-c88dd593da1b.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:49:07.354\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6abb489a-3bd3-496a-9422-40c46de85121.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:49:17.648\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6abb489a-3bd3-496a-9422-40c46de85121.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:49:17.649\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6b00462f-730c-4afd-be7c-fe5dfcde4f9e.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:49:28.064\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6b00462f-730c-4afd-be7c-fe5dfcde4f9e.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:49:28.065\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6b034de0-ad17-49ae-8e88-d5c94e125c9a.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:49:38.379\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6b034de0-ad17-49ae-8e88-d5c94e125c9a.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:49:38.380\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6d2c7e87-698b-4126-b51e-9ecee0f0e3d3.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:49:48.636\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6d2c7e87-698b-4126-b51e-9ecee0f0e3d3.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:49:48.637\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6dd8a312-97a4-424f-8f94-2e56217c68a5.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:49:59.205\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6dd8a312-97a4-424f-8f94-2e56217c68a5.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:49:59.206\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6e28f1ac-2f8a-450d-8e1b-28086baf8eee.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:50:09.593\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6e28f1ac-2f8a-450d-8e1b-28086baf8eee.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:50:09.593\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6e6abe9f-600a-4338-a34f-4d27c8d32a9d.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:50:20.490\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6e6abe9f-600a-4338-a34f-4d27c8d32a9d.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:50:20.491\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 715fed9d-16b6-4b1a-a856-acca01dc53e8.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:50:34.479\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 715fed9d-16b6-4b1a-a856-acca01dc53e8.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:50:34.481\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 726fbffa-d298-45b6-bbd4-9c10d5f73d22.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:50:47.364\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 726fbffa-d298-45b6-bbd4-9c10d5f73d22.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:50:47.365\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 72ff5e9b-a84a-428c-bdcb-15fc3fd5bdf3.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:50:58.071\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 72ff5e9b-a84a-428c-bdcb-15fc3fd5bdf3.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:50:58.072\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 73585170-d997-4b5c-b6f7-d01b8cfa4b91.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:51:08.663\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 73585170-d997-4b5c-b6f7-d01b8cfa4b91.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:51:08.664\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 7623da79-c45b-4399-9c77-36d6520906c4.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:51:19.030\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 7623da79-c45b-4399-9c77-36d6520906c4.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:51:19.031\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 76892c01-5c93-4797-8dd8-b1b7dba47448.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:51:29.881\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 76892c01-5c93-4797-8dd8-b1b7dba47448.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:51:29.882\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 76bbf9e7-be45-4a65-8d45-0d3be2f0bfba.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:51:42.738\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 76bbf9e7-be45-4a65-8d45-0d3be2f0bfba.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:51:42.739\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 76e9b3e5-2caf-409c-a14b-6c47a684cf76.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:51:53.049\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 76e9b3e5-2caf-409c-a14b-6c47a684cf76.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:51:53.050\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 7846deae-b381-40bd-90da-136a2531ab6c.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:52:03.740\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 7846deae-b381-40bd-90da-136a2531ab6c.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:52:03.741\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 7932828b-60c7-41f0-8f94-186c9fff6a09.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:52:13.998\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 7932828b-60c7-41f0-8f94-186c9fff6a09.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:52:13.999\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 7b9560d5-a2d4-48ce-86c6-ff5f60ec083a.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:52:24.294\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 7b9560d5-a2d4-48ce-86c6-ff5f60ec083a.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:52:24.294\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 7c1d1509-c3dd-4813-9a05-30e3fc96f9f0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:52:34.613\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 7c1d1509-c3dd-4813-9a05-30e3fc96f9f0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:52:34.614\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 7dda6eb4-9562-40dc-9cb5-d92cb6c6bfc6.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:52:49.101\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 7dda6eb4-9562-40dc-9cb5-d92cb6c6bfc6.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:52:49.102\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 7fac6559-015d-4e04-9b56-b8ce657d6dcb.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:53:11.336\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 7fac6559-015d-4e04-9b56-b8ce657d6dcb.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:53:11.337\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 868bcdd8-23e7-457c-b2c8-402c64c203d9.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:53:21.884\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 868bcdd8-23e7-457c-b2c8-402c64c203d9.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:53:21.884\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 86b0491a-8b9b-4af5-8cc2-d2a9bf1a0db8.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:53:32.172\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 86b0491a-8b9b-4af5-8cc2-d2a9bf1a0db8.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:53:32.172\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 86d7dc94-cb69-4dbe-8829-01cbf179dfb7.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:53:42.717\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 86d7dc94-cb69-4dbe-8829-01cbf179dfb7.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:53:42.718\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 87198a9d-a601-4604-aef5-eb6be1c9a515.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:53:53.177\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 87198a9d-a601-4604-aef5-eb6be1c9a515.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:53:53.178\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 8752e222-7cba-4e4d-a4d1-39dee844e767.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:54:03.479\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 8752e222-7cba-4e4d-a4d1-39dee844e767.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:54:03.479\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 87ad059f-d90e-44cd-ba64-8e870c4934c8.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:54:13.941\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 87ad059f-d90e-44cd-ba64-8e870c4934c8.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:54:13.942\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 8b5d0177-e192-4b1e-92f0-f2e85e0b4724.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:54:24.423\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 8b5d0177-e192-4b1e-92f0-f2e85e0b4724.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:54:24.424\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 8e0b3a6e-df27-4995-b423-5a5123842a33.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:54:34.739\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 8e0b3a6e-df27-4995-b423-5a5123842a33.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:54:34.741\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 8f296e17-06f7-4351-a7f9-bed18ed51edf.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:54:46.137\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 8f296e17-06f7-4351-a7f9-bed18ed51edf.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:54:46.138\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 8f59457b-86ed-4e8d-8b86-a1df0c16234d.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:54:56.471\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 8f59457b-86ed-4e8d-8b86-a1df0c16234d.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:54:56.472\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 914a8b18-1f55-454a-a06e-d340baa13e69.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:55:06.786\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 914a8b18-1f55-454a-a06e-d340baa13e69.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:55:06.788\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 922243f2-e399-4d97-8f44-9c6dcaba7def.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:55:17.089\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 922243f2-e399-4d97-8f44-9c6dcaba7def.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:55:17.091\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 9325df8e-5f69-47c8-84a7-23fc2e3410c3.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:55:27.510\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 9325df8e-5f69-47c8-84a7-23fc2e3410c3.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:55:27.511\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 9342db1b-d51d-44ab-b420-b2d4f7a84408.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:55:37.803\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 9342db1b-d51d-44ab-b420-b2d4f7a84408.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:55:37.804\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 991a343b-db42-48f9-a2c6-4733bc00818b.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:55:48.595\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 991a343b-db42-48f9-a2c6-4733bc00818b.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:55:48.596\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 9a064089-5975-46a1-9bf8-e9b9fcbf9c6d.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:55:59.375\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 9a064089-5975-46a1-9bf8-e9b9fcbf9c6d.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:55:59.376\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 9abe7d7c-e4cf-4ffb-bd08-84acab4e4bea.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:56:09.803\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 9abe7d7c-e4cf-4ffb-bd08-84acab4e4bea.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:56:09.804\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 9c0fb666-46e3-4269-8817-12446b82a0db.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:56:20.088\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 9c0fb666-46e3-4269-8817-12446b82a0db.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:56:20.089\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 9eb8cc06-4199-4a1a-a09f-cbb4d246129b.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:56:30.407\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 9eb8cc06-4199-4a1a-a09f-cbb4d246129b.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:56:30.408\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 9f6ee82c-4f37-47e7-abd9-9b6917b30e26.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:56:41.127\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 9f6ee82c-4f37-47e7-abd9-9b6917b30e26.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:56:41.128\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a19638d6-4882-4d5a-a377-174d1476c19c.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:56:53.800\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a19638d6-4882-4d5a-a377-174d1476c19c.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:56:53.801\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a3cf32cb-be9f-4a8a-b1e4-01d78d98a06b.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:57:07.360\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a3cf32cb-be9f-4a8a-b1e4-01d78d98a06b.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:57:07.361\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a4689e65-395b-4d64-a51f-10a122ed16ff.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:57:20.889\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a4689e65-395b-4d64-a51f-10a122ed16ff.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:57:20.890\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a4bc2b00-e36c-486b-911e-e6062da64381.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:57:31.274\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a4bc2b00-e36c-486b-911e-e6062da64381.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:57:31.275\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a6716664-5475-415f-8c2e-ad7fd7650931.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:57:41.621\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a6716664-5475-415f-8c2e-ad7fd7650931.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:57:41.622\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a9098ada-63c9-48d9-8fcc-a219182caaf5.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:57:51.937\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a9098ada-63c9-48d9-8fcc-a219182caaf5.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:57:51.938\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing aa052f34-10a1-47f3-9cc2-4e02c58d1e75.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:58:08.795\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile aa052f34-10a1-47f3-9cc2-4e02c58d1e75.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:58:08.796\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing aa0b7afe-c750-4236-9bd4-ddd504f6a830.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:58:19.200\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile aa0b7afe-c750-4236-9bd4-ddd504f6a830.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:58:19.201\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ab059fda-2515-4e84-9084-e09e9e0d177e.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:58:30.093\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ab059fda-2515-4e84-9084-e09e9e0d177e.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:58:30.094\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing aeea5fc0-4283-455c-b74e-c3fef9f1b417.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:58:40.345\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile aeea5fc0-4283-455c-b74e-c3fef9f1b417.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:58:40.346\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing afe4dbeb-1aec-4cc8-b117-df2579fa5e29.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:58:50.701\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile afe4dbeb-1aec-4cc8-b117-df2579fa5e29.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:58:50.701\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b0d37f88-15f6-4188-966e-e8cfcfcd8614.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:59:01.199\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b0d37f88-15f6-4188-966e-e8cfcfcd8614.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:59:01.200\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b1fa9de9-d1d2-46af-b5f0-575e38e9d78f.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:59:11.806\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b1fa9de9-d1d2-46af-b5f0-575e38e9d78f.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:59:11.807\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b2ea7e5b-2e5e-4457-9845-05a65248bc68.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:59:22.096\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b2ea7e5b-2e5e-4457-9845-05a65248bc68.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:59:22.097\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b3f4eb21-ed71-41f5-bc89-5ed37ed9e0d2.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:59:32.387\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b3f4eb21-ed71-41f5-bc89-5ed37ed9e0d2.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:59:32.388\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b4b0384c-8a19-4301-a95e-8d3dd99634a8.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:59:42.792\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b4b0384c-8a19-4301-a95e-8d3dd99634a8.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:59:42.793\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b502d068-d3bb-49ca-b6f8-c62d3b913f67.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 01:59:53.126\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b502d068-d3bb-49ca-b6f8-c62d3b913f67.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 01:59:53.127\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b5684ab8-df5b-4fc3-b42f-2ce146143673.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:00:03.402\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b5684ab8-df5b-4fc3-b42f-2ce146143673.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:00:03.403\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b85f98b7-f03c-4e54-ba77-165aee5c7aa3.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:00:13.980\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b85f98b7-f03c-4e54-ba77-165aee5c7aa3.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:00:13.981\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b8739994-e458-4c38-a2a0-af3eb28f2f3d.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:00:24.727\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b8739994-e458-4c38-a2a0-af3eb28f2f3d.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:00:24.727\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b9af9fad-4d1b-488b-8806-2249f42b135b.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:00:35.361\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b9af9fad-4d1b-488b-8806-2249f42b135b.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:00:35.362\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing baf45e1d-544d-4279-845e-b7a1c2483b22.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:00:45.999\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile baf45e1d-544d-4279-845e-b7a1c2483b22.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:00:46.000\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing bd073637-f5c2-4b28-8fec-e82e4cab6d75.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:00:56.337\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile bd073637-f5c2-4b28-8fec-e82e4cab6d75.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:00:56.338\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing bebbf2fe-fd7f-4f85-83b2-5a0ff90602a6.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:01:06.789\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile bebbf2fe-fd7f-4f85-83b2-5a0ff90602a6.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:01:06.790\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing bf08f732-cba7-4194-8bae-399110de3bcb.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:01:17.071\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile bf08f732-cba7-4194-8bae-399110de3bcb.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:01:17.072\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing bf57177b-5958-43f7-9907-de3d5a8ac198.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:01:29.613\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile bf57177b-5958-43f7-9907-de3d5a8ac198.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:01:29.614\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c0686456-4df6-4971-acae-5967d5fa03c6.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:01:39.900\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c0686456-4df6-4971-acae-5967d5fa03c6.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:01:39.901\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c1d83450-f140-4ab9-b450-4254fe4ac73c.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:01:50.221\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c1d83450-f140-4ab9-b450-4254fe4ac73c.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:01:50.221\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c36f4dbb-6965-40d4-9ab0-fd3d84335aca.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:02:05.804\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c36f4dbb-6965-40d4-9ab0-fd3d84335aca.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:02:05.805\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c3bea3ed-9021-4c74-a8cc-6e8c920382d7.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:02:16.153\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c3bea3ed-9021-4c74-a8cc-6e8c920382d7.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:02:16.154\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c4407416-beaf-4995-b623-9188e09f41f6.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:02:26.482\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c4407416-beaf-4995-b623-9188e09f41f6.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:02:26.483\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c4567aa5-c640-4871-bf5a-713a47f50dcd.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:02:37.089\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c4567aa5-c640-4871-bf5a-713a47f50dcd.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:02:37.090\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c47ba1b3-18eb-41c0-b6b4-534c2072ed0a.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:02:47.377\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c47ba1b3-18eb-41c0-b6b4-534c2072ed0a.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:02:47.378\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c597e074-e15a-4a27-aca9-cce9418a8a9b.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:02:57.975\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c597e074-e15a-4a27-aca9-cce9418a8a9b.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:02:57.976\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c59cecec-6f03-4848-a4f8-2d96b8d2c571.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:03:08.844\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c59cecec-6f03-4848-a4f8-2d96b8d2c571.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:03:08.845\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c5dc26b3-296b-4263-aa6a-46d2d957cab1.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:03:19.459\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c5dc26b3-296b-4263-aa6a-46d2d957cab1.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:03:19.461\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c67270af-20ac-49d6-9e75-e15493f3fe40.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:03:29.780\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c67270af-20ac-49d6-9e75-e15493f3fe40.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:03:29.781\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c6ea18d2-8dba-4de1-be00-4444b7263642.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:03:40.104\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c6ea18d2-8dba-4de1-be00-4444b7263642.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:03:40.105\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c73e443c-685f-4631-b778-6b7c9ecb1787.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:03:50.547\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c73e443c-685f-4631-b778-6b7c9ecb1787.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:03:50.548\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c9febf04-986b-407d-b604-7aef06f91f0b.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:04:00.874\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c9febf04-986b-407d-b604-7aef06f91f0b.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:04:00.875\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ca85a248-2ceb-4c8c-9e49-7c33ec8c1185.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:04:11.163\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ca85a248-2ceb-4c8c-9e49-7c33ec8c1185.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:04:11.164\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing cad26cd8-a44f-4ee3-b0bd-457b192bd80c.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:04:21.523\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile cad26cd8-a44f-4ee3-b0bd-457b192bd80c.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:04:21.524\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing cb0f60bc-3a19-4d3a-999f-7fe53d71f3d4.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:04:34.206\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile cb0f60bc-3a19-4d3a-999f-7fe53d71f3d4.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:04:34.212\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing cc51d137-9fe8-41e2-88e7-5f701e34cea4.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:04:46.616\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile cc51d137-9fe8-41e2-88e7-5f701e34cea4.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:04:46.617\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing cc9e791a-db3b-4ed0-a539-d7e7e81e004a.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:05:00.304\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile cc9e791a-db3b-4ed0-a539-d7e7e81e004a.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:05:00.306\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing cda22c8e-2441-442f-b74e-82dff4b4f9e4.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:05:11.043\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile cda22c8e-2441-442f-b74e-82dff4b4f9e4.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:05:11.045\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ce6a3a36-ce49-4e91-a977-48a51be5ad5a.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:05:26.978\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ce6a3a36-ce49-4e91-a977-48a51be5ad5a.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:05:26.978\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing cf4377f2-82bf-44ed-b7cf-8e19ee0e3738.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:05:37.279\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile cf4377f2-82bf-44ed-b7cf-8e19ee0e3738.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:05:37.280\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d0033a21-fcc6-446d-a4b9-1928ed5f53cf.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:05:48.305\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d0033a21-fcc6-446d-a4b9-1928ed5f53cf.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:05:48.305\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d22374c1-c6cc-47f2-bc8b-4e5ad6a47d56.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:05:58.599\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d22374c1-c6cc-47f2-bc8b-4e5ad6a47d56.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:05:58.600\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d3af8200-7f9a-4ae6-89f6-26869aeb9214.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:06:09.313\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d3af8200-7f9a-4ae6-89f6-26869aeb9214.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:06:09.315\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d7463aa5-be0f-4cb1-9a06-62831a7f285d.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:06:19.804\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d7463aa5-be0f-4cb1-9a06-62831a7f285d.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:06:19.805\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d9255a09-253e-4662-b10d-412056ca630f.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:06:30.458\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d9255a09-253e-4662-b10d-412056ca630f.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:06:30.459\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d9300c44-fa8b-47a3-95ee-3217f7467adb.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:06:40.758\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d9300c44-fa8b-47a3-95ee-3217f7467adb.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:06:40.760\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e6c530e2-440b-4c7f-af60-8d38dd0f6cc4.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:06:51.083\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e6c530e2-440b-4c7f-af60-8d38dd0f6cc4.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:06:51.084\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e80da5b0-91e3-458f-ae77-f733376c7d2c.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:07:01.409\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e80da5b0-91e3-458f-ae77-f733376c7d2c.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:07:01.410\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e867af8b-0419-48bd-a291-a45d1e7f3c3c.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:07:11.706\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e867af8b-0419-48bd-a291-a45d1e7f3c3c.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:07:11.707\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e939ca03-16f2-4f77-ae34-f92d4bc803c0.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:07:22.203\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e939ca03-16f2-4f77-ae34-f92d4bc803c0.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:07:22.204\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e95db2b4-5171-426b-816b-af605b572ebb.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:07:32.630\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e95db2b4-5171-426b-816b-af605b572ebb.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:07:32.631\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e9ae7ed7-6c0b-45d1-a30c-0456a18b803c.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:07:43.059\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e9ae7ed7-6c0b-45d1-a30c-0456a18b803c.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:07:43.060\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e9bdf3b4-ab35-46c4-bfc7-5527500dacc3.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:07:57.838\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e9bdf3b4-ab35-46c4-bfc7-5527500dacc3.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:07:57.839\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing eb1d2e96-79cc-40f8-b3df-e8e9f9c5e728.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:08:11.359\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile eb1d2e96-79cc-40f8-b3df-e8e9f9c5e728.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:08:11.360\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ed2f4c7e-70a2-45b3-9d5a-5b541878d536.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:08:21.715\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ed2f4c7e-70a2-45b3-9d5a-5b541878d536.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:08:21.716\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ed36000f-0956-4a6e-a088-e8fb6b4766f0.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:08:32.228\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ed36000f-0956-4a6e-a088-e8fb6b4766f0.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:08:32.228\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ed3a4869-61d6-4f07-b09e-1cb1191534f6.png... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:08:42.698\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ed3a4869-61d6-4f07-b09e-1cb1191534f6.png processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:08:42.699\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing eddedc60-176b-4775-bbea-bb8ff98001c1.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:08:53.421\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile eddedc60-176b-4775-bbea-bb8ff98001c1.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:08:53.422\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing edef0a6c-a92c-454d-b7fc-bddb9395e29d.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:09:03.988\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile edef0a6c-a92c-454d-b7fc-bddb9395e29d.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:09:03.989\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing edf82e98-e810-46ac-bd12-dc9e92a2ebf9.PDF... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:09:14.414\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile edf82e98-e810-46ac-bd12-dc9e92a2ebf9.PDF processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:09:14.415\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ef85edc9-3738-46e0-9b78-09fdc1aa8cee.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:09:24.711\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ef85edc9-3738-46e0-9b78-09fdc1aa8cee.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:09:24.712\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f02ba235-8042-49b9-ba6a-c2876d5fe059.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:09:35.233\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f02ba235-8042-49b9-ba6a-c2876d5fe059.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:09:35.234\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f27d5f3c-81c5-47fd-a75e-eda1b0ed0e0c.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:09:45.836\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f27d5f3c-81c5-47fd-a75e-eda1b0ed0e0c.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:09:45.837\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f2817608-6d63-4d12-8552-6c406e462b27.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:09:59.183\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f2817608-6d63-4d12-8552-6c406e462b27.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:09:59.184\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f3a7e9ba-4224-4c29-927d-75bfbac16d39.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:10:09.856\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f3a7e9ba-4224-4c29-927d-75bfbac16d39.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:10:09.857\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f403e62a-75d7-450a-bdfd-7ffbc74bdd1f.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:10:20.170\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f403e62a-75d7-450a-bdfd-7ffbc74bdd1f.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:10:20.171\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f523ee1b-16cb-425d-9324-2d65140125cd.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:10:30.765\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f523ee1b-16cb-425d-9324-2d65140125cd.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:10:30.766\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f57c179b-a916-4338-9eb3-39135222a8bd.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:10:41.109\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f57c179b-a916-4338-9eb3-39135222a8bd.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:10:41.110\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f5bfa1e1-22ca-4d6e-894b-15c4cfac5217.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:10:51.430\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f5bfa1e1-22ca-4d6e-894b-15c4cfac5217.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:10:51.432\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f76a33b0-dbb3-40bb-b92b-273c8c5729a7.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:11:02.074\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f76a33b0-dbb3-40bb-b92b-273c8c5729a7.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:11:02.076\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f7f8b054-6b09-46a8-8116-d631d1299da5.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:11:12.487\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f7f8b054-6b09-46a8-8116-d631d1299da5.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:11:12.488\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing fa9cd670-c0b5-429f-af9b-70b4404d13ba.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:11:22.744\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile fa9cd670-c0b5-429f-af9b-70b4404d13ba.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:11:22.745\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing fb9933ad-9ce4-4dda-9155-dbce08d4be92.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:11:33.284\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile fb9933ad-9ce4-4dda-9155-dbce08d4be92.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:11:33.285\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing fd33bfa0-deab-4e7a-b4fb-0ef1ad01d672.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:11:46.755\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile fd33bfa0-deab-4e7a-b4fb-0ef1ad01d672.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2025-06-13 02:11:46.756\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing fef5b661-05f2-44f4-bfd4-c50cbdf2364e.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2025-06-13 02:11:57.049\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile fef5b661-05f2-44f4-bfd4-c50cbdf2364e.pdf processed SUCCESS\u001b[0m\n"]}], "source": ["import json\n", "import pickle as pk\n", "\n", "for file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\n", "    retry_num = 0\n", "\n", "    file_stem = str(Path(file_path).stem)\n", "    while retry_num < MAX_RETRY:\n", "        logger.info(f\"Precessing {file_path}... Retried {retry_num} times\")\n", "        try:\n", "            with open(os.path.join(DATA_FOLDER, file_path), \"rb\") as f:\n", "                poller = document_analysis_client.begin_analyze_document(model_id=\"prebuilt-invoice\", document=f)\n", "                invoices = poller.result()\n", "                invoice_dict = invoices.to_dict()\n", "                ans.append(\n", "                    {\n", "                        \"file_path\": file_path,\n", "                        \"invoice\": invoice_dict,\n", "                        }\n", "                    )\n", "                time.sleep(5)\n", "\n", "                # dumpt to pk file\n", "                with open(os.path.join(OUTPUT_DATA_FOLDER, f\"{file_stem}.pk\"), \"wb\") as fout:\n", "                    pk.dump(invoice_dict, fout)\n", "                # dumpt to json file\n", "                with open(os.path.join(OUTPUT_DATA_FOLDER, f\"{file_stem}.json\"), \"w\") as fout:\n", "                    json.dump(invoice_dict, fout, indent=4, default=str)\n", "\n", "                break\n", "        except HttpResponseError as hre:\n", "            logger.exception(hre)\n", "            retry_num += 1\n", "            time.sleep(5)\n", "        except Exception as e:\n", "            logger.exception(e)\n", "            break\n", "\n", "    if ans and ans[-1][\"file_path\"] == file_path:\n", "        # process succeed\n", "        logger.info(f\"file {file_path} processed SUCCESS\")\n", "    else:\n", "        # process failed\n", "        logger.exception(f\"file {file_path} processed FAIL\")"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [], "source": ["# dumpt to pk file\n", "with open(os.path.join(OUTPUT_DATA_FOLDER, \"ans.pk\"), \"wb\") as fout:\n", "    pk.dump(ans, fout)\n", "# dumpt to json file\n", "with open(os.path.join(OUTPUT_DATA_FOLDER, \"ans.json\"), \"w\") as fout:\n", "    json.dump(ans, fout, indent=4, default=str)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# BIRI-9692 Business Rule Testing AutoRun\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading\n", "* load data in per file format or gathered running results format for DI result\n", "* load data of UPM data\n", "* load data of Truuth stat\n", "* load PaddleOCR results. Create one if not exists"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["# package import\n", "import json\n", "import pickle as pk\n", "import os\n", "import glob\n", "import time\n", "from dotenv import load_dotenv\n", "from loguru import logger\n", "import pandas as pd\n", "from pathlib import Path\n", "from typing import List, Dict, Tuple, Optional, Union\n", "from tqdm import tqdm\n", "import fitz  # PyMuPDF\n", "from paddleocr import PaddleOCR, draw_ocr\n", "from fitz import FileDataError\n", "\n", "\n", "load_dotenv()\n", "\n", "pd.set_option(\"display.max_columns\", None)\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["# load Azure Document Intelligence results from OUTPUT_DATA_FOLDER\n", "def load_document_intelligence_res(res_data_folder: str = OUTPUT_DATA_FOLDER, raw_data_folder: str = DATA_FOLDER) -> List[Dict]:\n", "    try:\n", "        with open(os.path.join(res_data_folder, \"ans.pk\"), \"rb\") as fin:\n", "            ans = pk.load(fin)\n", "    except FileNotFoundError:\n", "        original_file_path_list = sorted(os.listdir(raw_data_folder))\n", "        original_file_path_dict = {}\n", "        for p in original_file_path_list:\n", "            original_file_path_dict[str(Path(p).stem)] = p\n", "        ans = []\n", "        for file_path in sorted(os.listdir(res_data_folder)):\n", "            if file_path.endswith(\".json\"):\n", "                with open(os.path.join(res_data_folder, file_path), \"r\") as fin:\n", "                    ans.append(\n", "                        {\n", "                            \"file_path\": original_file_path_dict[str(Path(file_path).stem)],\n", "                            \"invoice\": json.load(fin),\n", "                            }\n", "                    )\n", "    return ans"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["186"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["document_intelligence_res = load_document_intelligence_res(OUTPUT_DATA_FOLDER)\n", "len(document_intelligence_res)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# CHANGELOG: 04 NOV 2024 add function to verify whether the content contains numbers and puncs only\n", "import string\n", "def is_numbers_and_punctuation(s):\n", "    allowed_chars = set(string.digits + string.punctuation + \" \")\n", "    return all(char in allowed_chars for char in s)\n", "\n", "# parse Azure Document Intelligence results from ans\n", "def parse_document_intelligence_res(data_info: List[Dict] | Dict) -> Dict:\n", "    if isinstance(data_info, Dict):\n", "        data_info = [data_info]\n", "\n", "    assert isinstance(data_info, List) and all(isinstance(data, Dict) for data in data_info)\n", "\n", "    ans = {}\n", "    for data in tqdm(data_info):\n", "        file_path = data[\"file_path\"]\n", "        invoice = data[\"invoice\"]\n", "        content = invoice[\"content\"]\n", "        invoice_info = []\n", "        for document in invoice[\"documents\"]:\n", "            service_provider = document[\"fields\"].get(\"VendorName\", {}).get(\"value\", \"\") + \" \" + document[\"fields\"].get(\"VendorAddressRecipient\", {}).get(\"value\", \"\")\n", "            service_provider = service_provider.strip()\n", "            service_provider_conf = 0.\n", "            service_provider_count = int(document[\"fields\"].get(\"VendorName\", {}).get(\"value\", \"\") != \"\") + int(document[\"fields\"].get(\"VendorAddressRecipient\", {}).get(\"value\", \"\") != \"\")\n", "            service_provider_conf = (document[\"fields\"].get(\"VendorName\", {}).get(\"confidence\", 0.) or 0.) + (document[\"fields\"].get(\"VendorAddressRecipient\", {}).get(\"confidence\", 0.) or 0.)\n", "            if service_provider_conf > 0.:\n", "                service_provider_conf /= service_provider_count\n", "            # CHANGELOG: 04 NOV 2024 add service provider address extraction for service provider field fuzzy matching preparation\n", "            service_provider_address = \"\"\n", "            service_provider_address_value = document[\"fields\"].get(\"VendorAddress\", {}).get(\"value\", {})\n", "            service_provider_address_exist = service_provider_address_value.get(\"street_address\", \"\") and (service_provider_address_value.get(\"postal_code\", \"\") or service_provider_address_value.get(\"suburb\", \"\") or service_provider_address_value.get(\"city\", \"\")) \n", "            service_provider_address_content = document[\"fields\"].get(\"VendorAddress\", {}).get(\"content\", \"\").replace(\"\\t\", \" \").replace(\"\\n\", \" \")\n", "            if service_provider_address_exist:\n", "                service_provider_address = service_provider_address_content\n", "\n", "\n", "            invoice_no = document[\"fields\"].get(\"InvoiceId\", {}).get(\"value\", \"\")\n", "            invoice_date = document[\"fields\"].get(\"InvoiceDate\", {}).get(\"value\", \"\") or \"\"\n", "            if not isinstance(invoice_date, str):\n", "                invoice_date = invoice_date.isoformat()\n", "            invoice_total_dict = document[\"fields\"].get(\"InvoiceTotal\", {}).get(\"value\", {}) or {}\n", "            invoice_total = invoice_total_dict.get(\"amount\", -1)\n", "\n", "            invoice_no_conf = document[\"fields\"].get(\"InvoiceId\", {}).get(\"confidence\", 0.) or 0.\n", "            invoice_date_conf = document[\"fields\"].get(\"InvoiceDate\", {}).get(\"confidence\", 0.) or 0.\n", "            invoice_total_conf = document[\"fields\"].get(\"InvoiceTotal\", {}).get(\"confidence\", 0.) or 0.\n", "\n", "\n", "            treatments = []\n", "            cur_treatment_date = invoice_date\n", "            cur_treatment_date_conf = invoice_date_conf\n", "\n", "            for item in document[\"fields\"].get(\"Items\", {}).get(\"value\", []):\n", "                item_conf = item.get(\"confidence\", 0.) or 0.\n", "\n", "                treatment_date = item.get(\"value\", {}).get(\"Date\", {}).get(\"value\", cur_treatment_date) or \"\"\n", "                if not isinstance(treatment_date, str):\n", "                    treatment_date = treatment_date.isoformat()\n", "                treatment_date_conf = item.get(\"value\", {}).get(\"Date\", {}).get(\"confidence\", cur_treatment_date_conf)\n", "                if treatment_date_conf is None:\n", "                    treatment_date_conf = item_conf\n", "\n", "                if not treatment_date:\n", "                    treatment_date = cur_treatment_date\n", "                    treatment_date_conf = cur_treatment_date_conf\n", "                cur_treatment_date = treatment_date\n", "                cur_treatment_date_conf = treatment_date_conf\n", "\n", "                desc = item.get(\"value\", {}).get(\"Description\", {}).get(\"content\", \"\")\n", "                product = item.get(\"value\", {}).get(\"ProductCode\", {}).get(\"content\", \"\")\n", "                # CHANGELOG: 04 NOV 2024 ignore product if it only contains numbers and puncs.\n", "                if is_numbers_and_punctuation(product.strip()):\n", "                     product = \"\"\n", "                desc_conf = item.get(\"value\", {}).get(\"Description\", {}).get(\"confidence\", item_conf) or item_conf\n", "                product_conf = item.get(\"value\", {}).get(\"ProductCode\", {}).get(\"confidence\", item_conf) or item_conf\n", "                desc_conf = (desc_conf * int(desc!=\"\") + product_conf * int(product!=\"\"))/(int(desc!=\"\") + int(product!=\"\")+1e-7)\n", "                desc = product + \" \" + desc\n", "                desc = desc.strip()\n", "\n", "                # CHANGELOG: 01 NOV 2024 default amount change from -1 to 0. This treatment line would be removed later during post process as amount  == 0\n", "                amount_dict = item.get(\"value\", {}).get(\"Amount\", {}).get(\"value\", {}) or {}\n", "                amount = amount_dict.get(\"amount\", 0)\n", "                amount_conf = item.get(\"value\", {}).get(\"Amount\", {}).get(\"confidence\", 0.)\n", "                if amount_conf is None:\n", "                    amount_conf = item_conf\n", "\n", "\n", "                treatments.append({\"treatment_date\": treatment_date, \n", "                                   \"treatment\": desc, \n", "                                   \"amount\": amount, \n", "                                   \"treatment_date_conf\": treatment_date_conf,\n", "                                   \"treatment_conf\": desc_conf, \n", "                                   \"amount_conf\": amount_conf, \n", "                                   \"treatmentline_conf\": item_conf})\n", "\n", "            if not invoice_date:\n", "                invoice_date = cur_treatment_date\n", "                invoice_date_conf = cur_treatment_date_conf\n", "\n", "            if not isinstance(invoice_date, str):\n", "                invoice_date = invoice_date.isoformat()\n", "\n", "            invoice_info.append(\n", "                {\n", "                    \"service_provider\": service_provider,\n", "                    \"service_provider_address\": service_provider_address,\n", "                    \"content\": content,\n", "                    \"invoice_no\": invoice_no,\n", "                    \"invoice_date\": invoice_date,\n", "                    \"invoice_total\": invoice_total,\n", "                    \"service_provider_conf\": service_provider_conf,\n", "                    \"invoice_no_conf\": invoice_no_conf,\n", "                    \"invoice_date_conf\": invoice_date_conf,\n", "                    \"invoice_total_conf\": invoice_total_conf,\n", "                    \"treatments\": treatments\n", "                }\n", "            )\n", "            \n", "        ans[file_path] = invoice_info\n", "\n", "\n", "    return ans"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 186/186 [00:00<00:00, 31838.57it/s]\n"]}], "source": ["document_intelligence_parsed_res = parse_document_intelligence_res(document_intelligence_res)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["# check whether PaddleOCR results exists\n", "def paddleocr_extract_consultation_notes(file_path: str) -> str:\n", "    \"\"\"\n", "    Extracts text from a PDF or image file using PaddleOCR, handling broken files.\n", "\n", "    Args:\n", "        file_path: The full path to the file.\n", "\n", "    Returns:\n", "        A string containing the extracted text, or an empty string if the\n", "        file is broken, unsupported, or contains no text.\n", "    \"\"\"\n", "    file_suffix = Path(file_path).suffix.lower()\n", "    logger.info(f\"Processing {file_path}\")\n", "\n", "    # Initialize result to None\n", "    result = None\n", "\n", "    try:\n", "        if file_suffix in [\".pdf\", \".PDF\"]:\n", "            # Open the PDF file\n", "            pdf_document = fitz.open(file_path)\n", "\n", "            # Get the number of pages\n", "            number_of_pages = pdf_document.page_count\n", "            pdf_document.close() # Close the document after getting page count\n", "            logger.info(f\"File {file_path} has {number_of_pages} pages\")\n", "\n", "            # Initialize and run OCR\n", "            ocr = PaddleOCR(use_angle_cls=True, lang=\"en\", page_num=number_of_pages)\n", "            result = ocr.ocr(file_path, cls=True)\n", "\n", "        elif file_suffix in [\".png\", \".jpg\", \".jpeg\"]:\n", "            # Initialize and run OCR\n", "            ocr = PaddleOCR(use_angle_cls=True, lang=\"en\")\n", "            result = ocr.ocr(file_path, cls=True)\n", "\n", "        else:\n", "            # Raise a TypeError for unsupported file formats\n", "            raise TypeError(f\"Unsupported file type: {file_suffix}\")\n", "\n", "    except Exception as e:\n", "        # Catch exceptions from broken/corrupted files (e.g., from fitz.open or ocr.ocr)\n", "        logger.error(f\"Could not process broken or corrupted file: {file_path}. Error: {e}\")\n", "        return \"\" # Return empty string for broken files\n", "\n", "    # Process and sort the OCR results if extraction was successful\n", "    if result and result[0] is not None:\n", "        def get_sort_key(item):\n", "            # Sort by top-left y coordinate, then x coordinate\n", "            top_left_y = item[0][0][1]\n", "            top_left_x = item[0][0][0]\n", "            return top_left_y, top_left_x\n", "\n", "        sorted_pages = []\n", "        for page in result:\n", "            if page:\n", "                # Sort lines within each page\n", "                sorted_page = sorted(page, key=get_sort_key)\n", "                sorted_pages.append(sorted_page)\n", "\n", "        if not sorted_pages:\n", "            logger.error(f\"No content extracted from {file_path}\")\n", "            return \"\"\n", "\n", "        # Combine text from all pages\n", "        all_text = []\n", "        for page in sorted_pages:\n", "            page_text = []\n", "            for line in page:\n", "                # line[1][0] contains the text part of the OCR result\n", "                if line and len(line) > 1 and len(line[1]) > 0:\n", "                    page_text.append(line[1][0])\n", "            all_text.append(\" \".join(page_text))\n", "\n", "        return \"\\n\".join(all_text)\n", "\n", "    # Return an empty string if no result was generated\n", "    logger.warning(f\"No text detected in {file_path}\")\n", "    return \"\"\n", "\n", "def load_paddleocr_res(pcr_res_path: str = PADDLEOCR_RES_PATH, data_folder: str = DATA_FOLDER):\n", "    try:\n", "        df = pd.read_csv(pcr_res_path)\n", "        df[[\"content\"]] = df[[\"content\"]].fillna(value=\"\")\n", "        return df.to_dict(orient=\"records\")\n", "    except FileNotFoundError:\n", "        # extract all the text content using paddleOCR\n", "        ocr_result = []\n", "        for file_path in sorted(os.listdir(data_folder)):\n", "            try:\n", "                consultation_note = paddleocr_extract_consultation_notes(os.path.join(data_folder, file_path))\n", "            except TypeError:\n", "                consultation_note = \"\"\n", "            ocr_result.append({\n", "                \"file_path\": file_path,\n", "                \"content\": consultation_note\n", "            })\n", "        pd.DataFrame(ocr_result).to_csv(pcr_res_path, index=False)\n", "        return ocr_result"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["paddleocr_res = load_paddleocr_res(pcr_res_path=PADDLEOCR_RES_PATH, data_folder=DATA_FOLDER)\n", "paddleocr_info_dict = {item[\"file_path\"].lower(): item for item in paddleocr_res}"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["# add paddleocr_extraction to document intelligence results\n", "tmp = {}\n", "for k, v in document_intelligence_parsed_res.items():\n", "    paddleocr_content = paddleocr_info_dict[k.lower()][\"content\"]\n", "    tmp_invoice = []\n", "    for invoice in v:\n", "        invoice[\"paddleocr_content\"] = paddleocr_content\n", "        tmp_invoice.append(invoice)\n", "    tmp[k] = tmp_invoice\n", "document_intelligence_parsed_res = tmp\n", "del tmp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rule Development Set\n", "\n", "* Three rule sets\n", "    - Post-Processing Rule set: aim to correct the results\n", "    - Extraction Rule set: aim to grab more information from the content which Azure Document Intelligence might miss\n", "    - Fall Out Rule set: aim to remove those error invoices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* invoice example\n", "\n", "```\n", "{'service_provider': 'Greencross\\nVetsGreencross Vets Noosa Civic',\n", " 'content': \"\",\n", " 'invoice_no': '65884233',\n", " 'invoice_date': '2024-09-17',\n", " 'invoice_total': 90.7,\n", " 'service_provider_conf': 0.9125000000000001,\n", " 'invoice_no_conf': 0.81,\n", " 'invoice_date_conf': 0.947,\n", " 'invoice_total_conf': 0.938,\n", " 'treatments': [{'treatment_date': '2024-09-17',\n", "   'treatment': ' Veterinary Consultation & Examination: Health Assessment',\n", "   'amount': 104.0,\n", "   'treatment_date_conf': 0.947,\n", "   'treatment_conf': 0.8899999110000089,\n", "   'amount_conf': 0.888,\n", "   'treatmentline_conf': 0.914}\n", "   ]}\n", "   ```"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["# FallOut Rules\n", "def if_empty_fields(info: Dict) -> Tuple[bool, str]:\n", "    # CHANGELOG: 04 Nov 2024 update the service provider empty field checking\n", "    # CHANGELOG: 04 Nov 2024 reconstruct the empty fields check and messages\n", "    messages = []\n", "\n", "    invoice_no = info[\"invoice_no\"]\n", "    if_invoice_no_empty = len(invoice_no.strip()) == 0\n", "    if if_invoice_no_empty:\n", "        messages.append(\"Invoice Number not extracted\")\n", "\n", "    service_provider = info[\"service_provider\"].lower()\n", "    service_provider_address = info[\"service_provider_address\"].lower()\n", "    abn = info[\"ABN\"]\n", "    if_service_provider_empty = False\n", "    if  len(service_provider.strip())==0 or service_provider.startswith(\"dr.\") or service_provider.startswith(\"dr \"):\n", "        # service provider extraction empty\n", "        # service provider extraction could be wrong that leads to no fuzzy matching result\n", "        if not (abn or service_provider_address):\n", "        # no abn is extracted from the invoice content\n", "        # no address extracted by DI\n", "            if_service_provider_empty = True\n", "            messages.append(\"Service provider name not extracted\")\n", "\n", "    invoice_date = info[\"invoice_date\"]\n", "    if_invoice_date_empty = len(invoice_date.strip()) == 0\n", "    if if_invoice_date_empty:\n", "        messages.append(\"Invoice date not extracted\")\n", "\n", "    total_amount = info[\"invoice_total\"]\n", "    if_total_amount_empty = len(str(total_amount).strip()) == 0\n", "    if if_total_amount_empty:\n", "        messages.append(\"Invoice total not extracted\")\n", "\n", "\n", "    activate = len(messages) > 0\n", "    if activate:\n", "        return activate, f\"FALL OUT: {'|'.join(messages)}\"\n", "    return activate, \"\"\n", "\n", "from datetime import datetime\n", "def if_date_in_future(info: Dict) -> Tuple[bool, str]:\n", "    invoice_date = info[\"invoice_date\"]\n", "    try:\n", "        # Convert the date string to a date object\n", "        date = datetime.strptime(invoice_date, '%Y-%m-%d').date()\n", "        # Get today's date\n", "        today = datetime.today().date()\n", "        # Check if the date is in the future\n", "        activate = date > today\n", "        messages = [\"\", \"FALL OUT: Date in Future\"]\n", "        return activate, messages[activate]\n", "    except ValueError:\n", "        return True, \"FALL OUT: Date Format Error\"\n", "\n", "def if_invoice_no_len_fit(info: Dict) -> Tuple[bool, str]:\n", "    invoice_no = info[\"invoice_no\"]\n", "    activate = len(invoice_no) <= 4 or len(invoice_no) >= 20\n", "    messages = [\"\", \"FALL OUT: Invoice Len\"]\n", "    return activate, messages[activate]\n", "\n", "from PIL import Image\n", "def if_file_broken(path: str) -> Tuple[bool, str]:\n", "    def check_pdf(filepath):\n", "        try:\n", "            with fitz.open(filepath) as doc:\n", "                doc.load_page(0)  # Try loading the first page\n", "            return True\n", "        except FileNotFoundError as fnfe:\n", "            filepath = filepath[:-3]+\"PDF\"\n", "            with fitz.open(filepath) as doc:\n", "                doc.load_page(0)  # Try loading the first page\n", "            return True\n", "        except Exception as e:\n", "            logger.exception(f\"PDF error: {e}\")\n", "            return False\n", "\n", "    def check_image(filepath):\n", "        try:\n", "            with Image.open(filepath) as img:\n", "                img.verify()  # Verifies image integrity\n", "            return True\n", "        except FileNotFoundError as fnfe:\n", "            file_suffix = Path(filepath).suffix\n", "            filepath = filepath[:-len(file_suffix)] + str(file_suffix).upper()\n", "            with Image.open(filepath) as img:\n", "                img.verify()  # Verifies image integrity\n", "            return True\n", "        except Exception as e:\n", "            logger.exception(f\"JPG error: {e}\")\n", "            return False\n", "    suffix = str(Path(path).suffix)\n", "\n", "    if suffix.lower() == \".pdf\":\n", "        activate = not check_pdf(path)\n", "    elif suffix.lower() in [\".jpeg\", \".jpg\", \".png\", \".bmp\", \".tiff\"]:\n", "        activate = not check_image(path)\n", "    else:\n", "        activate = True\n", "    messages = [\"\", \"FALL OUT: Broken File or Invalid File\"]\n", "    return activate, messages[activate]\n", "\n", "def if_empty_treatment(info: Dict) -> Tuple[bool, str]:\n", "    treatments = info[\"treatments\"]\n", "    activate = len(treatments) == 0\n", "    messages = [\"\", \"FALL OUT: Zero Treatment Line\"]\n", "    return activate, messages[activate]\n", "\n", "def if_negative_invoice_total(info: Dict) -> Tuple[bool, str]:\n", "    invoice_total = info[\"invoice_total\"]\n", "    activate = invoice_total < 0\n", "    messages = [\"\", \"FALL OUT: Negative Invoice Total\"]\n", "    return activate, messages[activate]\n", "\n", "def if_negative_treatment_amount(info: Dict) -> Tuple[bool, str]:\n", "    treatments = info[\"treatments\"]\n", "\n", "    # CHANGELOG: 06 NOV 2024 add acceptance rules for discount and rounding which are in the treatment lines\n", "    activate = False\n", "    for treatment in treatments:\n", "        # acceptable amount\n", "        if treatment[\"amount\"] >= 0:\n", "            continue\n", "        else:\n", "            if if_diff_invoice_total_sum_treatment_amount(info)[0]:\n", "                activate = True\n", "                break\n", "            else:\n", "                continue\n", "        # # rounding acceptance # TODO to be further refined for the rounding mapping\n", "        # if treatment[\"treatment\"].lower().strip() == \"rounding\" and -0.05 < treatment[\"amount\"] < 0:\n", "        #     continue\n", "\n", "        # # discount acceptance # TODO to be further refined for the discount mapping\n", "        # elif treatment[\"treatment\"].lower().strip().startswith(\"discount\"):\n", "        #     continue\n", "\n", "        # # fallout\n", "        # elif treatment[\"amount\"] < 0:\n", "        #     activate = True\n", "        #     break\n", "    # activate = any(treatment[\"amount\"] < 0 for treatment in treatments)\n", "    messages = [\"\", \"FALL OUT: Negative Treatment Amount\"]\n", "    return activate, messages[activate]\n", "\n", "def if_diff_invoice_total_sum_treatment_amount(info: Dict) -> Tuple[bool, str]:\n", "    # CHANGELOG: 4/11/2024 update the accept_rounding from 0.01 to 0.05\n", "    accept_rounding = 0.05\n", "    treatments = info[\"treatments\"]\n", "    treatment_total = sum([treatment[\"amount\"] if treatment[\"amount\"] else 0. for treatment in treatments ])\n", "    invoice_total = info[\"invoice_total\"]\n", "\n", "    activate = abs(round(invoice_total,2) - round(treatment_total, 2)) >= accept_rounding\n", "    messages = [\"\", \"FALL OUT: Invoice total does not match with line totals\"]\n", "    # CHANGELOG: 04 NOV 2024 lift invoice total confidence if invoice total == sum(treatment amount)\n", "    if not activate:\n", "        info[\"invoice_total_conf\"] = min(1.0, info[\"invoice_total_conf\"] + 0.3)\n", "    return activate, messages[activate]\n", "\n", "def if_over_conf_threshold(info: Dict, conf_threshold: float = 0.8) -> Tuple[bool, str]:\n", "    conf = min(info[\"invoice_no_conf\"], info[\"invoice_date_conf\"], info[\"invoice_total_conf\"])\n", "    activate = conf < conf_threshold\n", "    messages = [\"\", \"FALL OUT: Low Confidence\"]\n", "    return activate, messages[activate]"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["# Extraction Rules\n", "import re \n", "abn_extract_regex = r\"(?:\\d *){11}\"\n", "abn_extract_regex1 = r\"\\d{2}-\\d{3}-\\d{3}-\\d{3}\"\n", "\n", "def validate_abn(nums: List[int]) -> bool:\n", "    if len(nums) != 11:\n", "        return False\n", "    if not all(isinstance(x, int) for x in nums):\n", "        return False\n", "    if any(x>9 for x in nums):\n", "        return False\n", "    if any(x<0 for x in nums):\n", "        return False\n", "\n", "    s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))\n", "    return s%89 == 0\n", "\n", "\n", "def get_abn(info: Dict) -> List[str]:\n", "    content = info[\"content\"]\n", "    matches = re.findall(abn_extract_regex, content)\n", "    matches1 = re.findall(abn_extract_regex1, content)\n", "    ans = []\n", "    for match in matches + matches1:\n", "        match_num = []\n", "        for c in match:\n", "            try:\n", "                int_c = int(c)\n", "                match_num.append(int_c)\n", "            except ValueError:\n", "                continue\n", "        if validate_abn(match_num):\n", "            ans.append(match_num)\n", "\n", "    ans = list({\"\".join([str(x) for x in abn]) for abn in ans})\n", "    info[\"ABN\"] = ans\n", "    return info\n", "\n", "\n", "# TODO\n", "import re\n", "# get phone number \n", "def extract_phone_number(content: str) -> List[str]:\n", "    # phone number extraction\n", "    phone_number_regex = r\"\\+?61[- ]?\\d{1,2}[- ]?\\d{4}[- ]?\\d{4}\"\n", "    matches = re.findall(phone_number_regex, content) \n", "    return matches\n", "    \n", "\n", "# get email \n", "def extract_email(content: str) -> List[str]:\n", "    try: \n", "        # email extraction with sender name\n", "        pattern = r\"([a-zA-Z\\s]+)?\\s*<([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})>\"\n", "        matches = re.findall(pattern, content)\n", "    except Exception:\n", "        # Email regex only\n", "        pattern = r\"([a-zA-Z\\s]+)?\\s*<([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})>\"\n", "        matches = re.findall(pattern, content)\n", "    return matches\n", "# get weblink\n", "def extract_weblink(content: str) -> List[str]:\n", "    # weblink extraction\n", "    weblink_regex = r\"(http|ftp|https)://([\\w_-]+(?:(?:\\.[\\w_-]+)+))([\\w.,@?^=%&:/~+#-]*[\\w@?^=%&/~+#-])?\"\n", "    matches = re.findall(weblink_regex, content)\n", "    return matches\n", "    \n", "# get shipping Pet Chemist Online\n", "def get_shipping_pet_chemist_online(content: str) -> float:\n", "    # get related text snippet\n", "    # Regex to match the full section containing payment information\n", "    section_pattern = r\"Delivery Method:.*?(?:Discount|$)\"  # Matches from \"Delivery Method\" to \"Discount\" or end of text\n", "\n", "    # Extract the full section with payment information\n", "    section_match = re.search(section_pattern, content, re.DOTALL)\n", "    # extract $\n", "    try:\n", "        section_text = section_match.group()  # Extract the matched section\n", "\n", "        # Money regex to extract all amounts from this section\n", "        money_pattern = r\"\\$(\\d{1,3}(?:,\\d{3})*(?:\\.\\d{2})?)\"\n", "        amounts = re.findall(money_pattern, section_text)\n", "        # adjust which one\n", "        return float(amounts[-1])\n", "    except Exception:\n", "        return None\n", "    return None\n", "\n", "def get_shipping(info: Dict) -> Dict:\n", "    abn = info[\"ABN\"]\n", "    content = info[\"content\"]\n", "    # Pet Chemist Online\n", "    if \"***********\" in abn:\n", "        shipping = get_shipping_pet_chemist_online(content)\n", "        if shipping:\n", "            invoice_date = info[\"invoice_date\"]\n", "            invoice_date_conf = info[\"invoice_date_conf\"]\n", "            info[\"treatments\"].append({\n", "                \"treatment_date\": invoice_date,\n", "                \"treatment\": \"shipping\",\n", "                \"amount\": shipping,\n", "                \"treatment_date_conf\": invoice_date_conf,\n", "                \"treatment_conf\": 1.0,\n", "                \"amount_conf\": 1.0,\n", "                \"treatmentline_conf\": invoice_date_conf})\n", "    return info\n"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["# Post-Process Rules\n", "from copy import deepcopy\n", "\n", "def postprocess_treatmentline(info: Dict) -> Tuple[Dict, str]:\n", "    ans = deepcopy(info)\n", "    # CHANGELOG: 06 NOV 2024 update the payment related treatment line removal logic\n", "    # CHANGELOG: 06 NOV 2024 update the discount related treatment line removal logic    \n", "    treatments = ans[\"treatments\"]\n", "    treatment_total = sum([treatment[\"amount\"] if treatment[\"amount\"] else 0. for treatment in treatments ])\n", "    invoice_total = ans[\"invoice_total\"]\n", "\n", "    activate = False\n", "    message = [\"\", \"POSTPROCESS: Treatment Line Curation\"]\n", "\n", "    tmp = []\n", "    for treatment in treatments:\n", "        # CHANGELOG: add .strip() to remove space string\n", "        desc = treatment[\"treatment\"].strip().lower()\n", "\n", "        # \"\" in treatment line gets ignored\n", "        if len(desc) == 0:\n", "            activate = True\n", "            message[1] = \"POSTPROCESS: Empty Treatment Line\"\n", "            continue\n", "\n", "        # 0 in treatment line amount gets ignored\n", "        if treatment[\"amount\"] == 0:\n", "            activate = True\n", "            message[1] = \"POSTPROCESS: Zero Treatment Amount\"\n", "            continue\n", "        \n", "        # asking <PERSON> and <PERSON> for raw t containing \"eftpos\" as substring or not\n", "        if \"eftpos\" in desc:\n", "            activate = True\n", "            message[1] = \"POSTPROCESS: EFTPOS in Treatment Line\"\n", "            continue\n", "    \n", "        # \"payment\" or \"eftpos\" in treatment line gets ignored\n", "        #  payment surcharge should be reserved\n", "        if \"payment\" in desc:\n", "            if not (\"surcharge\" in desc and treatment_total == invoice_total):\n", "                activate = True\n", "                message[1] = \"POSTPROCESS: Payment in Treatment Line\"\n", "                continue\n", "        \n", "        tmp.append(treatment)\n", "\n", "    ans[\"treatments\"] = tmp\n", "\n", "    return ans, message[activate]\n", "\n", "\n", "def find_similar_substring(content: str, target: str, max_diff: int = 2):\n", "    \"\"\"\n", "    Find a substring in the content that has the same length as the target substring,\n", "    with only one or two different characters or digits.\n", "\n", "    Args:\n", "        content (str): The string to search within.\n", "        target (str): The substring to compare with.\n", "        max_diff (int): Maximum number of allowed differences. Default is 2.\n", "\n", "    Returns:\n", "        list: A list of matching substrings that differ by at most max_diff characters.\n", "    \"\"\"\n", "    target_len = len(target)\n", "    result = []\n", "\n", "    # Loop through content to get every possible substring of the same length as target\n", "    for i in range(len(content) - target_len + 1):\n", "        sub_str = content[i:i + target_len]\n", "        if i-1 >= 0 and content[i-1].isalnum():\n", "            continue\n", "        if i + target_len < len(content) and content[i + target_len].isalnum():\n", "            continue\n", "\n", "        # Count how many characters are different between sub_str and target\n", "        # di_invoice_no: 1012538, invoice_no_fuzzy_res: ['.00 58.', '. 1/21 ', '1012540'\n", "        diff_count = 0\n", "        for a, b in zip(sub_str.lower(), target.lower()):\n", "            if a!=b:\n", "                if a.isalnum() and b.isalnum():\n", "                    diff_count += 1\n", "                else:\n", "                    diff_count += max_diff+1\n", "                    break\n", "                # if b.isalnum() and not a.isalnum():\n", "                #     diff_count += max_diff\n", "                #     break\n", "                # elif not b.isalnum():\n", "                #     diff_count += max_diff\n", "                #     break\n", "        # If the difference is within the allowed limit, add to the result\n", "        if diff_count <= max_diff:\n", "            result.append(sub_str)\n", "\n", "    return list(set(result))\n", "\n", "def postprocess_invoice_no(info: Dict) -> Tuple[Dict, str]:\n", "    ans = deepcopy(info)\n", "    activate = False\n", "    message = [\"\", \"POSTPROCESS: Invoice No Curation\"]\n", "    \n", "    invoice_no = ans[\"invoice_no\"]\n", "    content = ans[\"content\"]\n", "    paddle_ocr_content = ans[\"paddleocr_content\"]\n", "\n", "    # CHANGELOG: 05 NOV 2024 add . into the stip set\n", "    invoice_no = invoice_no.strip(\"#) .\")\n", "    ans[\"invoice_no\"] = invoice_no\n", "\n", "    # later fall out\n", "    if len(invoice_no)<=4 or len(invoice_no)>=20:\n", "        return ans, \"\"\n", "    #  if invoice no and receipt no both appear, ask gpt (choose invocie no)\n", "    if \"invoice no\" in content.lower() and \"receipt no\" in content.lower():\n", "        return ans,\"POSTPROCESS: Invoice No Receipt No both Appearance. Need GPT Verification\"\n", "    \n", "    invoice_no_fuzzy_res = find_similar_substring(paddle_ocr_content, invoice_no, max_diff=2)\n", "    if len(invoice_no_fuzzy_res) == 0:\n", "        return ans, \"\"\n", "    elif len(invoice_no_fuzzy_res) == 1:\n", "        ans[\"invoice_no\"] = invoice_no_fuzzy_res[0]\n", "        return ans, f\"POSTPROCESS: Invoice No Replaced by PaddleOCR. Original: {invoice_no}\"\n", "    else:\n", "        return ans, f\"POSTPROCESS: Multi Fuzzy Invoice No Extracted by PaddleOCR. Need GPT Verification. Fuzzy: {invoice_no_fuzzy_res}\"\n", "\n", "\n", "# CHANGELOG: 06 NOV 2024 add gst for each item for certain service providers\n", "def if_extra_gst_service_provider(info: Dict) -> bool:\n", "    # TODO to be continued when samples got\n", "    extra_gst_servie_provider_abn_set = {\"***********\", # Pet Chemist Online\n", "                                         \"***********\", # Perth Animal Eye Hospital\n", "                                         \"***********\", # Melbourne Veterinary Specialist Centre-Essendon\n", "                                         \"***********\", # Hamilton Hill Veterinary Hospital\n", "                                         \"***********\", # Animal Eye Care\n", "                                         \"***********\", # Pets At Peace\n", "                                         \"***********\", # Dermatology for Animals\n", "                                         \"***********\", # Walk-In Clinic for Animals\n", "                                         }\n", "    abn = info[\"ABN\"]\n", "    return len(extra_gst_servie_provider_abn_set & set(abn)) > 0\n", "\n", "def postprocess_extra_gst_adjustment(info: Dict) -> <PERSON><PERSON>[Dict, str]:\n", "    ans = deepcopy(info)\n", "\n", "    if not if_extra_gst_service_provider(ans):\n", "        return ans, \"\"\n", "\n", "    treatments = ans[\"treatments\"]\n", "\n", "    tmp = []\n", "    for treatment in treatments:\n", "        adjust_treatment = deepcopy(treatment)\n", "        adjust_treatment[\"amount\"] = round(treatment[\"amount\"] * 1.1, 2)\n", "        tmp.append(adjust_treatment)\n", "\n", "    ans[\"treatments\"] = tmp\n", "\n", "    return ans, \"POSTPROCESS: Treatment Line GST Adjustment\""]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["# FallOut Rule Set\n", "fallout_rule_set = [if_empty_fields, if_date_in_future, if_invoice_no_len_fit,  \n", "                    if_empty_treatment, if_negative_invoice_total, \n", "                    if_negative_treatment_amount, if_diff_invoice_total_sum_treatment_amount, if_over_conf_threshold]\n", "# Extraction Rule Set\n", "extraction_rule_set = [get_abn, get_shipping]\n", "# PostProcess Rule Set\n", "postprocess_rule_set = [postprocess_treatmentline, postprocess_invoice_no, postprocess_extra_gst_adjustment]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rule Running Process\n", "\n", "* load rules from different rule sets\n", "* executive rules"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["def run_rules(data: Dict, fallout_rule_set: List=fallout_rule_set, extraction_rule_set: List = extraction_rule_set, postprocess_rule_set: List=postprocess_rule_set):\n", "    ans = {}\n", "    for k, v in data.items():\n", "        logger.info(k)\n", "        tmp = []\n", "        for invoice in v:\n", "            invoice = deepcopy(invoice)\n", "            notes = []\n", "\n", "            # run extraction rule\n", "            for erule in extraction_rule_set:\n", "                invoice = erule(invoice)\n", "\n", "            # run postprocess rule\n", "            for pprule in postprocess_rule_set:\n", "                invoice, message = pprule(invoice)\n", "                notes.append(message)\n", "\n", "            # run fall out rule\n", "            for frule in fallout_rule_set:\n", "                activate, message = frule(invoice)\n", "                if activate:\n", "                    notes.append(message)\n", "            invoice[\"rule_res\"] = \" \".join(notes)\n", "            tmp.append(invoice)\n", "        ans[k] = tmp\n", "    return ans"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-06-24 15:05:38.111\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m0983b113-2772-4a1d-bba6-3b8f823648c3.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.113\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m0ea11fff-e5bb-48b1-b34e-42f8feb7339a.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.114\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m1062b12f-027c-44bd-b8d0-53d00d5f20cb.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.115\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m113ddc44-be51-4cae-9f59-d419dc9f61a6.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.116\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m119ffa18-93ab-47e2-8c44-87bcbe4dd9b5.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.117\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m1374dd27-21f2-4aea-b317-eeaa5639b49b.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.119\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m15155ac2-737c-4fd0-a896-823316c0b754.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.120\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m16a6ba20-9d94-4ae0-aa4e-a4972fa18cab.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.121\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m16c87632-8071-4110-aa04-81965fbe6670.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.125\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m1b3def1d-4b23-4bd4-a94e-61d62bdc5c23.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.127\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m1c2502de-8e09-46af-ac2b-0244d7c4b129.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.128\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m219df155-ae50-4cdc-8439-025d2154615e.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.132\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m23136292-93ae-4215-bc52-068cb578379a.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.134\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m239e095a-6128-4b18-8675-c417a402e0c9.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.135\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m25714a32-883c-4fa2-bc07-81087ac14c73.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.136\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m263bb02a-0a2d-456e-828d-baad0d413431.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.137\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2648ba8e-b09f-42be-9fa8-df7dd4ab23a2.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.138\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m26695410-7ebe-4050-b310-5d4bbda59f42.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.142\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2734ddd6-df57-452a-aba7-167c5574737b.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.143\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2741d777-80a3-4181-b726-f656c71d3528.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.145\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m28a991aa-d832-45f3-9c81-f75cc0055008.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.146\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m29477e5a-06c4-44b5-be2f-b6a71d646ff6.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.147\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m299afb8e-49f6-4abd-af9a-5342f3f61472.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.149\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m29d0199c-2a2a-47bf-889f-0202a41c5df2.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.150\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2ae1e421-be3f-4a2a-b668-8ce542a8d9e0.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.151\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2bb165c6-6ed1-47b2-912a-6ff482aa2a68.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.152\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2c84a716-09aa-41ae-9d3d-b1f040777c1e.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.152\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2f2a772a-2ce6-4865-bdc0-299fe1ec47eb.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.153\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2f2e5944-f6d3-4512-85dc-d69be503c632.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.155\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m30467500-9f9e-47ff-b00a-d49acd7083d3.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.155\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m308073fb-7e2c-4270-a6c7-6547def20a2d.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.156\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m30b75c3d-062e-4ca1-96ef-e38c8604e886.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.157\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m33531129-818c-47d8-977f-0820eb76d2e9.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.159\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m335dbabf-e1ec-41ab-90db-3a299b5bdc6e.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.160\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m34193ed0-3c82-4c5c-a90f-d2a2e8a2ba29.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.161\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m34fb275e-1faa-4e3d-bb49-edfcd006ddcf.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.162\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m35795076-115e-40be-87b8-fcb61376f29d.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.163\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m360e641e-9e01-4b6c-81fa-7e2388fa66c5.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.164\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m36f6e764-7e8d-4ac8-9e8b-bb07ed1506e9.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.165\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m3940785d-266f-403d-912f-ed816c76bc73.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.167\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m3a91c57a-ed49-479a-8472-b0f0ff3e3adb.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.168\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m3a9518c8-ce07-474d-8d5b-ee55b196cc9b.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.169\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m3c1d9906-788a-48c0-a346-e89110928f8d.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.170\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m3dbceb2b-88c6-4ae9-b040-941a94c74ce0.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.171\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m3f5d87b9-aa25-40b5-9dcd-56d160d5cb30.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.172\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m41f46159-bfd0-449f-8907-5f2133117fe8.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.173\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m42d81376-1947-4da8-af88-1232ba21d49a.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.174\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m47439fba-95e7-4fcb-9671-1c8952b9729f.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.175\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m482ca2ba-f597-4403-aecc-06fc22480999.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.177\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m4a1fc5f6-133c-4dee-95b2-344c5d5cd27a.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.178\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m4b330b52-6c0d-4d0c-8397-ba10d407c24b.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.179\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m4bda80bc-80e8-4321-87ce-c96f1f78f412.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.181\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m4f402f17-5f37-475b-be4a-40d67710ed93.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.182\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m50813bb1-80cb-4ef4-8132-f25b97b0a875.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.183\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m508c5dfc-94b5-419e-b60d-dae495437f4a.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.184\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m523c2af3-006a-4cb7-b620-ec4928211e8d.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.185\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m599e2a45-ccd2-4116-a648-8376191fef83.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.186\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m5cbfccd2-a555-40c9-9fd9-08bbc48e7a0d.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.187\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m5cf82189-efc0-400e-b369-c4c71e7379dc.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.188\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m623b3d0d-66ae-4566-879c-740717699b58.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.189\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m685f8482-ea8e-428c-9810-97f9c5f39b47.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.190\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m68c2f8b4-b5ce-4d2c-8ae7-c88dd593da1b.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.191\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m6abb489a-3bd3-496a-9422-40c46de85121.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.192\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m6b00462f-730c-4afd-be7c-fe5dfcde4f9e.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.193\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m6b034de0-ad17-49ae-8e88-d5c94e125c9a.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.194\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m6d2c7e87-698b-4126-b51e-9ecee0f0e3d3.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.194\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m6dd8a312-97a4-424f-8f94-2e56217c68a5.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.195\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m6e28f1ac-2f8a-450d-8e1b-28086baf8eee.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.196\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m6e6abe9f-600a-4338-a34f-4d27c8d32a9d.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.197\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m715fed9d-16b6-4b1a-a856-acca01dc53e8.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.201\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m726fbffa-d298-45b6-bbd4-9c10d5f73d22.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.203\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m72ff5e9b-a84a-428c-bdcb-15fc3fd5bdf3.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.204\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m73585170-d997-4b5c-b6f7-d01b8cfa4b91.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.207\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m7623da79-c45b-4399-9c77-36d6520906c4.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.208\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m76892c01-5c93-4797-8dd8-b1b7dba47448.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.209\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m76bbf9e7-be45-4a65-8d45-0d3be2f0bfba.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.209\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m76e9b3e5-2caf-409c-a14b-6c47a684cf76.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.211\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m7846deae-b381-40bd-90da-136a2531ab6c.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.212\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m7932828b-60c7-41f0-8f94-186c9fff6a09.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.213\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m7b9560d5-a2d4-48ce-86c6-ff5f60ec083a.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.214\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m7c1d1509-c3dd-4813-9a05-30e3fc96f9f0.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.215\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m7dda6eb4-9562-40dc-9cb5-d92cb6c6bfc6.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.216\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m7fac6559-015d-4e04-9b56-b8ce657d6dcb.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.219\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m868bcdd8-23e7-457c-b2c8-402c64c203d9.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.221\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m86b0491a-8b9b-4af5-8cc2-d2a9bf1a0db8.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.222\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m86d7dc94-cb69-4dbe-8829-01cbf179dfb7.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.223\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m87198a9d-a601-4604-aef5-eb6be1c9a515.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.224\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m8752e222-7cba-4e4d-a4d1-39dee844e767.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.225\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m87ad059f-d90e-44cd-ba64-8e870c4934c8.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.225\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m8b5d0177-e192-4b1e-92f0-f2e85e0b4724.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.228\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m8e0b3a6e-df27-4995-b423-5a5123842a33.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.229\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m8f296e17-06f7-4351-a7f9-bed18ed51edf.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.229\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m8f59457b-86ed-4e8d-8b86-a1df0c16234d.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.231\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m914a8b18-1f55-454a-a06e-d340baa13e69.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.232\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m922243f2-e399-4d97-8f44-9c6dcaba7def.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.233\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m9325df8e-5f69-47c8-84a7-23fc2e3410c3.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.238\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m9342db1b-d51d-44ab-b420-b2d4f7a84408.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.238\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m991a343b-db42-48f9-a2c6-4733bc00818b.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.239\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m9a064089-5975-46a1-9bf8-e9b9fcbf9c6d.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.244\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m9abe7d7c-e4cf-4ffb-bd08-84acab4e4bea.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.245\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m9c0fb666-46e3-4269-8817-12446b82a0db.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.247\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m9eb8cc06-4199-4a1a-a09f-cbb4d246129b.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.249\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m9f6ee82c-4f37-47e7-abd9-9b6917b30e26.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.250\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1ma19638d6-4882-4d5a-a377-174d1476c19c.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.255\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1ma3cf32cb-be9f-4a8a-b1e4-01d78d98a06b.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.260\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1ma4689e65-395b-4d64-a51f-10a122ed16ff.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.261\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1ma4bc2b00-e36c-486b-911e-e6062da64381.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.263\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1ma6716664-5475-415f-8c2e-ad7fd7650931.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.264\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1ma9098ada-63c9-48d9-8fcc-a219182caaf5.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.265\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1maa052f34-10a1-47f3-9cc2-4e02c58d1e75.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.276\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1maa0b7afe-c750-4236-9bd4-ddd504f6a830.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.285\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mab059fda-2515-4e84-9084-e09e9e0d177e.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.292\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1maeea5fc0-4283-455c-b74e-c3fef9f1b417.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.297\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mafe4dbeb-1aec-4cc8-b117-df2579fa5e29.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.299\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb0d37f88-15f6-4188-966e-e8cfcfcd8614.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.304\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb1fa9de9-d1d2-46af-b5f0-575e38e9d78f.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.308\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb2ea7e5b-2e5e-4457-9845-05a65248bc68.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.309\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb3f4eb21-ed71-41f5-bc89-5ed37ed9e0d2.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.310\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb4b0384c-8a19-4301-a95e-8d3dd99634a8.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.318\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb502d068-d3bb-49ca-b6f8-c62d3b913f67.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.325\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb5684ab8-df5b-4fc3-b42f-2ce146143673.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.326\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb85f98b7-f03c-4e54-ba77-165aee5c7aa3.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.327\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb8739994-e458-4c38-a2a0-af3eb28f2f3d.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.333\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb9af9fad-4d1b-488b-8806-2249f42b135b.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.337\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mbaf45e1d-544d-4279-845e-b7a1c2483b22.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.343\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mbd073637-f5c2-4b28-8fec-e82e4cab6d75.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.348\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mbebbf2fe-fd7f-4f85-83b2-5a0ff90602a6.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.350\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mbf08f732-cba7-4194-8bae-399110de3bcb.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.351\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mbf57177b-5958-43f7-9907-de3d5a8ac198.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.353\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc0686456-4df6-4971-acae-5967d5fa03c6.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.356\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc1d83450-f140-4ab9-b450-4254fe4ac73c.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.357\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc36f4dbb-6965-40d4-9ab0-fd3d84335aca.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.358\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc3bea3ed-9021-4c74-a8cc-6e8c920382d7.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.359\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc4407416-beaf-4995-b623-9188e09f41f6.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.360\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc4567aa5-c640-4871-bf5a-713a47f50dcd.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.361\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc47ba1b3-18eb-41c0-b6b4-534c2072ed0a.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.362\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc597e074-e15a-4a27-aca9-cce9418a8a9b.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.363\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc59cecec-6f03-4848-a4f8-2d96b8d2c571.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.364\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc5dc26b3-296b-4263-aa6a-46d2d957cab1.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.365\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc67270af-20ac-49d6-9e75-e15493f3fe40.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.366\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc6ea18d2-8dba-4de1-be00-4444b7263642.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.367\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc73e443c-685f-4631-b778-6b7c9ecb1787.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.368\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc9febf04-986b-407d-b604-7aef06f91f0b.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.369\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mca85a248-2ceb-4c8c-9e49-7c33ec8c1185.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.370\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mcad26cd8-a44f-4ee3-b0bd-457b192bd80c.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.371\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mcb0f60bc-3a19-4d3a-999f-7fe53d71f3d4.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.371\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mcc51d137-9fe8-41e2-88e7-5f701e34cea4.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.372\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mcc9e791a-db3b-4ed0-a539-d7e7e81e004a.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.373\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mcda22c8e-2441-442f-b74e-82dff4b4f9e4.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.374\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mce6a3a36-ce49-4e91-a977-48a51be5ad5a.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.375\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mcf4377f2-82bf-44ed-b7cf-8e19ee0e3738.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.376\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md0033a21-fcc6-446d-a4b9-1928ed5f53cf.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.377\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md22374c1-c6cc-47f2-bc8b-4e5ad6a47d56.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.378\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md3af8200-7f9a-4ae6-89f6-26869aeb9214.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.379\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md7463aa5-be0f-4cb1-9a06-62831a7f285d.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.379\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md9255a09-253e-4662-b10d-412056ca630f.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.380\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md9300c44-fa8b-47a3-95ee-3217f7467adb.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.381\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1me6c530e2-440b-4c7f-af60-8d38dd0f6cc4.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.382\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1me80da5b0-91e3-458f-ae77-f733376c7d2c.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.383\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1me867af8b-0419-48bd-a291-a45d1e7f3c3c.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.384\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1me939ca03-16f2-4f77-ae34-f92d4bc803c0.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.385\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1me95db2b4-5171-426b-816b-af605b572ebb.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.387\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1me9ae7ed7-6c0b-45d1-a30c-0456a18b803c.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.388\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1me9bdf3b4-ab35-46c4-bfc7-5527500dacc3.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.390\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1meb1d2e96-79cc-40f8-b3df-e8e9f9c5e728.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.391\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1med2f4c7e-70a2-45b3-9d5a-5b541878d536.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.393\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1med36000f-0956-4a6e-a088-e8fb6b4766f0.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.394\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1med3a4869-61d6-4f07-b09e-1cb1191534f6.png\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.400\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1meddedc60-176b-4775-bbea-bb8ff98001c1.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.401\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1medef0a6c-a92c-454d-b7fc-bddb9395e29d.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.402\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1medf82e98-e810-46ac-bd12-dc9e92a2ebf9.PDF\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.403\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mef85edc9-3738-46e0-9b78-09fdc1aa8cee.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.404\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf02ba235-8042-49b9-ba6a-c2876d5fe059.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.408\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf27d5f3c-81c5-47fd-a75e-eda1b0ed0e0c.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.410\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf2817608-6d63-4d12-8552-6c406e462b27.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.411\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf3a7e9ba-4224-4c29-927d-75bfbac16d39.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.412\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf403e62a-75d7-450a-bdfd-7ffbc74bdd1f.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.413\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf523ee1b-16cb-425d-9324-2d65140125cd.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.414\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf57c179b-a916-4338-9eb3-39135222a8bd.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.415\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf5bfa1e1-22ca-4d6e-894b-15c4cfac5217.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.416\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf76a33b0-dbb3-40bb-b92b-273c8c5729a7.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.417\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf7f8b054-6b09-46a8-8116-d631d1299da5.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.418\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mfa9cd670-c0b5-429f-af9b-70b4404d13ba.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.418\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mfb9933ad-9ce4-4dda-9155-dbce08d4be92.jpg\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.419\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mfd33bfa0-deab-4e7a-b4fb-0ef1ad01d672.pdf\u001b[0m\n", "\u001b[32m2025-06-24 15:05:38.420\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mrun_rules\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mfef5b661-05f2-44f4-bfd4-c50cbdf2364e.pdf\u001b[0m\n"]}], "source": ["document_intelligence_rule_res = run_rules(document_intelligence_parsed_res)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["186"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["len(document_intelligence_rule_res)"]}, {"cell_type": "code", "execution_count": 176, "metadata": {}, "outputs": [], "source": ["with open(f\"../data/OCR_in_house/samples/{sample_prefix}_samples_DI_rule.json\", \"w\") as fout:\n", "    json.dump(document_intelligence_rule_res, fout, indent=4, default=str)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Service Provider Matching"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["import ast\n", "from fuzzywuzzy import fuzz\n", "import numpy as np\n", "import pandas as pd\n", "from gensim.models import Word2Vec\n", "from src.service_provider_matching.preprocessing.preprocess import preprocess, preprocess_numbers, preprocess_web"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["# config\n", "# Dictionary to link between OCR data and list of Service Provider columns\n", "sp_dict = {\n", "    \"Name\": \"ServiceProviderName\",\n", "    \"Address\": \"Address\",\n", "    # \"streetname_name\": \"ServiceProviderName\",\n", "    # \"suburb_name\": \"ServiceProviderName\",\n", "    \"abn\": \"ABN\",\n", "    \"email\": \"Email\",\n", "    \"web\": \"HomePage\",\n", "    \"phone_home\": \"PhoneNo_Home\",\n", "    \"phone_work\": \"PhoneNo_Work\",\n", "    \"phone_mobile\": \"PhoneNo_Mobile\",\n", "    \"fax\": \"FaxNo\",\n", "}\n", "\n", "# List of hyperparameters\n", "top_n = 10  # Top number of fuzzy matches to keep for Name, Address\n", "cos_l = 0.5  # Lower limit on cosine acceptance\n", "A_log = 1.0  # Logarithmic amplitude for scaling ABN multi-matches\n", "priority_dict = {  # Dictionary to link OCR fields to priority\n", "    \"Name\": 1.0,\n", "    \"Address\": 1.0,\n", "    # \"streetname_name\": 0.5,\n", "    # \"suburb_name\": 0.5,\n", "    \"abn\": 1.0,\n", "    \"email\": 1.0,\n", "    \"web\": 0.25,\n", "    \"phone_home\": 0.5,\n", "    \"phone_work\": 0.5,\n", "    \"phone_mobile\": 0.5,\n", "    \"fax\": 0.5,\n", "}"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Service Provider data loaded...\n"]}], "source": ["##########################\n", "# Load and preprocess data\n", "##########################\n", "\n", "# Read in Service Provider raw data\n", "# serv_prov_raw = pd.read_csv(\"/workspaces/OCR_in_house/data/OCR_in_house/data/ref_service_provider.csv\")\n", "serv_prov_raw = pd.read_csv(ROOTDIR/ \"data/ref_service_provider_updated.csv\")\n", "serv_prov_raw = serv_prov_raw[serv_prov_raw['Is_Blocked']==0].reset_index(drop=True)\n", "serv_prov = serv_prov_raw.copy()  # create copy of raw data\n", "serv_prov = serv_prov.fillna(\"\")\n", "serv_prov[\"PostCode\"] = serv_prov[\"PostCode\"].astype(str)\n", "print(\"Service Provider data loaded...\")\n", "\n", "# Load list of fields to check in iteration\n", "field_list = list(sp_dict.keys())\n", "priority_scores = [priority_dict[field] for field in field_list]\n", "\n", "# Preprocessing of Service Provider List\n", "serv_prov[\"Address\"] = (\n", "    serv_prov[\"Address\"]\n", "    + \" \"\n", "    + serv_prov[\"City\"]\n", "    + \" \"\n", "    + serv_prov[\"State\"]\n", "    + \" \"\n", "    + serv_prov[\"PostCode\"]\n", ")  # Concat fields to form full Address\n", "for field in [\n", "    \"ServiceProviderName\",\n", "    \"Address\",\n", "    \"ABN\",\n", "    \"Email\",\n", "    \"HomePage\",\n", "    \"PhoneNo_Home\",\n", "    \"PhoneNo_Work\",\n", "    \"PhoneNo_Mobile\",\n", "    \"FaxNo\",\n", "]:\n", "    if field in [\"ServiceProviderName\", \"Address\"]:\n", "        serv_prov[field] = serv_prov[field].apply(preprocess)\n", "    elif field in [\"Email\", \"HomePage\"]:\n", "        serv_prov[field] = serv_prov[field].apply(preprocess_web)\n", "    elif field in [\n", "        \"PhoneNo_Home\",\n", "        \"PhoneNo_Work\",\n", "        \"PhoneNo_Mobile\",\n", "        \"FaxNo\",\n", "        \"PhoneNo_Home\",\n", "    ]:\n", "        serv_prov[field] = serv_prov[field].apply(preprocess_numbers)\n", "    elif field in ['ABN']:\n", "        serv_prov[field] = serv_prov[field].apply(lambda x: int(x) if x != \"\" and pd.notnull(x) else x)"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "abn_extract_regex = r\"(?:\\d *){11}\"\n", "abn_extract_regex1 = r\"\\d{2}-\\d{3}-\\d{3}-\\d{3}\"\n", "\n", "def validate_abn(nums: List[int]) -> bool:\n", "    if len(nums) != 11:\n", "        return False\n", "    if not all(isinstance(x, int) for x in nums):\n", "        return False\n", "    if any(x>9 for x in nums):\n", "        return False\n", "    if any(x<0 for x in nums):\n", "        return False\n", "\n", "    s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))\n", "    return s%89 == 0\n", "\n", "\n", "def extract_abn(info: Dict) -> List[str]:\n", "    content = info[\"content\"]\n", "    matches = re.findall(abn_extract_regex, content)\n", "    matches1 = re.findall(abn_extract_regex1, content)\n", "    ans = []\n", "    for match in matches + matches1:\n", "        match_num = []\n", "        for c in match:\n", "            try:\n", "                int_c = int(c)\n", "                match_num.append(int_c)\n", "            except ValueError:\n", "                continue\n", "        if validate_abn(match_num):\n", "            ans.append(match_num)\n", "\n", "    ans = list({\"\".join([str(x) for x in abn]) for abn in ans})\n", "    return ans\n", "    # info[\"ABN\"] = ans\n", "    # return info\n", "\n", "# get phone number \n", "def extract_fax_number(content: str) -> List[str]:\n", "    # work landline extraction\n", "    phone_number_regex = r\"\\(0\\d{1,2}\\) \\d{4} \\d{4}\"\n", "    matches = re.findall(phone_number_regex, content)\n", "    return matches\n", "\n", "def extract_fax_number(content: str) -> List[str]:\n", "    # Regex to capture Australian phone numbers including:\n", "    # 1. Landlines like (02) 9876 5432\n", "    # 2. Mobile numbers like 0412 345 678\n", "    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.\n", "    # Exclude international format like +61\n", "    \n", "    phone_number_regex = r\"(\\(0\\d{1,2}\\) \\d{4} \\d{4})|(\\(04\\d{2}\\) \\d{3} \\d{3})|(\\d{4} \\d{3} \\d{3})|13\\d{4}|1300 \\d{3} \\d{3}|1800 \\d{3} \\d{3}\"\n", "    \n", "    # Extract all matches\n", "    matches = re.findall(phone_number_regex, content)\n", "    \n", "    # Flatten the list and remove any empty strings\n", "    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]\n", "    \n", "    return flattened_matches\n", "\n", "def extract_phone_number(content: str) -> List[str]:\n", "    # Regex to capture Australian phone numbers including:\n", "    # 1. Landlines like (02) 9876 5432\n", "    # 2. Mobile numbers like 0412 345 678\n", "    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.\n", "    # Exclude international format like +61\n", "    \n", "    phone_number_regex = r\"(\\(0\\d{1,2}\\) \\d{4} \\d{4})|(\\(04\\d{2}\\) \\d{3} \\d{3})|(\\d{4} \\d{3} \\d{3})|13\\d{4}|1300 \\d{3} \\d{3}|1800 \\d{3} \\d{3}\"\n", "    \n", "    # Extract all matches\n", "    matches = re.findall(phone_number_regex, content)\n", "    \n", "    # Flatten the list and remove any empty strings\n", "    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]\n", "    \n", "    return flattened_matches\n", "\n", "def extract_mobile_number(content: str) -> List[str]:\n", "    # phone number extraction\n", "    phone_number_regex = r\"\\+?61[- ]?\\d{1,2}[- ]?\\d{4}[- ]?\\d{4}\"\n", "    matches = re.findall(phone_number_regex, content) \n", "    return matches\n", "\n", "def extract_email(content: str) -> List[str]:\n", "    # Replace newlines with spaces to normalize text\n", "    content = content.replace(\"\\n\", \" \")\n", "    \n", "    # Improved regex to capture emails after a colon or space\n", "    pattern = r\"(?<=[:\\s])([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})(?!\\S)\"\n", "    \n", "    matches = re.findall(pattern, content)\n", "    \n", "    return matches\n", "\n", "# get weblink\n", "def extract_weblink(content: str) -> List[str]:\n", "    # weblink extraction\n", "    weblink_regex = r\"(http|ftp|https)://([\\w_-]+(?:(?:\\.[\\w_-]+)+))([\\w.,@?^=%&:/~+#-]*[\\w@?^=%&/~+#-])?\"\n", "    matches = re.findall(weblink_regex, content)\n", "    return matches"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["# prepare service provider info\n", "def prepare_service_provider_info(info: Dict) -> Dict:\n", "    service_provider_info = {}\n", "    content = info[\"content\"]\n", "    service_provider_info[\"phone_home\"] = extract_phone_number(content)\n", "    service_provider_info[\"phone_work\"] = service_provider_info[\"phone_home\"]\n", "    service_provider_info[\"fax\"] = service_provider_info[\"phone_home\"]\n", "    service_provider_info[\"phone_mobile\"] = extract_mobile_number(content)\n", "    service_provider_info[\"email\"] = extract_email(content)\n", "    service_provider_info[\"web\"] = extract_weblink(content)\n", "    service_provider_info[\"abn\"] = extract_abn(info)\n", "    service_provider_info[\"Name\"] = info[\"service_provider\"]\n", "    service_provider_info[\"Address\"] = info[\"service_provider_address\"]\n", "    # service_provider_info[\"streetname_name\"] = info[\"service_provider_streetname_name\"]\n", "    # service_provider_info[\"suburb_name\"] = info[\"service_provider_suburb_name\"]\n", "    return service_provider_info"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["def fuzzy_match_service_provider(service_provider_info: Dict):\n", "    # Prepare list of match index and cosine values\n", "    match_index_list = []\n", "    match_cosine_list = []\n", "\n", "    # Begin iterating over field list\n", "    for field in field_list:\n", "        if (service_provider_info.get(field, None) is not None or service_provider_info[field] is not np.NaN) and service_provider_info.get(field, \"\"):\n", "            # fields stored in lists (numerical)\n", "            if field in [\"abn\", \"phone_home\", \"phone_work\", \"phone_mobile\", \"fax\"]:\n", "                query_list = [\n", "                    preprocess_numbers(query)\n", "                    for query in service_provider_info[field]\n", "                ]  # Convert string to list of integers\n", "                query_list = [query for query in query_list if query != 99999999]\n", "                # print(query_list)\n", "            # fields stored in lists (non-numerical)\n", "            elif field in [\"email\", \"web\"]:\n", "                query_list = [\n", "                    preprocess_web(query) for query in service_provider_info[field]\n", "                ]  # Convert string to list of strings\n", "            # # fields stored in lists (requires Fuzzy Search)\n", "            # elif field in [\"Name\", \"Address\"]:\n", "            #     query_list = [\n", "            #         query for query in [service_provider_info[field]]\n", "            #     ]  # multiple strings in list\n", "            #     # print(query_list)\n", "            #     # flatten entire fuzzy match list\n", "            #     fuzzy_list = []\n", "            #     for query in query_list:\n", "            #         fuzzy_list += FT_model[field].wv.most_similar(query, topn=top_n)\n", "            #     cosine_dict = {\n", "            #         query: 1.0 for query in query_list if query\n", "            #     }  # Prepare dictionary of cosine value mappings\n", "            #     query_fuzzy_list = [\n", "            #         item[0] for item in fuzzy_list if item[1] >= cos_l\n", "            #     ]  # only keep items above threshold\n", "            #     # print(fuzzy_list)\n", "            #     # Add mappings for cosing values\n", "            #     cosine_fuzzy_dict = {\n", "            #         item[0]: item[1] for item in fuzzy_list if item[1] >= cos_l\n", "            #     }  # only keep items above threshold\n", "            #     # combine all mappings\n", "            #     cosine_dict = {**cosine_dict, **cosine_fuzzy_dict}\n", "            #     # combine all queries\n", "            #     query_list = query_list + query_fuzzy_list\n", "            #     print(query)\n", "            #     print(cosine_dict)\n", "            #     print(query_list)\n", "                      # fields stored in lists (requires Fuzzy Search)\n", "            elif field in [\"Name\", \"Address\"]:\n", "                query_list = [\n", "                    query for query in [service_provider_info[field]]\n", "                ]  # multiple strings in list\n", "                \n", "                # Use fuzzywuzzy for fuzzy matching\n", "                fuzzy_list = []\n", "                for query in query_list:\n", "                    # Perform fuzzy matching with each item in the field_list\n", "                    for field_item in serv_prov[sp_dict[field]]:\n", "                        similarity_score = fuzz.ratio(query.lower(), field_item.lower())\n", "                        # print(query.lower(),field_item.lower(),similarity_score)\n", "                        if similarity_score >= cos_l * 100:  # Check if the score exceeds the threshold\n", "                            fuzzy_list.append((field_item, similarity_score))\n", "                            # print(fuzzy_list)\n", "                \n", "                # Sort by similarity score in descending order\n", "                fuzzy_list = sorted(fuzzy_list, key=lambda x: x[1], reverse=True)\n", "\n", "                # Get the top N results based on similarity score\n", "                query_fuzzy_list = [item[0] for item in fuzzy_list[:top_n]]\n", "                cosine_dict = {\n", "                    item[0]: item[1] / 100.0 for item in fuzzy_list[:top_n]\n", "                }  # Prepare dictionary of cosine value mappings\n", "                \n", "                # Combine the original query with the fuzzy results\n", "                query_list = query_list + query_fuzzy_list\n", "                # print(query)\n", "                # print(cosine_dict)\n", "                # print(query_list)\n", "                \n", "            # Fields stored in list (does not require Fuzzy search)\n", "            # elif field in [\"streetname_name\", \"suburb_name\"]:\n", "            #     query_list = [\n", "            #         preprocess(query) for query in [service_provider_info[field]]\n", "            #     ]  # Convert string to list of strings\n", "            # fields not stored in lists\n", "            else:\n", "                query_list = [preprocess(service_provider_info[field])]  # Put string in list\n", "\n", "            # # Build up index of matches\n", "            # if field in [\"streetname_name\", \"suburb_name\"] and len(query_list) > 0:\n", "            #     match_index = []\n", "            #     match_query = []\n", "            #     match_length = []\n", "            #     for query in query_list:  # Check all suburbs in query list\n", "            #         match_check = serv_prov[\n", "            #             sp_dict[field]\n", "            #         ].apply(\n", "            #             lambda x: query in x\n", "            #         )  # check if suburb or streetname in contained in list of SP names\n", "            #         match_index += serv_prov[\n", "            #             match_check\n", "            #         ].index.values.tolist()  # Append index list for each query\n", "            #         match_query += serv_prov[match_check][sp_dict[field]].tolist()\n", "            # print(query_list)\n", "            if field in [\"abn\"] and len(query_list) > 0:\n", "                match_index = []\n", "                match_query = []\n", "                match_length = []\n", "                for query in query_list:\n", "                    match_check = serv_prov[sp_dict[field]].isin(query_list)\n", "                    match_index += serv_prov[match_check].index.values.tolist()\n", "                    match_query += serv_prov[match_check][sp_dict[field]].tolist()\n", "                    match_length += [\n", "                        len(match_index) for index in range(0, len(match_index))\n", "                    ]  # Scaling of cosine score from number of matches returned\n", "                    # print(match_query)\n", "            else:\n", "                match_check = serv_prov[sp_dict[field]].isin(query_list)\n", "                match_index = serv_prov[match_check].index.values.tolist()\n", "                match_query = serv_prov[match_check][sp_dict[field]].tolist()\n", "                # print(match_check,match_index,match_query)\n", "            \n", "            # Map to cosine values\n", "            if field in [\"Name\", \"Address\"]:\n", "                match_dict = dict(\n", "                    zip(match_index, [cosine_dict[item] for item in match_query])\n", "                )\n", "                match_cosine = [match_dict[idx] for idx in match_index]\n", "            # elif field in [\"abn\"]:\n", "            #     # print('abn')\n", "            #     match_cosine = [\n", "            #         1.0 / (A_log * np.log(match_length[idx]) + 1)\n", "            #         for idx in range(0, len(match_index))\n", "            #     ]\n", "                # print(match_cosine)\n", "            else:\n", "                match_cosine = [1.0 for idx in range(0, len(match_index))]\n", "            match_index_list.append(match_index)\n", "            match_cosine_list.append(match_cosine)\n", "        else:\n", "            match_index_list.append([])\n", "            match_cosine_list.append([])\n", "\n", "    # Flatten entire match index list\n", "    flat_index_list = [ind for sublist in match_index_list for ind in sublist]\n", "\n", "    # Calculate counts, priority score and entities matched\n", "    count_list = []\n", "    for ind in set(flat_index_list):\n", "        priority_score = 0\n", "        fields_matched = []\n", "        for n, match_index in enumerate(match_index_list):\n", "            if ind in match_index:\n", "                cosine_score = match_cosine_list[n][match_index.index(ind)]\n", "                priority_score += priority_scores[n] * cosine_score\n", "                fields_matched.append(field_list[n])\n", "        count_list.append(\n", "            (ind, flat_index_list.count(ind), priority_score, fields_matched)\n", "        )\n", "\n", "    # Keep Top 5 best matches in descending order of priority score\n", "    sorted_count_list = sorted(count_list, key=lambda tup: (tup[2], len(tup[3])), reverse=True)[\n", "        :5\n", "    ]  # Sort by match count/ match priority?\n", "    # print(sorted_count_list)\n", "    # print(sorted_count_list)\n", "\n", "    # Print best and second best matches to file\n", "\n", "    # Initialize the second-best match variables\n", "    best_match_count_2 = 0\n", "    best_match_priority_2 = 0\n", "    best_match_fields_2 = None\n", "    best_match_list_2 = [None]\n", "\n", "    if len(sorted_count_list) > 1:\n", "        best_match_index = int([item[0] for item in sorted_count_list][0])\n", "        best_match_count = int([item[1] for item in sorted_count_list][0])\n", "        best_match_priority = [item[2] for item in sorted_count_list][0]\n", "        best_match_fields = [item[3] for item in sorted_count_list][0]\n", "        best_match_list = (\n", "            serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "            .iloc[best_match_index]\n", "            .values.tolist()\n", "        )\n", "\n", "        # Check for a tie in priority score and resolve by field match count\n", "        best_match_index_2 = int([item[0] for item in sorted_count_list][1])\n", "        best_match_fields_2 = [item[3] for item in sorted_count_list][1]\n", "        best_match_priority_2 = [item[2] for item in sorted_count_list][1]\n", "        best_match_list_2 = (\n", "            serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "            .iloc[best_match_index_2]\n", "            .values.tolist()\n", "        )\n", "\n", "        # If priority scores are the same, compare the number of fields matched\n", "        if best_match_priority == best_match_priority_2:\n", "            if len(best_match_fields) < len(best_match_fields_2):\n", "                best_match_index_2 = int([item[0] for item in sorted_count_list][1])\n", "                best_match_fields_2 = [item[3] for item in sorted_count_list][1]\n", "                best_match_list_2 = (\n", "                    serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "                    .iloc[best_match_index_2]\n", "                    .values.tolist()\n", "                )\n", "            # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index_2].values.tolist()\n", "    \n", "    # Single Match\n", "    elif len(sorted_count_list) == 1:\n", "        best_match_index = int([item[0] for item in sorted_count_list][0])\n", "        best_match_count = int([item[1] for item in sorted_count_list][0])\n", "        best_match_priority = [item[2] for item in sorted_count_list][0]\n", "        best_match_fields = [item[3] for item in sorted_count_list][0]\n", "        best_match_list = (\n", "            serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "            .iloc[best_match_index]\n", "            .values.tolist()\n", "        )  # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index].values.tolist()\n", "\n", "        best_match_index_2 = None\n", "        best_match_count_2 = 0\n", "        best_match_priority_2 = 0\n", "        best_match_fields_2 = None\n", "        best_match_list_2 = [None]\n", "    # No matches\n", "    else:\n", "        best_match_index = None\n", "        best_match_count = 0\n", "        best_match_priority = 0\n", "        best_match_fields = None\n", "        best_match_list = [\"\",\"\"]\n", "\n", "        best_match_index_2 = None\n", "        best_match_count_2 = 0\n", "        best_match_priority_2 = None\n", "        best_match_fields_2 = None\n", "        best_match_list_2 = [None]\n", "\n", "    # export to list\n", "    return {\n", "        \"best_match_list\": best_match_list,\n", "        \"best_match_evidence\": (best_match_fields, best_match_count, best_match_priority),\n", "        \"best_match_list2\": best_match_list_2,\n", "        \"best_match_evidence2\": (best_match_fields_2, best_match_count_2, best_match_priority_2),\n", "        \"list\" : sorted_count_list\n", "    }"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["for docfile, info in document_intelligence_rule_res.items():\n", "    service_provider_info = prepare_service_provider_info(info[0])\n", "    service_provider_info.update(fuzzy_match_service_provider(service_provider_info))\n", "    info[0][\"service_provider_info\"] = service_provider_info\n", "    info[0][\"service_provider_fm\"] = info[0][\"service_provider_info\"]['best_match_list'][1]\n", "    info[0][\"service_provider_no_fm\"] = info[0][\"service_provider_info\"]['best_match_list'][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Gathering\n", "* gather the loaded data per invoice\n", "* reformat the data for downstream usage"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["# prepare claimno file name mapping\n", "def map_claimno_docfile(df: pd.DataFrame = doc_path) -> Tuple[Dict, Dict]:\n", "    claimno2docfile = {}\n", "    docfile2claimno = {}\n", "\n", "    for claimno, docfile in zip(df[\"ClaimNumber\"], df[\"DocFile\"]):\n", "        docfile = docfile.lower()\n", "        if claimno not in claimno2docfile:\n", "            claimno2docfile[claimno] = docfile.lower()\n", "\n", "        if docfile not in docfile2claimno:\n", "            docfile2claimno[docfile.lower()] = claimno\n", "\n", "    return claimno2docfile, docfile2claimno\n", "claimno2docfile, docfile2claimno = map_claimno_docfile(doc_path)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["(172, 185)"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["len(claimno2docfile), len(docfile2claimno)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Dict, Tu<PERSON>, List"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["a = json.loads(df_truuth['OCRResponseObject'].iloc[0])"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'VetXmlClaim': {'InfoFromPolicyHolder': {'AnimalDetails': {'Name': None}},\n", "  'InfoFromVet': {'Vet': {'VetName': None,\n", "    'PracticeName': 'Burvale Heights Vet Hospital',\n", "    'PracticeNameConfidence': '0.85',\n", "    'PracticeId': 'CT0000987',\n", "    'PracticeIdConfidence': '0.85'},\n", "   'Conditions': [{'Financial': {'TotalExVAT': None,\n", "      'VAT': None,\n", "      'TotalIncVat': '140',\n", "      'TotalIncVatConfidence': '0.2125',\n", "      'Invoices': [{'InvoiceNumber': 'B243142539',\n", "        'InvoiceNumberConfidence': '0.99',\n", "        'Date': '2024-03-14T00:00:00',\n", "        'DateConfidence': '1',\n", "        'TotalExVat': None,\n", "        'VAT': None,\n", "        'TotalIncVat': '140',\n", "        'TotalIncVatConfidence': '0.2125',\n", "        'Items': [{'TreatmentDate': '2024-03-14T00:00:00',\n", "          'TreatmentDateConfidence': '1',\n", "          'ItemCode': None,\n", "          'Sequence': '1',\n", "          'Description': 'Vaccine Protech C5',\n", "          'DescriptionConfidence': '0.99',\n", "          'AmountExVAT': None,\n", "          'DiscountExVaT': None,\n", "          'VAT': None,\n", "          'Quantity': None,\n", "          'TotalIncVAT': '140',\n", "          'TotalIncVATConfidence': '1'}]}]}}],\n", "   'DocumentInfo': {'Confidence': {'DocumentConfidence': '0.385'},\n", "    'ProbabilityOfError': None,\n", "    'ErrorCode': [{'Code': '108',\n", "      'Description': 'Invoice Number B243142539 : Multiple Pets Found in the Invoice.'}]}}}}"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "from typing import Dict, Any, Optional, List\n", "\n", "def flatten_ocr_response(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Flatten the OCRResponseObject column into separate columns.\n", "    Creates one row per treatment item, with multiple rows per original record if there are multiple invoices/treatments.\n", "    \n", "    Args:\n", "        df: DataFrame containing 'OCRResponseObject', 'ClaimNumber', and 'OCRDocumentConfidence' columns\n", "        \n", "    Returns:\n", "        DataFrame with flattened OCR response fields as separate columns\n", "    \"\"\"\n", "    count = 0\n", "    \n", "    def extract_ocr_fields(ocr_obj: Any) -> List[Dict[str, Any]]:\n", "        \"\"\"Extract fields from a single OCR response object, returning a list of records for each treatment item.\"\"\"\n", "        \n", "        # Initialize base fields that are common across all treatment items\n", "        base_fields = {\n", "            'ServiceProvider': None,\n", "            'ServiceProviderNo': None,\n", "            'ServiceProvider_conf': None,\n", "            'TotalAmount': None,\n", "            'TotalAmount_conf': None,\n", "            'Tru<PERSON>_Error': None\n", "        }\n", "        \n", "        # Initialize treatment-specific fields\n", "        treatment_fields_template = {\n", "            'InvoiceDate': None,\n", "            'InvoiceDate_conf': None,\n", "            'InvoiceNumber': None,\n", "            'InvoiceNumber_conf': None,\n", "            'TreatmentDate': None,\n", "            'TreatmentDate_conf': None,\n", "            'TreatmentDescription': None,\n", "            'TreatmentDescription_conf': None,\n", "            'TreatmentAmount': None,\n", "            'TreatmentAmount_conf': None,\n", "        }\n", "        \n", "        try:\n", "            # Convert to dict if it's a string\n", "            if isinstance(ocr_obj, str):\n", "                ocr_data = json.loads(ocr_obj)\n", "            elif isinstance(ocr_obj, dict):\n", "                ocr_data = ocr_obj\n", "            else:\n", "                # Return single record with all None values if can't parse\n", "                return [{**base_fields, **treatment_fields_template}]\n", "            \n", "            # Navigate through the nested structure\n", "            vet_xml_claim = ocr_data.get('VetXmlClaim', {})\n", "            info_from_vet = vet_xml_claim.get('InfoFromVet', {})\n", "            vet_info = info_from_vet.get('Vet', {})\n", "            \n", "            # Extract ServiceProvider fields (common across all records)\n", "            base_fields['ServiceProvider'] = vet_info.get('PracticeName')\n", "            base_fields['ServiceProviderNo'] = vet_info.get('PracticeId')\n", "            base_fields['ServiceProvider_conf'] = vet_info.get('PracticeNameConfidence')\n", "            \n", "            # Extract error information (common across all records)\n", "            document_info = vet_xml_claim.get('DocumentInfo', {})\n", "            error_codes = document_info.get('ErrorCode', [])\n", "            if error_codes:\n", "                # Combine all error descriptions\n", "                error_descriptions = [error.get('Description', '') for error in error_codes if error.get('Description')]\n", "                base_fields['Tru<PERSON>_Error'] = '; '.join(error_descriptions) if error_descriptions else None\n", "            \n", "            # Extract invoice and treatment details from Conditions\n", "            conditions = info_from_vet.get('Conditions', [])\n", "            \n", "            all_treatment_records = []\n", "            \n", "            if conditions:\n", "                # Loop through all conditions\n", "                for condition in conditions:\n", "                    financial = condition.get('Financial', {})\n", "                    \n", "                    # Extract total amount (at condition level)\n", "                    base_fields['TotalAmount'] = financial.get('TotalIncVat')\n", "                    base_fields['TotalAmount_conf'] = financial.get('TotalIncVatConfidence')\n", "                    \n", "                    # Loop through all invoices\n", "                    invoices = financial.get('Invoices', [])\n", "                    for invoice in invoices:\n", "                        invoice_number = invoice.get('InvoiceNumber')\n", "                        invoice_number_conf = invoice.get('InvoiceNumberConfidence')\n", "                        invoice_date = invoice.get('Date')\n", "                        invoice_date_conf = invoice.get('DateConfidence')\n", "                        \n", "                        # Loop through all treatment items in this invoice\n", "                        items = invoice.get('Items', [])\n", "                        if items:\n", "                            for item in items:\n", "                                treatment_record = {\n", "                                    **base_fields,\n", "                                    'InvoiceNumber': invoice_number,\n", "                                    'InvoiceNumber_conf': invoice_number_conf,\n", "                                    'InvoiceDate': invoice_date,\n", "                                    'InvoiceDate_conf': invoice_date_conf,\n", "                                    'TreatmentDate': item.get('TreatmentDate'),\n", "                                    'TreatmentDate_conf': item.get('TreatmentDateConfidence'),\n", "                                    'TreatmentDescription': item.get('Description'),\n", "                                    'TreatmentDescription_conf': item.get('DescriptionConfidence'),\n", "                                    'TreatmentAmount': item.get('TotalIncVAT'),\n", "                                    'TreatmentAmount_conf': item.get('TotalIncVATConfidence'),\n", "                                }\n", "                                all_treatment_records.append(treatment_record)\n", "                        else:\n", "                            # If no items, still create a record with invoice info\n", "                            treatment_record = {\n", "                                **base_fields,\n", "                                **treatment_fields_template,\n", "                                'InvoiceNumber': invoice_number,\n", "                                'InvoiceNumber_conf': invoice_number_conf,\n", "                                'InvoiceDate': invoice_date,\n", "                                'InvoiceDate_conf': invoice_date_conf,\n", "                            }\n", "                            all_treatment_records.append(treatment_record)\n", "            \n", "            # If no records were created, return a single record with base fields\n", "            if not all_treatment_records:\n", "                all_treatment_records = [{**base_fields, **treatment_fields_template}]\n", "            \n", "            return all_treatment_records\n", "            \n", "        except (j<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IndexError) as e:\n", "            # If any error occurs during parsing, return single record with None values\n", "            print(count)\n", "            print(f\"Error parsing OCR response: {e}\")\n", "            return [{**base_fields, **treatment_fields_template}]\n", "    count += 1\n", "    # Process each row and collect all treatment records\n", "    all_records = []\n", "    \n", "    for idx, row in df.iterrows():\n", "        # Extract OCR fields (returns list of treatment records)\n", "        treatment_records = extract_ocr_fields(row['OCRResponseObject'])\n", "        \n", "        # Add the original row data to each treatment record\n", "        for treatment_record in treatment_records:\n", "            combined_record = {\n", "                'ClaimNumber': row['ClaimNumber'],\n", "                'OCRDocumentConfidence': row['OCRDocumentConfidence'],\n", "                **treatment_record\n", "            }\n", "            all_records.append(combined_record)\n", "    \n", "    # Create DataFrame from all records\n", "    result_df = pd.DataFrame(all_records)\n", "    \n", "    # Define column order\n", "    column_order = [\n", "        'ClaimNumber',\n", "        'OCRDocumentConfidence',\n", "        'ServiceProvider',\n", "        'ServiceProviderNo',\n", "        'ServiceProvider_conf',\n", "        'InvoiceDate',\n", "        'InvoiceDate_conf',\n", "        'InvoiceNumber',\n", "        'InvoiceNumber_conf',\n", "        'TreatmentDate',\n", "        'TreatmentDate_conf',\n", "        'TreatmentDescription',\n", "        'TreatmentDescription_conf',\n", "        'TreatmentAmount',\n", "        'TreatmentAmount_conf',\n", "        'TotalAmount',\n", "        'TotalAmount_conf',\n", "        'T<PERSON><PERSON>_Error'\n", "    ]\n", "    \n", "    return result_df[column_order]\n", "\n", "# Example usage:\n", "# df_flattened = flatten_ocr_response(df)\n", "# print(f\"Original DataFrame shape: {df.shape}\")\n", "# print(f\"Flattened DataFrame shape: {df_flattened.shape}\")\n", "# print(df_flattened.head())"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "Error parsing OCR response: Expecting value: line 1 column 1 (char 0)\n"]}], "source": ["df_truuth_processed = flatten_ocr_response(df_truuth)\n", "# df_truuth_processed.head()"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["def parse_truuth_info(df: pd.DataFrame) -> Dict:\n", "    ans = {}\n", "\n", "    for info in df.to_dict(orient=\"records\"):\n", "        claim_no = info[\"ClaimNumber\"]\n", "        ocr_confidence = info[\"OCRDocumentConfidence\"]\n", "        service_provider = info[\"ServiceProvider\"]\n", "        service_provider_no = info[\"ServiceProviderNo\"]\n", "        service_provider_conf = info[\"ServiceProvider_conf\"]\n", "        invoice_date = info[\"InvoiceDate\"]\n", "        invoice_date_conf = info[\"InvoiceDate_conf\"]\n", "        invoice_no = info[\"InvoiceNumber\"]\n", "        invoice_no_conf = info[\"InvoiceNumber_conf\"]\n", "        treatment_date = info[\"TreatmentDate\"]\n", "        treatment_date_conf = info[\"TreatmentDate_conf\"]\n", "        treatment_description = info[\"TreatmentDescription\"]\n", "        treatment_description_conf = info[\"TreatmentDescription_conf\"]\n", "        treatment_amount = info[\"TreatmentAmount\"]\n", "        treatment_amount_conf = info[\"TreatmentAmount_conf\"]\n", "        total_amount = info[\"TotalAmount\"]\n", "        total_amount_conf = info[\"TotalAmount_conf\"]\n", "        truuth_error = info[\"Truuth_Error\"]\n", " \n", "        if claim_no not in ans:\n", "            ans[claim_no] = {\n", "                \"claim_no\": claim_no,\n", "                \"ocr_confidence\": ocr_confidence,\n", "                \"service_provider\": service_provider,\n", "                \"service_provider_no\": service_provider_no,\n", "                \"service_provider_conf\": service_provider_conf,\n", "                \"invoice_date\": invoice_date,\n", "                \"invoice_date_conf\": invoice_date_conf,\n", "                \"invoice_no\": invoice_no,\n", "                \"invoice_no_conf\": invoice_no_conf,\n", "                \"total_amount\": total_amount,\n", "                \"total_amount_conf\": total_amount_conf,\n", "                \"truuth_error\": truuth_error,\n", "\n", "                \"treatments\": [\n", "                    {\n", "                        \"treatment_date\": treatment_date,\n", "                        \"treatment_date_conf\": treatment_date_conf,\n", "                        \"treatment_description\": treatment_description,\n", "                        \"treatment_description_conf\": treatment_description_conf,\n", "                        \"treatment_amount\": treatment_amount,\n", "                        \"treatment_amount_conf\": treatment_amount_conf,\n", "                    }\n", "                ]\n", "            }\n", "        else:\n", "            ans[claim_no][\"treatments\"].append(\n", "                {\n", "                    \"treatment_date\": treatment_date,\n", "                        \"treatment_date_conf\": treatment_date_conf,\n", "                        \"treatment_description\": treatment_description,\n", "                        \"treatment_description_conf\": treatment_description_conf,\n", "                        \"treatment_amount\": treatment_amount,\n", "                        \"treatment_amount_conf\": treatment_amount_conf\n", "                }\n", "            )\n", "    return ans"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["truuth_info_dict = parse_truuth_info(df_truuth_processed)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def gather_all_info_truuth_di(di_res_dict: Dict,\n", "                    truuth_info_dict: Dict,\n", "                    paddleocr_res_dict: Dict,\n", "                    claimno2docfile: Dict,\n", "                    docfile2claimno: Dict):\n", "\n", "    summary_list = []\n", "\n", "    # Threshold for considering treatments as similar\n", "    TREATMENT_SIMILARITY_THRESHOLD = 0.7\n", "\n", "    # files processed by Azure Document Intelligence\n", "    for k, v in di_res_dict.items():\n", "        docfile = k\n", "        invoices = v\n", "\n", "        mapping_claimno = docfile2claimno[docfile.lower()]\n", "\n", "        if not mapping_claimno:\n", "            logger.warning(f\"No claim number mapping found for docfile: {docfile}. Skipping.\")\n", "            continue\n", "\n", "        logger.info(f\"Gather infomation: {mapping_claimno} {docfile}\")\n", "\n", "        paddleocr_content = paddleocr_res_dict[docfile.lower()][\"content\"]\n", "\n", "        if len(invoices) > 1:\n", "            print(k, mapping_claimno, len(invoices))\n", "            print(\"-\"*40)\n", "        \n", "        # Check if the claim number exists in truuth_res_dict\n", "        if mapping_claimno not in truuth_info_dict:\n", "            logger.warning(f\"Claim number {mapping_claimno} not found in Truuth data. Processing with empty Truuth data.\")\n", "            # Create empty Truuth info with default values\n", "            truuth_info = {\n", "                \"claim_no\": mapping_claimno,\n", "                \"invoice_no\": \"\",\n", "                \"invoice_date\": None,\n", "                \"invoice_total\": 0,\n", "                \"service_provider\": \"\",\n", "                \"service_provider_no\": \"\",\n", "                \"service_provider_evidence\": \"\",\n", "                \"service_provider2\": \"\",\n", "                \"service_provider_no2\": \"\",\n", "                \"service_provider_evidence2\": \"\",\n", "                \"treatments\": [\n", "                    {\n", "                        \"date_treatment\": \"\",\n", "                        \"treatment\": \"\",\n", "                        \"amount\": \"\",\n", "                    }\n", "                ]\n", "            }\n", "        else:\n", "            truuth_info = truuth_info_dict[mapping_claimno]\n", "            \n", "        for invoice in invoices:\n", "            # check invoice no\n", "            di_invoice_no = invoice[\"invoice_no\"]\n", "            truuth_invoice_no = str(truuth_info[\"invoice_no\"])\n", "            di_invoice_no_correct = int(di_invoice_no == truuth_invoice_no)\n", "    \n", "            # check service provider\n", "            di_service_provider = invoice[\"service_provider_fm\"]\n", "            di_service_provider_no = invoice[\"service_provider_no_fm\"]\n", "            di_service_provider_evidence = invoice[\"service_provider_evidence\"]\n", "            di_service_provider2 = invoice[\"service_provider_fm2\"]\n", "            di_service_provider_no2 = invoice[\"service_provider_no_fm2\"]\n", "            di_service_provider_evidence2 = invoice[\"service_provider_evidence2\"]\n", "            di_service_provider_address = invoice[\"service_provider_address\"]\n", "            truuth_service_provider = truuth_info[\"service_provider\"]\n", "            truuth_service_provider_no = truuth_info[\"service_provider_no\"]\n", "\n", "\n", "            di_service_provider_correct = int(di_service_provider_no == truuth_service_provider_no)\n", "            \n", "            # check invoice date TODO cannot check Truuth results now 15OCT24\n", "            di_invoice_date = invoice[\"invoice_date\"].isoformat() if not isinstance(invoice[\"invoice_date\"], str) else invoice[\"invoice_date\"]\n", "            if truuth_info[\"invoice_date\"]:\n", "                print(truuth_info[\"invoice_date\"])\n", "                parsed_date = datetime.fromisoformat(truuth_info[\"invoice_date\"])\n", "                truuth_invoice_date = parsed_date.date().isoformat()\n", "            else:\n", "                truuth_invoice_date = \"\"\n", "            # if truuth_info[\"invoice_date\"] != truuth_info[\"invoice_date\"].date().isoformat():\n", "            #     print(\"invoice_date mismatch\", k, mapping_claimno)\n", "            di_invoice_date_correct = int(di_invoice_date == truuth_invoice_date)\n", "\n", "            # check total amount ### TODO\n", "            di_total_amount = invoice[\"invoice_total\"]\n", "            truuth_total_amount = truuth_info[\"total_amount\"]  \n", "            # if stat_info.get(\"invoice_total_truuth\", -1) != truuth_info.get(\"invoice_total\", None):\n", "            #     print(\"invoice_total mismatch\", k, mapping_claimno)\n", "            di_total_amount_correct = int(abs(di_total_amount - truuth_total_amount) < 0.05)\n", "\n", "            summary = {\n", "                \"claimno\": mapping_claimno,\n", "                \"docfile\": docfile,\n", "                # \"paddleocr_content\": paddleocr_content,\n", "\n", "                \"di_conf\": min(invoice[\"invoice_no_conf\"], invoice[\"invoice_date_conf\"], invoice[\"invoice_total_conf\"]),\n", "\n", "                \"di_invoice_no\": di_invoice_no,\n", "                \"truuth_invoice_no\": truuth_invoice_no,\n", "                \"di_invoice_no_correct\": di_invoice_no_correct,\n", "                \"di_invoice_no_conf\": invoice[\"invoice_no_conf\"],\n", "                \"truuth_invoice_no_conf\": truuth_info.get(\"invoice_no_conf\", 0.0),\n", "\n", "                \"di_service_provider\": di_service_provider,\n", "                \"di_service_provider_conf\": invoice[\"service_provider_conf\"],\n", "                \"di_service_provider_no\": di_service_provider_no,\n", "                \"di_service_provider_evidence\": di_service_provider_evidence,\n", "                \"truuth_service_provider\": truuth_service_provider,\n", "                \"truuth_service_provider_conf\": truuth_info.get(\"service_provider_conf\", 0.0),\n", "                \"di_service_provider_correct\": di_service_provider_correct,\n", "                \"di_service_provider_fm2\": di_service_provider2,\n", "                \"di_service_provider_no_fm2\": di_service_provider2,\n", "                \"di_service_provider_evidence2\": di_service_provider_evidence2,\n", "                # \"di_service_provider_address\": di_service_provider_address\n", "                # \"di_abn\": invoice[\"ABN\"],\n", "\n", "                \"di_invoice_date\": di_invoice_date,\n", "                \"truuth_invoice_date\": truuth_invoice_date,\n", "                \"di_invoice_date_correct\": di_invoice_date_correct,\n", "                \"di_invoice_date_conf\": invoice[\"invoice_date_conf\"],\n", "                \"truuth_invoice_date_conf\": truuth_info.get(\"invoice_date_conf\", 0.0),\n", "\n", "                \"di_total_amount\": di_total_amount,\n", "                \"truuth_total_amount\": truuth_total_amount,\n", "                \"di_total_amount_correct\": di_total_amount_correct,\n", "                \"di_total_amount_conf\": invoice[\"invoice_total_conf\"],\n", "                \"truuth_total_amount_conf\": truuth_info.get(\"total_amount_conf\", 0.0),\n", "\n", "                \"rule_res\": invoice[\"rule_res\"],\n", "                \"if_fallout\": int(\"FALL OUT\" in invoice[\"rule_res\"]),\n", "                \"truuth_conf\" : truuth_info.get(\"ocr_confidence\",0),\n", "                \"truuth_error\" : truuth_info.get(\"truuth_error\",0)\n", "            }\n", "\n", "            sorted_di_treatments = sorted(invoice[\"treatments\"], key=lambda x: x[\"amount\"])\n", "            sorted_truuth_treatments = sorted(truuth_info[\"treatments\"], key=lambda x: x[\"amount\"])\n", "            treatments = []\n", "            di_pointer, truuth_pointer = 0, 0\n", "            while di_pointer < len(sorted_di_treatments) or truuth_pointer < len(sorted_truuth_treatments):\n", "                try:\n", "                    di_treatment = sorted_di_treatments[di_pointer]\n", "                except IndexError:\n", "                    assert di_pointer == len(sorted_di_treatments)\n", "                    di_treatment = {}\n", "                try:\n", "                    truuth_treatment = sorted_truuth_treatments[truuth_pointer]\n", "                except IndexError:\n", "                    assert truuth_pointer == len(sorted_truuth_treatments)\n", "                    truuth_treatment = {}\n", "                # logger.info(di_treatment)\n", "\n", "                # logger.info(upm_treatment)\n", "                if not di_treatment and not truuth_treatment:\n", "                    logger.error(f\"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.\")\n", "                    logger.error(f\"sorted_di_treatments: {sorted_di_treatments}\")\n", "                    logger.error(f\"sorted_truuth_treatments: {sorted_truuth_treatments}\")\n", "                    logger.error(f\"di_pointer: {di_pointer}, truuth_pointer: {truuth_pointer}\")\n", "                    break\n", "\n", "                if not di_treatment:\n", "                    if truuth_treatment[\"treatment_date\"]:\n", "                        parsed_date = datetime.fromisoformat(truuth_treatment[\"treatment_date\"])\n", "                        truuth_treatment_date = parsed_date.date().isoformat()\n", "                    treatments.append({\n", "                        \"di_treatment_date\": \"\",\n", "                        \"di_treatment\": \"\",\n", "                        \"di_amount\": \"\",\n", "                        \"di_treatment_date_conf\": \"\",\n", "                        \"di_treatment_conf\": \"\",\n", "                        \"di_amount_conf\": \"\",\n", "\n", "                        \"truuth_treatment_date\": truuth_treatment_date,\n", "                        \"truuth_treatment\": truuth_treatment[\"treatment_description\"],\n", "                        \"truuth_amount\": truuth_treatment[\"treatment_amount\"],\n", "                        \"truuth_treatment_date_conf\": truuth_treatment[\"treatment_date_conf\"],\n", "                        \"truuth_amount_conf\": truuth_treatment[\"treatment_amount_conf\"],\n", "                        \"truuth_treatment_conf\": truuth_treatment[\"treatment_description_conf\"],\n", "\n", "                        \"di_treatment_amount_correct\" : \"\",\n", "                        \"di_treatment_date_correct\" : \"\"\n", "                    })\n", "                    truuth_pointer += 1\n", "                    continue\n", "\n", "                if not truuth_treatment:\n", "                    di_treatment_date = di_treatment[\"treatment_date\"].isoformat() if not isinstance(di_treatment[\"treatment_date\"], str) else di_treatment[\"treatment_date\"]\n", "\n", "                    treatments.append({\n", "                        \"di_treatment_date\": di_treatment_date,\n", "                        \"di_treatment\": di_treatment[\"treatment\"],\n", "                        \"di_amount\": di_treatment[\"amount\"],\n", "                        \"di_treatment_date_conf\": di_treatment[\"treatment_date_conf\"],\n", "                        \"di_treatment_conf\": di_treatment[\"treatment_conf\"],\n", "                        \"di_amount_conf\": di_treatment[\"amount_conf\"],\n", "\n", "                        \"truuth_treatment_date\": \"\",\n", "                        \"truuth_treatment\": \"\",\n", "                        \"truuth_amount\": \"\",\n", "                        \"truuth_treatment_date_conf\": \"\",\n", "                        \"truuth_amount_conf\": \"\",\n", "                        \"truuth_treatment_conf\": \"\",\n", "\n", "                        \"di_treatment_amount_correct\" : 0,\n", "                        \"di_treatment_date_correct\" : 0\n", "                    })\n", "                    di_pointer += 1\n", "                    continue\n", "\n", "                if abs(di_treatment[\"amount\"] - truuth_treatment[\"amount\"]) < 0.05:\n", "                    di_treatment_date = di_treatment[\"treatment_date\"].isoformat() if not isinstance(di_treatment[\"treatment_date\"], str) else di_treatment[\"treatment_date\"]\n", "                    if truuth_treatment[\"treatment_date\"]:\n", "                        parsed_date = datetime.fromisoformat(truuth_treatment[\"treatment_date\"])\n", "                        truuth_treatment_date = parsed_date.date().isoformat()\n", "                    di_treatment_date_correct = int(di_treatment_date == truuth_treatment_date)\n", "\n", "                    # Add treatment text similarity calculation\n", "                    treatment_similarity = get_text_similarity(di_treatment[\"treatment\"], truuth_treatment[\"treatment_description\"])\n", "                    di_treatment_text_correct = int(treatment_similarity >= TREATMENT_SIMILARITY_THRESHOLD)\n", "\n", "                    treatments.append({\n", "                        \"di_treatment_date\": di_treatment_date,\n", "                        \"di_treatment\": di_treatment[\"treatment\"],\n", "                        \"di_amount\": di_treatment[\"amount\"],\n", "                        \"di_treatment_date_conf\": di_treatment[\"treatment_date_conf\"],\n", "                        \"di_treatment_conf\": di_treatment[\"treatment_conf\"],\n", "                        \"di_amount_conf\": di_treatment[\"amount_conf\"],\n", "\n", "                        \"truuth_treatment_date\": truuth_treatment_date,\n", "                        \"truuth_treatment\": truuth_treatment[\"treatment_description\"],\n", "                        \"truuth_amount\": truuth_treatment[\"treatment_amount\"],\n", "                        \"truuth_treatment_date_conf\": truuth_treatment[\"treatment_date_conf\"],\n", "                        \"truuth_amount_conf\": truuth_treatment[\"treatment_amount_conf\"],\n", "                        \"truuth_treatment_conf\": truuth_treatment[\"treatment_description_conf\"],\n", "\n", "                        \"di_treatment_amount_correct\" : 1,\n", "                        \"di_treatment_date_correct\" : di_treatment_date_correct,\n", "                        \"di_treatment_desc_correct\": di_treatment_text_correct,\n", "                        \"di_treatment_similarity\": treatment_similarity\n", "                    })\n", "                    di_pointer += 1\n", "                    truuth_pointer += 1\n", "                elif di_treatment[\"amount\"] < truuth_treatment[\"treatment_amount\"]:\n", "\n", "                    di_treatment_date = parsed_date.date().isoformat()\n", "                    treatments.append({\n", "                        \"di_treatment_date\": di_treatment_date,\n", "                        \"di_treatment\": di_treatment[\"treatment\"],\n", "                        \"di_amount\": di_treatment[\"amount\"],\n", "                        \"di_treatment_date_conf\": di_treatment[\"treatment_date_conf\"],\n", "                        \"di_treatment_conf\": di_treatment[\"treatment_conf\"],\n", "                        \"di_amount_conf\": di_treatment[\"amount_conf\"],\n", "\n", "                        \"truuth_treatment_date\": \"\",\n", "                        \"truuth_treatment\": \"\",\n", "                        \"truuth_amount\": \"\",\n", "                        \"truuth_treatment_date_conf\": \"\",\n", "                        \"truuth_amount_conf\": \"\",\n", "                        \"truuth_treatment_conf\": \"\",\n", "\n", "                        \"di_treatment_amount_correct\" : 0,\n", "                        \"di_treatment_date_correct\" : 0,\n", "                        \"di_treatment_desc_correct\": 0,\n", "                        \"di_treatment_similarity\": 0\n", "                    })\n", "                    di_pointer += 1\n", "                else:\n", "                    if truuth_treatment[\"treatment_date\"]:\n", "                        parsed_date = datetime.fromisoformat(truuth_treatment[\"treatment_date\"])\n", "                        truuth_treatment_date = parsed_date.date().isoformat()\n", "                    treatments.append({\n", "                        \"di_treatment_date\": \"\",\n", "                        \"di_treatment\": \"\",\n", "                        \"di_amount\": \"\",\n", "                        \"di_treatment_date_conf\": \"\",\n", "                        \"di_treatment_conf\": \"\",\n", "                        \"di_amount_conf\": \"\",\n", "\n", "                        \"truuth_treatment_date\": truuth_treatment_date,\n", "                        \"truuth_treatment\": truuth_treatment[\"treatment_description\"],\n", "                        \"truuth_amount\": truuth_treatment[\"treatment_amount\"],\n", "                        \"truuth_treatment_date_conf\": truuth_treatment[\"treatment_date_conf\"],\n", "                        \"truuth_amount_conf\": truuth_treatment[\"treatment_amount_conf\"],\n", "                        \"truuth_treatment_conf\": truuth_treatment[\"treatment_description_conf\"],\n", "\n", "                        \"di_treatment_amount_correct\" : \"\",\n", "                        \"di_treatment_date_correct\" : \"\",\n", "                        \"di_treatment_desc_correct\": \"\",\n", "                        \"di_treatment_similarity\": \"\"\n", "                    })\n", "\n", "                    truuth_pointer += 1\n", "         \n", "            # If there are treatments, loop through each one and create a flat record \n", "            # by combining the invoice summary with the treatment details.\n", "            if treatments:\n", "                for treatment_item in treatments:\n", "                    row = summary.copy()\n", "                    row.update(treatment_item)\n", "                    summary_list.append(row)\n", "            # If there are no treatments, add the main summary record as is.\n", "            # When converted to a DataFrame, treatment columns will be empty (NaN).\n", "            else:\n", "                summary_list.append(summary)\n", "\n", "    summary_list = sorted(summary_list, key=lambda x: (x[\"claimno\"], x[\"docfile\"]))\n", "    return summary_list"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['********', '********', 'C8782000', 'C8782327', 'C8782368', 'C8782392', 'C8782671', 'C8782763', 'C8782771', 'C8782764', 'C8782837', 'C8782923', 'C8782943', 'C8781942', 'C8781947', 'C8781956', 'C8781980', 'C8781983', 'C8781987', 'C8781992', 'C8782032', 'C8782055', 'C8782169', 'C8782191', 'C8782319', 'C8782429', 'C8782601', 'C8782624', 'C8782824', 'C8782887', 'C8782912', 'C8782938', 'C8781953', 'C8781957', 'C8781961', 'C8781977', 'C8781981', 'C8781984', 'C8781998', 'C8782014', 'C8782150', 'C8782235', 'C8782246', 'C8782340', 'C8782409', 'C8782428', 'C8782595', 'C8782789', 'C8782821', 'C8782825', 'C8782848', 'C8782862', 'C8782883', 'C8782908', 'C8781948', 'C8781967', 'C8781971', 'C8781989', 'C8782066', 'C8782356', 'C8782347', 'C8782471', 'C8782453', 'C8782454', 'C8782477', 'C8782598', 'C8782647', 'C8782701', 'C8782719', 'C8782796', 'C8782838', 'C8782827', 'C8782851', 'C8782896', 'C8781950', 'C8781951', 'C8781969', 'C8781974', 'C8781982', 'C8782003', 'C8782045', 'C8782243', 'C8782324', 'C8782448', 'C8782481', 'C8782525', 'C8782554', 'C8782553', 'C8782602', 'C8782570', 'C8782659', 'C8782722', 'C8782750', 'C8782823', 'C8782893', 'C8781975', 'C8782004', 'C8782092', 'C8782188', 'C8782251', 'C8782245', 'C8782408', 'C8782449', 'C8782461', 'C8782482', 'C8782532', 'C8782550', 'C8782669', 'C8782615', 'C8782632', 'C8782708', 'C8782716', 'C8782801', 'C8782847', 'C8782922', 'C8782956', 'C8781958', 'C8781963', 'C8781972', 'C8782091', 'C8782100', 'C8782121', 'C8782217', 'C8782440', 'C8782507', 'C8782560', 'C8782604', '********', 'C8782642', 'C8782617', 'C8782672', 'C8782688', 'C8782758', 'C8782873', 'C8782888', 'C8782914', 'C8782935', 'C8782915', 'C8782946', 'C8781946', 'C8781966', 'C8782028', 'C8782140', 'C8782157', 'C8782186', 'C8782215', 'C8782262', 'C8782279', 'C8782296', 'C8782349', 'C8782433', 'C8782562', 'C8782587', 'C8782717', 'C8782778', 'C8782759', 'C8782798', 'C8782894', 'C8782933'])"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["truuth_info_dict.keys()"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'claim_no': '********',\n", " 'ocr_confidence': '0.92',\n", " 'service_provider': 'Sutherland Vet',\n", " 'service_provider_no': 'CT0004957',\n", " 'service_provider_conf': '0.85',\n", " 'invoice_date': '2025-05-02T00:00:00',\n", " 'invoice_date_conf': '0.928',\n", " 'invoice_no': '558713',\n", " 'invoice_no_conf': '0.99',\n", " 'total_amount': '54.1',\n", " 'total_amount_conf': '0.85',\n", " 'truuth_error': None,\n", " 'treatments': [{'treatment_date': '2025-05-02T00:00:00',\n", "   'treatment_date_conf': '0.928',\n", "   'treatment_description': '<PERSON><PERSON><PERSON> (Paroxetine) 20mg (30)',\n", "   'treatment_description_conf': '0.99',\n", "   'treatment_amount': '27.2',\n", "   'treatment_amount_conf': '0.99'},\n", "  {'treatment_date': '2025-05-02T00:00:00',\n", "   'treatment_date_conf': '0.928',\n", "   'treatment_description': 'Rivotril Drops (Clonazepam) 2.5mg/ml 10ml',\n", "   'treatment_description_conf': '0.99',\n", "   'treatment_amount': '26.9',\n", "   'treatment_amount_conf': '1'}]}"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["truuth_info_dict['********']"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/plain": ["159"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df_truuth_processed['ClaimNumber'].unique())"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNumber</th>\n", "      <th>OCRDocumentConfidence</th>\n", "      <th>ServiceProvider</th>\n", "      <th>ServiceProviderNo</th>\n", "      <th>ServiceProvider_conf</th>\n", "      <th>InvoiceDate</th>\n", "      <th>InvoiceDate_conf</th>\n", "      <th>InvoiceNumber</th>\n", "      <th>InvoiceNumber_conf</th>\n", "      <th>TreatmentDate</th>\n", "      <th>TreatmentDate_conf</th>\n", "      <th>TreatmentDescription</th>\n", "      <th>TreatmentDescription_conf</th>\n", "      <th>TreatmentAmount</th>\n", "      <th>TreatmentAmount_conf</th>\n", "      <th>TotalAmount</th>\n", "      <th>TotalAmount_conf</th>\n", "      <th>Error</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>********</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ClaimNumber OCRDocumentConfidence ServiceProvider ServiceProviderNo  \\\n", "220    ********                     0            None              None   \n", "\n", "    ServiceProvider_conf InvoiceDate InvoiceDate_conf InvoiceNumber  \\\n", "220                 None        None             None          None   \n", "\n", "    InvoiceNumber_conf TreatmentDate TreatmentDate_conf TreatmentDescription  \\\n", "220               None          None               None                 None   \n", "\n", "    TreatmentDescription_conf TreatmentAmount TreatmentAmount_conf  \\\n", "220                      None            None                 None   \n", "\n", "    TotalAmount TotalAmount_conf Error  \n", "220        None             None  None  "]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["df_truuth_processed[df_truuth_processed['ClaimNumber'] == '********']"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNumber</th>\n", "      <th>OCRDocumentConfidence</th>\n", "      <th>ServiceProvider</th>\n", "      <th>ServiceProviderNo</th>\n", "      <th>ServiceProvider_conf</th>\n", "      <th>InvoiceDate</th>\n", "      <th>InvoiceDate_conf</th>\n", "      <th>InvoiceNumber</th>\n", "      <th>InvoiceNumber_conf</th>\n", "      <th>TreatmentDate</th>\n", "      <th>TreatmentDate_conf</th>\n", "      <th>TreatmentDescription</th>\n", "      <th>TreatmentDescription_conf</th>\n", "      <th>TreatmentAmount</th>\n", "      <th>TreatmentAmount_conf</th>\n", "      <th>TotalAmount</th>\n", "      <th>TotalAmount_conf</th>\n", "      <th><PERSON><PERSON><PERSON>_<PERSON>rror</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>0.92</td>\n", "      <td>Sutherland Vet</td>\n", "      <td>CT0004957</td>\n", "      <td>0.85</td>\n", "      <td>2025-05-02T00:00:00</td>\n", "      <td>0.928</td>\n", "      <td>558713</td>\n", "      <td>0.99</td>\n", "      <td>2025-05-02T00:00:00</td>\n", "      <td>0.928</td>\n", "      <td><PERSON><PERSON><PERSON> (Paroxetine) 20mg (30)</td>\n", "      <td>0.99</td>\n", "      <td>27.2</td>\n", "      <td>0.99</td>\n", "      <td>54.1</td>\n", "      <td>0.85</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>0.92</td>\n", "      <td>Sutherland Vet</td>\n", "      <td>CT0004957</td>\n", "      <td>0.85</td>\n", "      <td>2025-05-02T00:00:00</td>\n", "      <td>0.928</td>\n", "      <td>558713</td>\n", "      <td>0.99</td>\n", "      <td>2025-05-02T00:00:00</td>\n", "      <td>0.928</td>\n", "      <td>Rivotril Drops (Clonazepam) 2.5mg/ml 10ml</td>\n", "      <td>0.99</td>\n", "      <td>26.9</td>\n", "      <td>1</td>\n", "      <td>54.1</td>\n", "      <td>0.85</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C8782000</td>\n", "      <td>0.41</td>\n", "      <td>Chemist Warehouse Lake Macquarie Fair</td>\n", "      <td>CT0980273</td>\n", "      <td>0.42</td>\n", "      <td>2025-05-17T00:00:00</td>\n", "      <td>0.47</td>\n", "      <td>86956095408230624</td>\n", "      <td>0.47</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C8782327</td>\n", "      <td>0.52</td>\n", "      <td>All Pets Vet Hospital</td>\n", "      <td>CT0000142</td>\n", "      <td>0.85</td>\n", "      <td>2025-05-14T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>1670264</td>\n", "      <td>0.7</td>\n", "      <td>2025-05-14T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>Consultation Leptospirosis 2 Vaccination Fee</td>\n", "      <td>0.7</td>\n", "      <td>78.4</td>\n", "      <td>0.7</td>\n", "      <td>355.9</td>\n", "      <td>0.6</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>444</th>\n", "      <td>C8782933</td>\n", "      <td>0.52</td>\n", "      <td>Manly Rd Vet Hospital</td>\n", "      <td>CT0003238</td>\n", "      <td>0.85</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>1465505</td>\n", "      <td>0.7</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>Clavulox Injection</td>\n", "      <td>0.7</td>\n", "      <td>24.55</td>\n", "      <td>0.7</td>\n", "      <td>346.7</td>\n", "      <td>0.6</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>445</th>\n", "      <td>C8782933</td>\n", "      <td>0.52</td>\n", "      <td>Manly Rd Vet Hospital</td>\n", "      <td>CT0003238</td>\n", "      <td>0.85</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>1465505</td>\n", "      <td>0.7</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>Metacam Injection 5mg Per mL (20mL)</td>\n", "      <td>0.7</td>\n", "      <td>22.9</td>\n", "      <td>0.7</td>\n", "      <td>346.7</td>\n", "      <td>0.6</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>446</th>\n", "      <td>C8782933</td>\n", "      <td>0.52</td>\n", "      <td>Manly Rd Vet Hospital</td>\n", "      <td>CT0003238</td>\n", "      <td>0.85</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>1465505</td>\n", "      <td>0.7</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>Amoxyclav 250mg Tablets</td>\n", "      <td>0.7</td>\n", "      <td>32.3</td>\n", "      <td>0.7</td>\n", "      <td>346.7</td>\n", "      <td>0.6</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>447</th>\n", "      <td>C8782933</td>\n", "      <td>0.52</td>\n", "      <td>Manly Rd Vet Hospital</td>\n", "      <td>CT0003238</td>\n", "      <td>0.85</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>1465505</td>\n", "      <td>0.7</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>Metacam Oral K9 10mL (10mL)</td>\n", "      <td>0.7</td>\n", "      <td>42.85</td>\n", "      <td>0.7</td>\n", "      <td>346.7</td>\n", "      <td>0.6</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>448</th>\n", "      <td>C8782933</td>\n", "      <td>0.52</td>\n", "      <td>Manly Rd Vet Hospital</td>\n", "      <td>CT0003238</td>\n", "      <td>0.85</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>1465505</td>\n", "      <td>0.7</td>\n", "      <td>2025-03-11T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>11/03/2025 Credit Card (# 956855)</td>\n", "      <td>0.7</td>\n", "      <td>346.7</td>\n", "      <td>0.7</td>\n", "      <td>346.7</td>\n", "      <td>0.6</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>449 rows × 18 columns</p>\n", "</div>"], "text/plain": ["    ClaimNumber OCRDocumentConfidence                        ServiceProvider  \\\n", "0      ********                     0                                   None   \n", "1      ********                  0.92                         Sutherland Vet   \n", "2      ********                  0.92                         Sutherland Vet   \n", "3      C8782000                  0.41  Chemist Warehouse Lake Macquarie Fair   \n", "4      C8782327                  0.52                  All Pets Vet Hospital   \n", "..          ...                   ...                                    ...   \n", "444    C8782933                  0.52                  Manly Rd Vet Hospital   \n", "445    C8782933                  0.52                  Manly Rd Vet Hospital   \n", "446    C8782933                  0.52                  Manly Rd Vet Hospital   \n", "447    C8782933                  0.52                  Manly Rd Vet Hospital   \n", "448    C8782933                  0.52                  Manly Rd Vet Hospital   \n", "\n", "    ServiceProviderNo ServiceProvider_conf          InvoiceDate  \\\n", "0                None                 None                 None   \n", "1           CT0004957                 0.85  2025-05-02T00:00:00   \n", "2           CT0004957                 0.85  2025-05-02T00:00:00   \n", "3           CT0980273                 0.42  2025-05-17T00:00:00   \n", "4           CT0000142                 0.85  2025-05-14T00:00:00   \n", "..                ...                  ...                  ...   \n", "444         CT0003238                 0.85  2025-03-11T00:00:00   \n", "445         CT0003238                 0.85  2025-03-11T00:00:00   \n", "446         CT0003238                 0.85  2025-03-11T00:00:00   \n", "447         CT0003238                 0.85  2025-03-11T00:00:00   \n", "448         CT0003238                 0.85  2025-03-11T00:00:00   \n", "\n", "    InvoiceDate_conf      InvoiceNumber InvoiceNumber_conf  \\\n", "0               None               None               None   \n", "1              0.928             558713               0.99   \n", "2              0.928             558713               0.99   \n", "3               0.47  86956095408230624               0.47   \n", "4                0.7            1670264                0.7   \n", "..               ...                ...                ...   \n", "444              0.7            1465505                0.7   \n", "445              0.7            1465505                0.7   \n", "446              0.7            1465505                0.7   \n", "447              0.7            1465505                0.7   \n", "448              0.7            1465505                0.7   \n", "\n", "           TreatmentDate TreatmentDate_conf  \\\n", "0                   None               None   \n", "1    2025-05-02T00:00:00              0.928   \n", "2    2025-05-02T00:00:00              0.928   \n", "3                   None               None   \n", "4    2025-05-14T00:00:00                0.7   \n", "..                   ...                ...   \n", "444  2025-03-11T00:00:00                0.7   \n", "445  2025-03-11T00:00:00                0.7   \n", "446  2025-03-11T00:00:00                0.7   \n", "447  2025-03-11T00:00:00                0.7   \n", "448  2025-03-11T00:00:00                0.7   \n", "\n", "                             TreatmentDescription TreatmentDescription_conf  \\\n", "0                                            None                      None   \n", "1                  Paxtine (Paroxetine) 20mg (30)                      0.99   \n", "2       Rivotril Drops (Clonazepam) 2.5mg/ml 10ml                      0.99   \n", "3                                            None                      None   \n", "4    Consultation Leptospirosis 2 Vaccination Fee                       0.7   \n", "..                                            ...                       ...   \n", "444                            Clavulox Injection                       0.7   \n", "445           Metacam Injection 5mg Per mL (20mL)                       0.7   \n", "446                       Amoxyclav 250mg Tablets                       0.7   \n", "447                   Metacam Oral K9 10mL (10mL)                       0.7   \n", "448             11/03/2025 Credit Card (# 956855)                       0.7   \n", "\n", "    TreatmentAmount TreatmentAmount_conf TotalAmount TotalAmount_conf  \\\n", "0              None                 None        None             None   \n", "1              27.2                 0.99        54.1             0.85   \n", "2              26.9                    1        54.1             0.85   \n", "3              None                 None        None             None   \n", "4              78.4                  0.7       355.9              0.6   \n", "..              ...                  ...         ...              ...   \n", "444           24.55                  0.7       346.7              0.6   \n", "445            22.9                  0.7       346.7              0.6   \n", "446            32.3                  0.7       346.7              0.6   \n", "447           42.85                  0.7       346.7              0.6   \n", "448           346.7                  0.7       346.7              0.6   \n", "\n", "    <PERSON><PERSON><PERSON>_Error  \n", "0           None  \n", "1           None  \n", "2           None  \n", "3           None  \n", "4           None  \n", "..           ...  \n", "444         None  \n", "445         None  \n", "446         None  \n", "447         None  \n", "448         None  \n", "\n", "[449 rows x 18 columns]"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["df_truuth_processed"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNo</th>\n", "      <th>InvoiceNo</th>\n", "      <th>ServiceProviderName</th>\n", "      <th>ServiceProviderNo</th>\n", "      <th>DateInvoice</th>\n", "      <th>DateTreatment</th>\n", "      <th>TreatmentDrugDescription</th>\n", "      <th>AmountInclVat</th>\n", "      <th>InvoiceAmount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>1/1264562</td>\n", "      <td>Alderley-Enoggera Vet Practice</td>\n", "      <td>*********</td>\n", "      <td>2025-05-27</td>\n", "      <td>2025-05-27</td>\n", "      <td>Consult Medical Progress Ear/Eye/Skin</td>\n", "      <td>52.50</td>\n", "      <td>90.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>1/1264562</td>\n", "      <td>Alderley-Enoggera Vet Practice</td>\n", "      <td>*********</td>\n", "      <td>2025-05-27</td>\n", "      <td>2025-05-27</td>\n", "      <td>Cytology Follow-Up or 2nd Site</td>\n", "      <td>38.00</td>\n", "      <td>90.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C8782028</td>\n", "      <td>1/1265390</td>\n", "      <td>Mitchelton Veterinary Practice</td>\n", "      <td>CT0003486</td>\n", "      <td>2025-05-30</td>\n", "      <td>2025-05-30</td>\n", "      <td>Cytopoint 10mg Vial</td>\n", "      <td>146.85</td>\n", "      <td>160.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C8782028</td>\n", "      <td>1/1265390</td>\n", "      <td>Mitchelton Veterinary Practice</td>\n", "      <td>CT0003486</td>\n", "      <td>2025-05-30</td>\n", "      <td>2025-05-30</td>\n", "      <td>PAYMENTS:</td>\n", "      <td>13.36</td>\n", "      <td>160.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C8782409</td>\n", "      <td>1/165579</td>\n", "      <td>Vets On Eyre</td>\n", "      <td>CT0008818</td>\n", "      <td>2025-05-30</td>\n", "      <td>2025-05-30</td>\n", "      <td>CONSULTATION</td>\n", "      <td>88.00</td>\n", "      <td>224.20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ClaimNo  InvoiceNo              ServiceProviderName ServiceProviderNo  \\\n", "0  ********  1/1264562   Alderley-Enoggera Vet Practice         *********   \n", "1  ********  1/1264562   Alderley-Enoggera Vet Practice         *********   \n", "2  C8782028  1/1265390  Mitchelton Veterinary Practice          CT0003486   \n", "3  C8782028  1/1265390  Mitchelton Veterinary Practice          CT0003486   \n", "4  C8782409   1/165579                     Vets On Eyre         CT0008818   \n", "\n", "  DateInvoice DateTreatment               TreatmentDrugDescription  \\\n", "0  2025-05-27    2025-05-27  Consult Medical Progress Ear/Eye/Skin   \n", "1  2025-05-27    2025-05-27         Cytology Follow-Up or 2nd Site   \n", "2  2025-05-30    2025-05-30                    Cytopoint 10mg Vial   \n", "3  2025-05-30    2025-05-30                              PAYMENTS:   \n", "4  2025-05-30    2025-05-30                           CONSULTATION   \n", "\n", "   AmountInclVat  InvoiceAmount  \n", "0          52.50          90.50  \n", "1          38.00          90.50  \n", "2         146.85         160.21  \n", "3          13.36         160.21  \n", "4          88.00         224.20  "]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["# load UPM ground truth information from UPM_GROUND_TRUTH_PATH\n", "upm_truth_df = pd.read_excel(UPM_GROUND_TRUTH_PATH)\n", "# [['Claim No', 'Invoice No', 'Service Provider Name',\n", "#     'Date Invoice', 'Date Treatment','Treatment Drug Code','Treatment Drug Description', 'Amount Inc Vat',]]\n", "# 'Amount Claimed (UPM)']] #\n", "upm_truth_df.head()"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["# parse upm_truth_df \n", "def parse_upm_info(df: pd.DataFrame) -> Dict:\n", "    ans = {}\n", "\n", "    for info in df.to_dict(orient=\"records\"):\n", "        claim_no = info[\"ClaimNo\"]\n", "        invoice_no = info[\"InvoiceNo\"]\n", "        service_provider = info[\"ServiceProviderName\"]\n", "        service_provider_no = info[\"ServiceProviderNo\"]\n", "        date_treatment = info[\"DateTreatment\"]\n", "        treatment = info[\"TreatmentDrugDescription\"]\n", "        # treatment_code = info[\"TreatmentDrugCode\"]\n", "        amount = info[\"AmountInclVat\"]\n", "        invoice_date = info.get(\"DateInvoice\", \"\")\n", "        invoice_total = info.get(\"InvoiceAmount\", 0.)\n", "\n", "        if claim_no not in ans:\n", "            ans[claim_no] = {\n", "                \"claim_no\": claim_no,\n", "                \"invoice_no\": invoice_no,\n", "                \"invoice_date\": invoice_date,\n", "                \"invoice_total\": invoice_total,\n", "                \"service_provider\": service_provider,\n", "                \"service_provider_no\": service_provider_no,\n", "                \"treatments\": [\n", "                    {\n", "                        \"date_treatment\": date_treatment,\n", "                        \"treatment\": treatment,\n", "                        # \"treatment_code\": treatment_code,\n", "                        \"amount\": amount,\n", "                    }\n", "                ]\n", "            }\n", "        else:\n", "            ans[claim_no][\"treatments\"].append(\n", "                {\n", "                    \"date_treatment\": date_treatment,\n", "                    \"treatment\": treatment,\n", "                    # \"treatment_code\": treatment_code,\n", "                    \"amount\": amount,\n", "                }\n", "            )\n", "    return ans"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["upm_info_dict = parse_upm_info(upm_truth_df)"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [], "source": ["def filter_di_res_dict(di_res_dict: Dict, \n", "                      upm_res_dict: Dict,\n", "                      docfile2claimno: Dict) -> Dict:\n", "    \"\"\"\n", "    Remove entries from di_res_dict where the corresponding claim numbers don't exist in upm_res_dict.\n", "    \n", "    Args:\n", "        di_res_dict: Dictionary mapping docfiles to DI results\n", "        upm_res_dict: Dictionary mapping claim numbers to UPM results\n", "        docfile2claimno: Dictionary mapping docfiles to claim numbers\n", "        \n", "    Returns:\n", "        Filtered di_res_dict with only entries that have matching claim numbers in upm_res_dict\n", "    \"\"\"\n", "    filtered_di_res_dict = {}\n", "    removed_count = 0\n", "    \n", "    for docfile, di_results in di_res_dict.items():\n", "        # Get the claim number for this docfile\n", "        claim_no = docfile2claimno.get(docfile.lower())\n", "        \n", "        # Skip if no claim number mapping exists\n", "        if not claim_no:\n", "            logger.warning(f\"No claim number mapping found for docfile: {docfile}. Removing from results.\")\n", "            removed_count += 1\n", "            continue\n", "            \n", "        # Check if the claim number exists in upm_res_dict\n", "        if claim_no in upm_res_dict:\n", "            filtered_di_res_dict[docfile] = di_results\n", "        else:\n", "            logger.warning(f\"Claim number {claim_no} (docfile: {docfile}) not found in UPM data. Removing from results.\")\n", "            removed_count += 1\n", "            \n", "    logger.info(f\"Removed {removed_count} entries from di_res_dict that had no matching claim numbers in upm_res_dict.\")\n", "    logger.info(f\"Original di_res_dict size: {len(di_res_dict)}, Filtered di_res_dict size: {len(filtered_di_res_dict)}\")\n", "    \n", "    return filtered_di_res_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-06-24 16:55:18.411\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781962 (docfile: 0ea11fff-e5bb-48b1-b34e-42f8feb7339a.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.412\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782349 (docfile: 113ddc44-be51-4cae-9f59-d419dc9f61a6.png) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.413\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782214 (docfile: 119ffa18-93ab-47e2-8c44-87bcbe4dd9b5.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.414\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m24\u001b[0m - \u001b[33m\u001b[1mNo claim number mapping found for docfile: 1374dd27-21f2-4aea-b317-eeaa5639b49b.pdf. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.414\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782368 (docfile: 15155ac2-737c-4fd0-a896-823316c0b754.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.415\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782251 (docfile: 1b3def1d-4b23-4bd4-a94e-61d62bdc5c23.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.415\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782481 (docfile: 219df155-ae50-4cdc-8439-025d2154615e.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.416\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781947 (docfile: 239e095a-6128-4b18-8675-c417a402e0c9.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.416\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782363 (docfile: 25714a32-883c-4fa2-bc07-81087ac14c73.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.417\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782215 (docfile: 2648ba8e-b09f-42be-9fa8-df7dd4ab23a2.png) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.417\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782737 (docfile: 28a991aa-d832-45f3-9c81-f75cc0055008.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.418\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782045 (docfile: 2bb165c6-6ed1-47b2-912a-6ff482aa2a68.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.419\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782560 (docfile: 2c84a716-09aa-41ae-9d3d-b1f040777c1e.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.420\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782201 (docfile: 2f2a772a-2ce6-4865-bdc0-299fe1ec47eb.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.421\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782632 (docfile: 2f2e5944-f6d3-4512-85dc-d69be503c632.PDF) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.421\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782624 (docfile: 30b75c3d-062e-4ca1-96ef-e38c8604e886.PDF) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.422\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781984 (docfile: 3f5d87b9-aa25-40b5-9dcd-56d160d5cb30.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.422\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782461 (docfile: 41f46159-bfd0-449f-8907-5f2133117fe8.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.423\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781948 (docfile: 42d81376-1947-4da8-af88-1232ba21d49a.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.424\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782000 (docfile: 4a1fc5f6-133c-4dee-95b2-344c5d5cd27a.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.425\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781967 (docfile: 4bda80bc-80e8-4321-87ce-c96f1f78f412.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.426\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782076 (docfile: 50813bb1-80cb-4ef4-8132-f25b97b0a875.png) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.427\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782385 (docfile: 508c5dfc-94b5-419e-b60d-dae495437f4a.png) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.427\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782439 (docfile: 523c2af3-006a-4cb7-b620-ec4928211e8d.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.428\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781975 (docfile: 5cbfccd2-a555-40c9-9fd9-08bbc48e7a0d.png) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.429\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782847 (docfile: 5cf82189-efc0-400e-b369-c4c71e7379dc.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.429\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782737 (docfile: 685f8482-ea8e-428c-9810-97f9c5f39b47.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.430\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782217 (docfile: 6b034de0-ad17-49ae-8e88-d5c94e125c9a.png) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.431\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number ******** (docfile: 6d2c7e87-698b-4126-b51e-9ecee0f0e3d3.PDF) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.433\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782076 (docfile: 6dd8a312-97a4-424f-8f94-2e56217c68a5.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.434\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782201 (docfile: 6e6abe9f-600a-4338-a34f-4d27c8d32a9d.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.434\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782434 (docfile: 726fbffa-d298-45b6-bbd4-9c10d5f73d22.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.435\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782798 (docfile: 72ff5e9b-a84a-428c-bdcb-15fc3fd5bdf3.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.435\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781951 (docfile: 73585170-d997-4b5c-b6f7-d01b8cfa4b91.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.435\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781962 (docfile: 76bbf9e7-be45-4a65-8d45-0d3be2f0bfba.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.436\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781982 (docfile: 7dda6eb4-9562-40dc-9cb5-d92cb6c6bfc6.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.436\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782482 (docfile: 86b0491a-8b9b-4af5-8cc2-d2a9bf1a0db8.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.437\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782385 (docfile: 87ad059f-d90e-44cd-ba64-8e870c4934c8.png) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.438\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782157 (docfile: 8e0b3a6e-df27-4995-b423-5a5123842a33.png) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.438\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782434 (docfile: 9a064089-5975-46a1-9bf8-e9b9fcbf9c6d.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.442\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782783 (docfile: 9abe7d7c-e4cf-4ffb-bd08-84acab4e4bea.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.443\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782347 (docfile: 9f6ee82c-4f37-47e7-abd9-9b6917b30e26.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.446\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782091 (docfile: a19638d6-4882-4d5a-a377-174d1476c19c.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.447\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782429 (docfile: aa052f34-10a1-47f3-9cc2-4e02c58d1e75.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.447\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782750 (docfile: aa0b7afe-c750-4236-9bd4-ddd504f6a830.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.448\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782507 (docfile: ab059fda-2515-4e84-9084-e09e9e0d177e.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.448\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number ******** (docfile: afe4dbeb-1aec-4cc8-b117-df2579fa5e29.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.454\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782615 (docfile: b0d37f88-15f6-4188-966e-e8cfcfcd8614.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.455\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782003 (docfile: b1fa9de9-d1d2-46af-b5f0-575e38e9d78f.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.457\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782356 (docfile: b3f4eb21-ed71-41f5-bc89-5ed37ed9e0d2.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.458\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782923 (docfile: b85f98b7-f03c-4e54-ba77-165aee5c7aa3.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.458\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782214 (docfile: b9af9fad-4d1b-488b-8806-2249f42b135b.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.459\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782894 (docfile: c1d83450-f140-4ab9-b450-4254fe4ac73c.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.459\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782778 (docfile: c4567aa5-c640-4871-bf5a-713a47f50dcd.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.460\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782191 (docfile: c67270af-20ac-49d6-9e75-e15493f3fe40.png) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.460\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782759 (docfile: c6ea18d2-8dba-4de1-be00-4444b7263642.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.461\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782532 (docfile: c73e443c-685f-4631-b778-6b7c9ecb1787.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.461\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782408 (docfile: cb0f60bc-3a19-4d3a-999f-7fe53d71f3d4.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.462\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782046 (docfile: cc9e791a-db3b-4ed0-a539-d7e7e81e004a.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.462\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782439 (docfile: cf4377f2-82bf-44ed-b7cf-8e19ee0e3738.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.464\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782827 (docfile: d22374c1-c6cc-47f2-bc8b-4e5ad6a47d56.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.465\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782862 (docfile: d9255a09-253e-4662-b10d-412056ca630f.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.465\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782783 (docfile: e9bdf3b4-ab35-46c4-bfc7-5527500dacc3.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.466\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782201 (docfile: eddedc60-176b-4775-bbea-bb8ff98001c1.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.467\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782056 (docfile: f27d5f3c-81c5-47fd-a75e-eda1b0ed0e0c.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.467\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782887 (docfile: f3a7e9ba-4224-4c29-927d-75bfbac16d39.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.468\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781981 (docfile: f403e62a-75d7-450a-bdfd-7ffbc74bdd1f.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.468\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782363 (docfile: f57c179b-a916-4338-9eb3-39135222a8bd.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.468\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8781962 (docfile: f76a33b0-dbb3-40bb-b92b-273c8c5729a7.jpeg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.469\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number C8782243 (docfile: fb9933ad-9ce4-4dda-9155-dbce08d4be92.jpg) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.470\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m32\u001b[0m - \u001b[33m\u001b[1mClaim number ******** (docfile: fd33bfa0-deab-4e7a-b4fb-0ef1ad01d672.pdf) not found in UPM data. Removing from results.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.471\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m35\u001b[0m - \u001b[1mRemoved 71 entries from di_res_dict that had no matching claim numbers in upm_res_dict.\u001b[0m\n", "\u001b[32m2025-06-24 16:55:18.473\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mfilter_di_res_dict\u001b[0m:\u001b[36m36\u001b[0m - \u001b[1mOriginal di_res_dict size: 186, Filtered di_res_dict size: 115\u001b[0m\n"]}], "source": ["filtered_di_res_dict = filter_di_res_dict(document_intelligence_rule_res, upm_info_dict, docfile2claimno)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO combine the rule processed results with UPM data (Clary raw)\n", "# Import for text similarity matching\n", "from difflib import SequenceMatcher\n", "\n", "def get_text_similarity(text1, text2):\n", "    \"\"\"Calculate similarity ratio between two strings\"\"\"\n", "    if not text1 or not text2:\n", "        return 0.0\n", "    return SequenceMatcher(None, str(text1).lower(), str(text2).lower()).ratio()\n", "\n", "\n", "    \n", "def gather_all_info(di_res_dict: Dict = document_intelligence_rule_res, \n", "                   \n", "                    upm_res_dict: Dict = upm_info_dict, \n", "                    paddleocr_res_dict: Dict = paddleocr_info_dict, \n", "                    claimno2docfile: Dict = claimno2docfile, \n", "                    docfile2claimno: Dict = docfile2claimno):\n", "\n", "    summary_list = []\n", "\n", "    # Threshold for considering treatments as similar\n", "    TREATMENT_SIMILARITY_THRESHOLD = 0.7\n", "\n", "    # files are not processed by Azure Document Intelligence\n", "    # for docfile in set(docfile2claimno.keys()) - set(di_res_dict.keys()):\n", "    #     mapping_claimno = docfile2claimno[docfile.lower()]\n", "    #     stat_info = stat_res_dict[mapping_claimno]\n", "\n", "    #     ocr_conf = stat_info.get(\"doc_conf\", 0.) or 0.\n", "    #     try:\n", "    #         activate, message = if_file_broken(os.path.join(f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix}_samples_DI\", docfile))\n", "    #     except FileNotFoundError:\n", "    #         activate, message = if_file_broken(os.path.join(f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix}_samples_DI\", docfile.lower()))\n", "    #     summary = {\n", "    #             \"claimno\": mapping_claimno,\n", "    #             \"docfile\": docfile,\n", "    #             \"ocr_conf\": ocr_conf,\n", "    #             \"rule_res\": message}\n", "    #     summary_list.append(summary)\n", "\n", "    # files processed by Azure Document Intelligence\n", "    for k, v in di_res_dict.items():\n", "        docfile = k\n", "\n", "        # [{'service_provider': 'Baldivis Vet Hospital Pty Ltd',\n", "        # 'invoice_no': '1/928537',\n", "        # 'invoice_date': datetime.date(2024, 9, 22),\n", "        # 'invoice_total': 2634.57,\n", "        # 'service_provider_conf': 0.888,\n", "        # 'invoice_no_conf': 0.94,\n", "        # 'invoice_date_conf': 0.941,\n", "        # 'invoice_total_conf': 0.93,\n", "        # 'treatments': [{'treatment': 'Consult 19:00 - 23:30',\n", "        #     'amount': 190.0,\n", "        #     'treatment_conf': 0.888,\n", "        #     'amount_conf': 0.889,\n", "        #     'treatmentline_conf': 0.85},\n", "        invoices = v\n", "\n", "        mapping_claimno = docfile2claimno[docfile.lower()]\n", "\n", "        if not mapping_claimno:\n", "            logger.warning(f\"No claim number mapping found for docfile: {docfile}. Skipping.\")\n", "            continue\n", "\n", "        logger.info(f\"Gather infomation: {mapping_claimno} {docfile}\")\n", "\n", "        paddleocr_content = paddleocr_res_dict[docfile.lower()][\"content\"]\n", "\n", "        if len(invoices) > 1:\n", "            print(k, mapping_claimno, len(invoices))\n", "            print(\"-\"*40)\n", "\n", "        # \"claim_no\": claim_no,\n", "        # \"invoice_no\": invoice_no,\n", "        # \"invoice_date\": invoice_date,\n", "        # \"invoice_total\": invoice_total,\n", "        # \"service_provider\": service_provider,\n", "        # \"service_provider_address\": service_provider_address,\n", "        # \"treatments\": [\n", "        #     {\n", "        #         \"date_treatment\": date_treatment,\n", "        #         \"treatment\": treatment,\n", "        #         \"amount\": amount,\n", "        #     }\n", "        # ]\n", "\n", "        # \"claim_no\"\n", "        # \"doc_conf\"\n", "        # \"invoice_total_ocr\"\n", "        # \"invoice_total_upm\"\n", "        # \"invoice_total_diff\"\n", "        # \"invoice_count_ocr\"\n", "        # \"invoice_count_upm\"\n", "        # \"invoice_count_diff\"\n", "        # \"is_invoice_num_diff\"\n", "        # \"treatment_count_ocr\"\n", "        # \"treatment_count_upm\"\n", "        # \"treatment_count_diff\"\n", "        # \"invoice_no\"\n", "        # \"invoice_date\"\n", "        # \"service_provider_ocr\"\n", "        # \"service_provider_upm\"\n", "        # \"is_service_provider_diff\"\n", "        # \"amount\"\n", "        # \"doc_file\"\n", "        \n", "        # Check if the claim number exists in upm_res_dict\n", "        if mapping_claimno not in upm_res_dict:\n", "            logger.warning(f\"Claim number {mapping_claimno} not found in UPM data. Processing with empty UPM data.\")\n", "            # Create empty UPM info with default values\n", "            upm_info = {\n", "                \"claim_no\": mapping_claimno,\n", "                \"invoice_no\": \"\",\n", "                \"invoice_date\": None,\n", "                \"invoice_total\": 0,\n", "                \"service_provider\": \"\",\n", "                \"service_provider_no\": \"\",\n", "                \"treatments\": []\n", "            }\n", "        else:\n", "            upm_info = upm_res_dict[mapping_claimno]\n", "            \n", "        for invoice in invoices:\n", "            # check invoice no\n", "            di_invoice_no = invoice[\"invoice_no\"]\n", "            upm_invoice_no = str(upm_info[\"invoice_no\"])\n", "            di_invoice_no_correct = int(di_invoice_no == upm_invoice_no)\n", "      \n", "\n", "            # check service provider\n", "            di_service_provider = invoice[\"service_provider\"]\n", "            di_service_provider_address = invoice[\"service_provider_address\"]\n", "            upm_service_provider = upm_info[\"service_provider\"]\n", "     \n", "    \n", "            di_service_provider_correct = int(di_service_provider == upm_service_provider)\n", "            \n", "\n", "            # check invoice date TODO cannot check Truuth results now 15OCT24\n", "            di_invoice_date = invoice[\"invoice_date\"].isoformat() if not isinstance(invoice[\"invoice_date\"], str) else invoice[\"invoice_date\"]\n", "            if upm_info[\"invoice_date\"]:\n", "                print(upm_info[\"invoice_date\"])\n", "                upm_invoice_date = upm_info[\"invoice_date\"].date().isoformat()\n", "            else:\n", "                upm_invoice_date = \"\"\n", "            # if upm_info[\"invoice_date\"] != upm_info[\"invoice_date\"].date().isoformat():\n", "            #     print(\"invoice_date mismatch\", k, mapping_claimno)\n", "            di_invoice_date_correct = int(di_invoice_date == upm_invoice_date)\n", "\n", "\n", "            # check total amount ### TODO\n", "            di_total_amount = invoice[\"invoice_total\"]\n", "            upm_total_amount = upm_info[\"invoice_total\"]  \n", "            # if stat_info.get(\"invoice_total_upm\", -1) != upm_info.get(\"invoice_total\", None):\n", "            #     print(\"invoice_total mismatch\", k, mapping_claimno)\n", "            di_total_amount_correct = int(di_total_amount == upm_total_amount)\n", "\n", "\n", "            summary = {\n", "                \"claimno\": mapping_claimno,\n", "                \"docfile\": docfile,\n", "                \"paddleocr_content\": paddleocr_content,\n", "\n", "                \"di_conf\": min(invoice[\"invoice_no_conf\"], invoice[\"invoice_date_conf\"], invoice[\"invoice_total_conf\"]),\n", "\n", "                \"di_invoice_no\": di_invoice_no,\n", "                \"upm_invoice_no\": upm_invoice_no,\n", "                \"di_invoice_no_correct\": di_invoice_no_correct,\n", "                \"di_invoice_no_conf\": invoice[\"invoice_no_conf\"],\n", "\n", "                \"di_service_provider\": di_service_provider,\n", "                \"di_service_provider_address\": di_service_provider_address,\n", "                \"upm_service_provider\": upm_service_provider,\n", "                \"di_service_provider_correct\": di_service_provider_correct,\n", "        \n", "                \"di_service_provider_conf\": invoice[\"service_provider_conf\"],\n", "                \"di_abn\": invoice[\"ABN\"],\n", "\n", "                \"di_invoice_date\": di_invoice_date,\n", "                \"upm_invoice_date\": upm_invoice_date,\n", "                \"di_invoice_date_correct\": di_invoice_date_correct,\n", "                \"di_invoice_date_conf\": invoice[\"invoice_date_conf\"],\n", "\n", "                \"di_total_amount\": di_total_amount,\n", "          \n", "                \"di_total_amount_correct\": di_total_amount_correct,\n", "         \n", "                \"di_total_amount_conf\": invoice[\"invoice_total_conf\"],\n", "\n", "                \"rule_res\": invoice[\"rule_res\"],\n", "                \"if_fallout\": int(\"FALL OUT\" in invoice[\"rule_res\"])\n", "            }\n", "\n", "            # DI treatment \n", "            # \"treatments\": [{'treatment_date': '2024-09-17',\n", "            # 'treatment': ' Veterinary Consultation & Examination: Health Assessment',\n", "            # 'amount': 104.0,\n", "            # 'treatment_date_conf': 0.947,\n", "            # 'treatment_conf': 0.8899999110000089,\n", "            # 'amount_conf': 0.888,\n", "            # 'treatmentline_conf': 0.914},]\n", "            # UPM treatment\n", "            # \"treatments\": [\n", "            #     {\n", "            #         \"date_treatment\": date_treatment,\n", "            #         \"treatment\": treatment,\n", "            #         \"amount\": amount,\n", "            #     }\n", "            # ]\n", "            sorted_di_treatments = sorted(invoice[\"treatments\"], key=lambda x: x[\"amount\"])\n", "            sorted_upm_treatments = sorted(upm_info[\"treatments\"], key=lambda x: x[\"amount\"])\n", "            treatments = []\n", "            di_pointer, upm_pointer = 0, 0\n", "            while di_pointer < len(sorted_di_treatments) or upm_pointer < len(sorted_upm_treatments):\n", "                try:\n", "                    di_treatment = sorted_di_treatments[di_pointer]\n", "                except IndexError:\n", "                    assert di_pointer == len(sorted_di_treatments)\n", "                    di_treatment = {}\n", "                try:\n", "                    upm_treatment = sorted_upm_treatments[upm_pointer]\n", "                except IndexError:\n", "                    assert upm_pointer == len(sorted_upm_treatments)\n", "                    upm_treatment = {}\n", "                # logger.info(di_treatment)\n", "\n", "                # logger.info(upm_treatment)\n", "                if not di_treatment and not upm_treatment:\n", "                    logger.error(f\"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.\")\n", "                    logger.error(f\"sorted_di_treatments: {sorted_di_treatments}\")\n", "                    logger.error(f\"sorted_upm_treatments: {sorted_upm_treatments}\")\n", "                    logger.error(f\"di_pointer: {di_pointer}, upm_pointer: {upm_pointer}\")\n", "                    break\n", "\n", "                if not di_treatment:\n", "                    if upm_treatment[\"date_treatment\"]:\n", "                        upm_treatment_date = upm_treatment[\"date_treatment\"].date().isoformat()\n", "                    treatments.append({\n", "                        \"di_treatment_date\": \"\",\n", "                        \"di_treatment\": \"\",\n", "                        \"di_amount\": \"\",\n", "                        \"di_treatment_date_conf\": \"\",\n", "                        \"di_treatment_conf\": \"\",\n", "                        \"di_amount_conf\": \"\",\n", "\n", "                        \"upm_treatment_date\": upm_treatment_date,\n", "                        \"upm_treatment\": upm_treatment[\"treatment\"],\n", "                        \"upm_amount\": upm_treatment[\"amount\"],\n", "\n", "                        \"di_treatment_amount_correct\" : \"\",\n", "                        \"di_treatment_date_correct\" : \"\"\n", "\n", "\n", "                    })\n", "                    upm_pointer += 1\n", "                    continue\n", "\n", "                if not upm_treatment:\n", "                    di_treatment_date = di_treatment[\"treatment_date\"].isoformat() if not isinstance(di_treatment[\"treatment_date\"], str) else di_treatment[\"treatment_date\"]\n", "\n", "                    treatments.append({\n", "                        \"di_treatment_date\": di_treatment_date,\n", "                        \"di_treatment\": di_treatment[\"treatment\"],\n", "                        \"di_amount\": di_treatment[\"amount\"],\n", "                        \"di_treatment_date_conf\": di_treatment[\"treatment_date_conf\"],\n", "                        \"di_treatment_conf\": di_treatment[\"treatment_conf\"],\n", "                        \"di_amount_conf\": di_treatment[\"amount_conf\"],\n", "\n", "\n", "                        \"upm_treatment_date\": \"\",\n", "                        \"upm_treatment\": \"\",\n", "                        \"upm_amount\": \"\",\n", "\n", "                        \"di_treatment_amount_correct\" : 0,\n", "                        \"di_treatment_date_correct\" : 0\n", "\n", "                    })\n", "                    di_pointer += 1\n", "                    continue\n", "\n", "                if (di_treatment[\"amount\"] == upm_treatment[\"amount\"]) or (di_treatment[\"amount\"] - upm_treatment[\"amount\"]<0.05):\n", "                    di_treatment_date = di_treatment[\"treatment_date\"].isoformat() if not isinstance(di_treatment[\"treatment_date\"], str) else di_treatment[\"treatment_date\"]\n", "                    if upm_treatment[\"date_treatment\"]:\n", "                        upm_treatment_date = upm_treatment[\"date_treatment\"].date().isoformat()\n", "                    di_treatment_date_correct = int(di_treatment_date == upm_treatment_date )\n", "\n", "                    # Add treatment text similarity calculation\n", "                    treatment_similarity = get_text_similarity(di_treatment[\"treatment\"], upm_treatment[\"treatment\"])\n", "                    di_treatment_text_correct = int(treatment_similarity >= TREATMENT_SIMILARITY_THRESHOLD)\n", "\n", "                    treatments.append({\n", "                        \"di_treatment_date\": di_treatment_date,\n", "                        \"di_treatment\": di_treatment[\"treatment\"],\n", "                        \"di_amount\": di_treatment[\"amount\"],\n", "                        \"di_treatment_date_conf\": di_treatment[\"treatment_date_conf\"],\n", "                        \"di_treatment_conf\": di_treatment[\"treatment_conf\"],\n", "                        \"di_amount_conf\": di_treatment[\"amount_conf\"],\n", "\n", "\n", "                        \"upm_treatment_date\": upm_treatment[\"date_treatment\"],\n", "                        \"upm_treatment\": upm_treatment_date,\n", "                        \"upm_amount\": upm_treatment[\"amount\"],\n", "\n", "                        \"di_treatment_amount_correct\" : 1,\n", "                        \"di_treatment_date_correct\" : di_treatment_date_correct,\n", "                        \"di_treatment_desc_correct\": di_treatment_text_correct,\n", "                        \"di_reatment_similarity\": treatment_similarity\n", "\n", "\n", "                    })\n", "                    di_pointer += 1\n", "                    upm_pointer += 1\n", "                elif di_treatment[\"amount\"] < upm_treatment[\"amount\"]:\n", "                    di_treatment_date = di_treatment[\"treatment_date\"].isoformat() if not isinstance(di_treatment[\"treatment_date\"], str) else di_treatment[\"treatment_date\"]\n", "\n", "                    treatments.append({\n", "                        \"di_treatment_date\": di_treatment_date,\n", "                        \"di_treatment\": di_treatment[\"treatment\"],\n", "                        \"di_amount\": di_treatment[\"amount\"],\n", "                        \"di_treatment_date_conf\": di_treatment[\"treatment_date_conf\"],\n", "                        \"di_treatment_conf\": di_treatment[\"treatment_conf\"],\n", "                        \"di_amount_conf\": di_treatment[\"amount_conf\"],\n", "\n", "                        \"upm_treatment_date\": \"\",\n", "                        \"upm_treatment\": \"\",\n", "                        \"upm_amount\": \"\",\n", "\n", "                        \"di_treatment_amount_correct\" : 0,\n", "                        \"di_treatment_date_correct\" : 0,\n", "                        \"di_treatment_desc_correct\": 0,\n", "                        \"di_treatment_similarity\": 0\n", "\n", "                    })\n", "                    di_pointer += 1\n", "                else:\n", "                    if upm_treatment[\"date_treatment\"]:\n", "                        upm_treatment_date = upm_treatment[\"date_treatment\"].date().isoformat()\n", "                    treatments.append({\n", "                        \"di_treatment_date\": \"\",\n", "                        \"di_treatment\": \"\",\n", "                        \"di_amount\": \"\",\n", "                        \"di_treatment_date_conf\": \"\",\n", "                        \"di_treatment_conf\": \"\",\n", "                        \"di_amount_conf\": \"\",\n", "\n", "                        \"upm_treatment_date\": upm_treatment_date,\n", "                        \"upm_treatment\": upm_treatment[\"treatment\"],\n", "                        \"upm_amount\": upm_treatment[\"amount\"],\n", "\n", "                        \"di_treatment_amount_correct\" : \"\",\n", "                        \"di_treatment_date_correct\" : \"\",\n", "                        \"di_treatment_desc_correct\": \"\",\n", "                        \"di_treatment_similarity\": \"\"\n", "\n", "\n", "                    })\n", "\n", "                    upm_pointer += 1\n", "            summary[\"treatments\"] = treatments\n", "            summary_list.append(summary)\n", "    summary_list = sorted(summary_list, key=lambda x: (x[\"claimno\"], x[\"docfile\"]))\n", "    return summary_list\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Matching with merged UPM lines"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-06-24 16:55:37.656\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782922 0983b113-2772-4a1d-bba6-3b8f823648c3.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.658\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782587 1062b12f-027c-44bd-b8d0-53d00d5f20cb.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.659\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782324 16a6ba20-9d94-4ae0-aa4e-a4972fa18cab.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.659\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782055 16c87632-8071-4110-aa04-81965fbe6670.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.660\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781987 1c2502de-8e09-46af-ac2b-0244d7c4b129.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.662\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782837 23136292-93ae-4215-bc52-068cb578379a.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.663\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782092 263bb02a-0a2d-456e-828d-baad0d413431.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.663\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781974 26695410-7ebe-4050-b310-5d4bbda59f42.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.664\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782821 2734ddd6-df57-452a-aba7-167c5574737b.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.665\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781977 2741d777-80a3-4181-b726-f656c71d3528.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.666\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782454 29477e5a-06c4-44b5-be2f-b6a71d646ff6.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.666\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781963 299afb8e-49f6-4abd-af9a-5342f3f61472.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.667\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: ******** 29d0199c-2a2a-47bf-889f-0202a41c5df2.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.668\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782140 2ae1e421-be3f-4a2a-b668-8ce542a8d9e0.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.668\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782327 30467500-9f9e-47ff-b00a-d49acd7083d3.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.669\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782602 308073fb-7e2c-4270-a6c7-6547def20a2d.PDF\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.670\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782672 33531129-818c-47d8-977f-0820eb76d2e9.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.670\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782004 335dbabf-e1ec-41ab-90db-3a299b5bdc6e.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.671\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782647 34193ed0-3c82-4c5c-a90f-d2a2e8a2ba29.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.671\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782433 34fb275e-1faa-4e3d-bb49-edfcd006ddcf.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.672\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782801 35795076-115e-40be-87b8-fcb61376f29d.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.673\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782873 360e641e-9e01-4b6c-81fa-7e2388fa66c5.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.674\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781998 36f6e764-7e8d-4ac8-9e8b-bb07ed1506e9.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.674\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782888 3940785d-266f-403d-912f-ed816c76bc73.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.675\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782659 3a91c57a-ed49-479a-8472-b0f0ff3e3adb.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.676\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782340 3a9518c8-ce07-474d-8d5b-ee55b196cc9b.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.676\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781971 3c1d9906-788a-48c0-a346-e89110928f8d.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.677\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782570 3dbceb2b-88c6-4ae9-b040-941a94c74ce0.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.678\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782186 47439fba-95e7-4fcb-9671-1c8952b9729f.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.679\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782669 482ca2ba-f597-4403-aecc-06fc22480999.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.679\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782943 4b330b52-6c0d-4d0c-8397-ba10d407c24b.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.680\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782032 4f402f17-5f37-475b-be4a-40d67710ed93.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.680\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782601 599e2a45-ccd2-4116-a648-8376191fef83.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.681\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782956 623b3d0d-66ae-4566-879c-740717699b58.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.682\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782028 68c2f8b4-b5ce-4d2c-8ae7-c88dd593da1b.PDF\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.682\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782409 6abb489a-3bd3-496a-9422-40c46de85121.PDF\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.683\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782796 6b00462f-730c-4afd-be7c-fe5dfcde4f9e.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.684\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782453 6e28f1ac-2f8a-450d-8e1b-28086baf8eee.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.684\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782789 715fed9d-16b6-4b1a-a856-acca01dc53e8.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.685\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782554 7623da79-c45b-4399-9c77-36d6520906c4.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.686\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782279 76892c01-5c93-4797-8dd8-b1b7dba47448.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.687\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782471 76e9b3e5-2caf-409c-a14b-6c47a684cf76.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.687\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782604 7846deae-b381-40bd-90da-136a2531ab6c.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.688\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782914 7932828b-60c7-41f0-8f94-186c9fff6a09.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.688\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782764 7b9560d5-a2d4-48ce-86c6-ff5f60ec083a.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.689\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782701 7c1d1509-c3dd-4813-9a05-30e3fc96f9f0.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.690\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782771 7fac6559-015d-4e04-9b56-b8ce657d6dcb.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.690\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782915 868bcdd8-23e7-457c-b2c8-402c64c203d9.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.691\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782245 86d7dc94-cb69-4dbe-8829-01cbf179dfb7.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.692\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782763 87198a9d-a601-4604-aef5-eb6be1c9a515.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.692\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781992 8752e222-7cba-4e4d-a4d1-39dee844e767.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.693\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781946 8b5d0177-e192-4b1e-92f0-f2e85e0b4724.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.694\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781958 8f296e17-06f7-4351-a7f9-bed18ed51edf.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.694\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782428 8f59457b-86ed-4e8d-8b86-a1df0c16234d.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.695\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782014 914a8b18-1f55-454a-a06e-d340baa13e69.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.696\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782319 922243f2-e399-4d97-8f44-9c6dcaba7def.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.696\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782562 9325df8e-5f69-47c8-84a7-23fc2e3410c3.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.697\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782838 9342db1b-d51d-44ab-b420-b2d4f7a84408.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.698\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782392 991a343b-db42-48f9-a2c6-4733bc00818b.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.698\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781983 9c0fb666-46e3-4269-8817-12446b82a0db.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.699\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782848 9eb8cc06-4199-4a1a-a09f-cbb4d246129b.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.700\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781972 a3cf32cb-be9f-4a8a-b1e4-01d78d98a06b.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.700\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782553 a4689e65-395b-4d64-a51f-10a122ed16ff.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.701\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782235 a4bc2b00-e36c-486b-911e-e6062da64381.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.701\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782896 a6716664-5475-415f-8c2e-ad7fd7650931.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.702\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782066 a9098ada-63c9-48d9-8fcc-a219182caaf5.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.703\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: ******** aeea5fc0-4283-455c-b74e-c3fef9f1b417.PDF\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.703\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782262 b2ea7e5b-2e5e-4457-9845-05a65248bc68.PDF\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.704\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782688 b4b0384c-8a19-4301-a95e-8d3dd99634a8.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.705\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782525 b502d068-d3bb-49ca-b6f8-c62d3b913f67.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.706\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782449 b5684ab8-df5b-4fc3-b42f-2ce146143673.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.706\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782150 b8739994-e458-4c38-a2a0-af3eb28f2f3d.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.707\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782550 baf45e1d-544d-4279-845e-b7a1c2483b22.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.708\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782938 bd073637-f5c2-4b28-8fec-e82e4cab6d75.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.708\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781950 bebbf2fe-fd7f-4f85-83b2-5a0ff90602a6.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.709\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782671 bf08f732-cba7-4194-8bae-399110de3bcb.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.709\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782722 bf57177b-5958-43f7-9907-de3d5a8ac198.PDF\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.710\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782935 c0686456-4df6-4971-acae-5967d5fa03c6.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.711\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782893 c36f4dbb-6965-40d4-9ab0-fd3d84335aca.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.711\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782440 c3bea3ed-9021-4c74-a8cc-6e8c920382d7.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.712\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781957 c4407416-beaf-4995-b623-9188e09f41f6.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.712\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782946 c47ba1b3-18eb-41c0-b6b4-534c2072ed0a.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.713\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781956 c597e074-e15a-4a27-aca9-cce9418a8a9b.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.713\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781989 c59cecec-6f03-4848-a4f8-2d96b8d2c571.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.714\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782296 c5dc26b3-296b-4263-aa6a-46d2d957cab1.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.714\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782716 c9febf04-986b-407d-b604-7aef06f91f0b.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.715\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781966 ca85a248-2ceb-4c8c-9e49-7c33ec8c1185.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.715\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782448 cad26cd8-a44f-4ee3-b0bd-457b192bd80c.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.717\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781942 cc51d137-9fe8-41e2-88e7-5f701e34cea4.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.718\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782595 cda22c8e-2441-442f-b74e-82dff4b4f9e4.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.719\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782617 ce6a3a36-ce49-4e91-a977-48a51be5ad5a.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.719\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782908 d0033a21-fcc6-446d-a4b9-1928ed5f53cf.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.720\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782717 d3af8200-7f9a-4ae6-89f6-26869aeb9214.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.721\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782188 d7463aa5-be0f-4cb1-9a06-62831a7f285d.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.721\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782477 d9300c44-fa8b-47a3-95ee-3217f7467adb.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.722\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781980 e6c530e2-440b-4c7f-af60-8d38dd0f6cc4.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.722\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782121 e80da5b0-91e3-458f-ae77-f733376c7d2c.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.723\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782246 e867af8b-0419-48bd-a291-a45d1e7f3c3c.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.725\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781961 e939ca03-16f2-4f77-ae34-f92d4bc803c0.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.726\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782883 e95db2b4-5171-426b-816b-af605b572ebb.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.727\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782823 e9ae7ed7-6c0b-45d1-a30c-0456a18b803c.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.728\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782169 eb1d2e96-79cc-40f8-b3df-e8e9f9c5e728.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.735\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782933 ed2f4c7e-70a2-45b3-9d5a-5b541878d536.PDF\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.737\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782100 ed36000f-0956-4a6e-a088-e8fb6b4766f0.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.738\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782912 ed3a4869-61d6-4f07-b09e-1cb1191534f6.png\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.742\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782642 edef0a6c-a92c-454d-b7fc-bddb9395e29d.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.744\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782598 edf82e98-e810-46ac-bd12-dc9e92a2ebf9.PDF\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.745\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782851 ef85edc9-3738-46e0-9b78-09fdc1aa8cee.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.750\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782824 f02ba235-8042-49b9-ba6a-c2876d5fe059.jpg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.751\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781969 f2817608-6d63-4d12-8552-6c406e462b27.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.752\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782758 f523ee1b-16cb-425d-9324-2d65140125cd.jpeg\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.753\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782708 f5bfa1e1-22ca-4d6e-894b-15c4cfac5217.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.754\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8781953 f7f8b054-6b09-46a8-8116-d631d1299da5.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.754\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782825 fa9cd670-c0b5-429f-af9b-70b4404d13ba.pdf\u001b[0m\n", "\u001b[32m2025-06-24 16:55:37.755\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgather_all_info\u001b[0m:\u001b[36m63\u001b[0m - \u001b[1mGather infomation: C8782719 fef5b661-05f2-44f4-bfd4-c50cbdf2364e.pdf\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-05-31 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-27 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-28 00:00:00\n", "2025-05-29 00:00:00\n", "2025-04-28 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-28 00:00:00\n", "2025-05-05 00:00:00\n", "2025-05-02 00:00:00\n", "2025-05-23 00:00:00\n", "2025-05-14 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-26 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-23 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-16 00:00:00\n", "2025-05-26 00:00:00\n", "2025-05-27 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "NaT\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-03-06 00:00:00\n", "2025-05-07 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-28 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-27 00:00:00\n", "NaT\n", "2025-04-22 00:00:00\n", "2025-05-23 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-28 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-24 00:00:00\n", "2025-05-27 00:00:00\n", "2025-05-28 00:00:00\n", "2025-05-26 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-28 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-27 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-26 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-26 00:00:00\n", "2025-05-31 00:00:00\n", "2024-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-26 00:00:00\n", "2025-04-26 00:00:00\n", "2025-04-24 00:00:00\n", "2025-05-16 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-20 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-29 00:00:00\n", "2025-03-11 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2025-05-20 00:00:00\n", "2025-05-29 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-30 00:00:00\n", "2025-05-31 00:00:00\n", "2024-02-28 00:00:00\n", "2024-03-14 00:00:00\n", "2025-05-31 00:00:00\n", "2024-08-23 00:00:00\n"]}], "source": ["data_gather_res = gather_all_info(di_res_dict=filtered_di_res_dict,\n", "                                  #stat_res_dict=stat_info_dict, \n", "                                  upm_res_dict=upm_info_dict,\n", "                                  paddleocr_res_dict=paddleocr_info_dict, \n", "                                  claimno2docfile=claimno2docfile, \n", "                                  docfile2claimno=docfile2claimno)"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"data": {"text/plain": ["115"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["len(data_gather_res)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare and Save Results\n", "* If there is any previous results, compare the rule action results\n", "* If there is no previous results, leave an empty column called Rule Change Result\n", "* Results are saved in both JSON and xlsx format"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [], "source": ["# initial output\n", "data_output = []\n", "for info in data_gather_res:\n", "    tmp = deepcopy(info)\n", "    try:\n", "        tmp.pop(\"treatments\", None)\n", "        for treatment in info[\"treatments\"]:\n", "            tmp.update(treatment)\n", "            tmp_output = deepcopy(tmp)\n", "            data_output.append(tmp_output)\n", "            del tmp_output\n", "    except:\n", "        data_output.append(tmp)"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"data": {"text/plain": ["395"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["len(data_output)"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [], "source": ["from openpyxl import load_workbook\n", "\n", "# RULE_RES_OUTPUT_PATH = f\"/workspaces/OCR_in_house/data/{sample_prefix}_samples_DI_rule_res_conf.xlsx\"\n", "\n", "RULE_RES_OUTPUT_PATH = ROOTDIR/f\"data/result/{sample_prefix}_samples_DI_evaluation_raw.xlsx\"\n", "\n", "try:\n", "    # book = load_workbook(RULE_RES_OUTPUT_PATH)\n", "    excel_writer = pd.ExcelWriter(RULE_RES_OUTPUT_PATH, mode=\"a\")\n", "    num_pre_rule_res = len(pd.ExcelFile(RULE_RES_OUTPUT_PATH).sheet_names)\n", "    # excel_writer.book = book\n", "\n", "    logger.info(f\"Number of sheets: {num_pre_rule_res}\")\n", "    pd.DataFrame(data_output).to_excel(excel_writer, sheet_name=f\"Sheet{num_pre_rule_res+1}\", index=False)\n", "    excel_writer.close()\n", "except FileNotFoundError:\n", "    pd.DataFrame(data_output).to_excel(RULE_RES_OUTPUT_PATH, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO combine the rule processed results with UPM data\n", "def gather_all_info(di_res_dict: Dict , \n", "                 \n", "                    upm_res_dict: Dict , \n", "                    paddleocr_res_dict: Dict, \n", "                    claimno2docfile: Dict, \n", "                    docfile2claimno: Dict):\n", "\n", "    summary_list = []\n", "\n", "    # Threshold for considering treatments as similar\n", "    TREATMENT_SIMILARITY_THRESHOLD = 0.7\n", "\n", "\n", "    \n", "    # files processed by Azure Document Intelligence\n", "    for k, v in di_res_dict.items():\n", "        docfile = k\n", "        \n", "        # [{'service_provider': 'Baldivis Vet Hospital Pty Ltd',\n", "        # 'invoice_no': '1/928537',\n", "        # 'invoice_date': datetime.date(2024, 9, 22),\n", "        # 'invoice_total': 2634.57,\n", "        # 'service_provider_conf': 0.888,\n", "        # 'invoice_no_conf': 0.94,\n", "        # 'invoice_date_conf': 0.941,\n", "        # 'invoice_total_conf': 0.93,\n", "        # 'treatments': [{'treatment': 'Consult 19:00 - 23:30',\n", "        #     'amount': 190.0,\n", "        #     'treatment_conf': 0.888,\n", "        #     'amount_conf': 0.889,\n", "        #     'treatmentline_conf': 0.85},\n", "        invoices = v\n", "\n", "        mapping_claimno = docfile2claimno[docfile.lower()]\n", "\n", "        if not mapping_claimno:\n", "            logger.warning(f\"No claim number mapping found for docfile: {docfile}. Skipping.\")\n", "            continue\n", "\n", "        logger.info(f\"Gather infomation: {mapping_claimno} {docfile}\")\n", "\n", "        paddleocr_content = paddleocr_res_dict[docfile.lower()][\"content\"]\n", "\n", "        if len(invoices) > 1:\n", "            print(k, mapping_claimno, len(invoices))\n", "            print(\"-\"*40)\n", "\n", "        # \"claim_no\": claim_no,\n", "        # \"invoice_no\": invoice_no,\n", "        # \"invoice_date\": invoice_date,\n", "        # \"invoice_total\": invoice_total,\n", "        # \"service_provider\": service_provider,\n", "        # \"service_provider_address\": service_provider_address,\n", "        # \"treatments\": [\n", "        #     {\n", "        #         \"date_treatment\": date_treatment,\n", "        #         \"treatment\": treatment,\n", "        #         \"amount\": amount,\n", "        #     }\n", "        # ]\n", "        upm_info = upm_res_dict[mapping_claimno]\n", "        # \"claim_no\"\n", "        # \"doc_conf\"\n", "        # \"invoice_total_ocr\"\n", "        # \"invoice_total_upm\"\n", "        # \"invoice_total_diff\"\n", "        # \"invoice_count_ocr\"\n", "        # \"invoice_count_upm\"\n", "        # \"invoice_count_diff\"\n", "        # \"is_invoice_num_diff\"\n", "        # \"treatment_count_ocr\"\n", "        # \"treatment_count_upm\"\n", "        # \"treatment_count_diff\"\n", "        # \"invoice_no\"\n", "        # \"invoice_date\"\n", "        # \"service_provider_ocr\"\n", "        # \"service_provider_upm\"\n", "        # \"is_service_provider_diff\"\n", "        # \"amount\"\n", "        # \"doc_file\"\n", "        stat_info = stat_res_dict[mapping_claimno]\n", "\n", "        ocr_conf = stat_info[\"doc_conf\"]\n", "        for invoice in invoices:\n", "            # check invoice no\n", "            di_invoice_no = invoice[\"invoice_no\"]\n", "            upm_invoice_no = str(upm_info[\"invoice_no\"])\n", "            if stat_info[\"is_invoice_num_diff\"] == 1:\n", "                ocr_invoice_no = \"\"\n", "            else:\n", "                ocr_invoice_no = upm_invoice_no\n", "            di_invoice_no_correct = int(di_invoice_no == upm_invoice_no)\n", "            ocr_invoice_no_correct = int(ocr_invoice_no == upm_invoice_no)\n", "\n", "            # check service provider\n", "            di_service_provider = invoice[\"service_provider\"]\n", "            di_service_provider_address = invoice[\"service_provider_address\"]\n", "            upm_service_provider = stat_info[\"service_provider_upm\"]\n", "            if upm_info[\"service_provider\"] != upm_service_provider:\n", "                print(\"service_provider mismatch\",k, mapping_claimno)\n", "            ocr_service_provider = stat_info[\"service_provider_ocr\"]\n", "            is_service_provider_diff = stat_info[\"is_service_provider_diff\"]\n", "            di_service_provider_correct = int(di_service_provider == upm_service_provider)\n", "            ocr_service_provider_correct = int(is_service_provider_diff == 0)\n", "\n", "            # check invoice date TODO cannot check Truuth results now 15OCT24\n", "            di_invoice_date = invoice[\"invoice_date\"].isoformat() if not isinstance(invoice[\"invoice_date\"], str) else invoice[\"invoice_date\"]\n", "            upm_invoice_date = stat_info[\"invoice_date\"]\n", "            if upm_info[\"invoice_date\"] and upm_invoice_date != upm_info[\"invoice_date\"].date().isoformat():\n", "                print(\"invoice_date mismatch\", k, mapping_claimno)\n", "            di_invoice_date_correct = int(di_invoice_date == upm_invoice_date)\n", "            # TODO\n", "            ocr_invoice_date_correct = 1\n", "\n", "            # check total amount\n", "            di_total_amount = invoice[\"invoice_total\"]\n", "            upm_total_amount = stat_info[\"invoice_total_upm\"]\n", "            ocr_total_amount = stat_info[\"invoice_total_ocr\"]\n", "            if upm_total_amount != upm_info[\"invoice_total\"]:\n", "                print(\"invoice_total mismatch\", k, mapping_claimno)\n", "            di_total_amount_correct = int(di_total_amount == upm_total_amount)\n", "            ocr_total_amount_correct = int(ocr_total_amount == upm_total_amount)\n", "\n", "            summary = {\n", "                \"claimno\": mapping_claimno,\n", "                \"docfile\": docfile,\n", "                \"paddleocr_content\": paddleocr_content,\n", "                \"ocr_conf\": ocr_conf,\n", "                \"di_conf\": min(invoice[\"invoice_no_conf\"], invoice[\"invoice_date_conf\"], invoice[\"invoice_total_conf\"]),\n", "\n", "                \"di_invoice_no\": di_invoice_no,\n", "                \"upm_invoice_no\": upm_invoice_no,\n", "                \"di_invoice_no_correct\": di_invoice_no_correct,\n", "                \"ocr_invoice_no_correct\": ocr_invoice_no_correct,\n", "                \"di_invoice_no_conf\": invoice[\"invoice_no_conf\"],\n", "\n", "                \"di_service_provider\": di_service_provider,\n", "                \"di_service_provider_address\": di_service_provider_address,\n", "                \"upm_service_provider\": upm_service_provider,\n", "                \"ocr_service_provider\": ocr_service_provider,\n", "                \"di_service_provider_correct\": di_service_provider_correct,\n", "                \"ocr_service_provider_correct\": ocr_service_provider_correct,\n", "                \"di_service_provider_conf\": invoice[\"service_provider_conf\"],\n", "                \"di_abn\": invoice[\"ABN\"],\n", "\n", "                \"di_invoice_date\": di_invoice_date,\n", "                \"upm_invoice_date\": upm_invoice_date,\n", "                \"di_invoice_date_correct\": di_invoice_date_correct,\n", "                \"ocr_invoice_date_correct\": ocr_invoice_date_correct,\n", "                \"di_invoice_date_conf\": invoice[\"invoice_date_conf\"],\n", "\n", "                \"di_total_amount\": di_total_amount,\n", "                \"upm_total_amount\": upm_total_amount,\n", "                \"ocr_total_amount\": ocr_total_amount,\n", "                \"di_total_amount_correct\": di_total_amount_correct,\n", "                \"ocr_total_amount_correct\": ocr_total_amount_correct,\n", "                \"di_total_amount_conf\": invoice[\"invoice_total_conf\"],\n", "\n", "                \"rule_res\": invoice[\"rule_res\"],\n", "                \"if_fallout\": int(\"FALL OUT\" in invoice[\"rule_res\"])\n", "            }\n", "\n", "            # DI treatment \n", "            # \"treatments\": [{'treatment_date': '2024-09-17',\n", "            # 'treatment': ' Veterinary Consultation & Examination: Health Assessment',\n", "            # 'amount': 104.0,\n", "            # 'treatment_date_conf': 0.947,\n", "            # 'treatment_conf': 0.8899999110000089,\n", "            # 'amount_conf': 0.888,\n", "            # 'treatmentline_conf': 0.914},]\n", "            # UPM treatment\n", "            # \"treatments\": [\n", "            #     {\n", "            #         \"date_treatment\": date_treatment,\n", "            #         \"treatment\": treatment,\n", "            #         \"amount\": amount,\n", "            #     }\n", "            # ]\n", "            sorted_di_treatments = sorted(invoice[\"treatments\"], key=lambda x: x[\"amount\"])\n", "            sorted_upm_treatments = sorted(upm_info[\"treatments\"], key=lambda x: x[\"amount\"])\n", "            treatments = []\n", "            di_pointer, upm_pointer = 0, 0\n", "            while di_pointer < len(sorted_di_treatments) or upm_pointer < len(sorted_upm_treatments):\n", "                try:\n", "                    di_treatment = sorted_di_treatments[di_pointer]\n", "                except IndexError:\n", "                    assert di_pointer == len(sorted_di_treatments)\n", "                    di_treatment = {}\n", "                try:\n", "                    upm_treatment = sorted_upm_treatments[upm_pointer]\n", "                except IndexError:\n", "                    assert upm_pointer == len(sorted_upm_treatments)\n", "                    upm_treatment = {}\n", "                # logger.info(di_treatment)\n", "\n", "                # logger.info(upm_treatment)\n", "                if not di_treatment and not upm_treatment:\n", "                    logger.error(f\"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.\")\n", "                    logger.error(f\"sorted_di_treatments: {sorted_di_treatments}\")\n", "                    logger.error(f\"sorted_upm_treatments: {sorted_upm_treatments}\")\n", "                    logger.error(f\"di_pointer: {di_pointer}, upm_pointer: {upm_pointer}\")\n", "                    break\n", "\n", "                if not di_treatment:\n", "                    treatments.append({\n", "                        \"di_treatment_date\": \"\",\n", "                        \"di_treatment\": \"\",\n", "                        \"di_amount\": \"\",\n", "\n", "                        \"upm_treatment_date\": upm_treatment[\"date_treatment\"],\n", "                        \"upm_treatment\": upm_treatment[\"treatment\"],\n", "                        \"upm_amount\": upm_treatment[\"amount\"],\n", "\n", "                    })\n", "                    upm_pointer += 1\n", "                    continue\n", "\n", "                if not upm_treatment:\n", "                    treatments.append({\n", "                        \"di_treatment_date\": di_treatment[\"treatment_date\"],\n", "                        \"di_treatment\": di_treatment[\"treatment\"],\n", "                        \"di_amount\": di_treatment[\"amount\"],\n", "\n", "                        \"upm_treatment_date\": \"\",\n", "                        \"upm_treatment\": \"\",\n", "                        \"upm_amount\": \"\",\n", "\n", "                    })\n", "                    di_pointer += 1\n", "                    continue\n", "\n", "                if di_treatment[\"amount\"] == upm_treatment[\"amount\"]:\n", "                    treatments.append({\n", "                        \"di_treatment_date\": di_treatment[\"treatment_date\"],\n", "                        \"di_treatment\": di_treatment[\"treatment\"],\n", "                        \"di_amount\": di_treatment[\"amount\"],\n", "\n", "                        \"upm_treatment_date\": upm_treatment[\"date_treatment\"],\n", "                        \"upm_treatment\": upm_treatment[\"treatment\"],\n", "                        \"upm_amount\": upm_treatment[\"amount\"],\n", "\n", "                    })\n", "                    di_pointer += 1\n", "                    upm_pointer += 1\n", "                elif di_treatment[\"amount\"] < upm_treatment[\"amount\"]:\n", "                    treatments.append({\n", "                        \"di_treatment_date\": di_treatment[\"treatment_date\"],\n", "                        \"di_treatment\": di_treatment[\"treatment\"],\n", "                        \"di_amount\": di_treatment[\"amount\"],\n", "\n", "                        \"upm_treatment_date\": \"\",\n", "                        \"upm_treatment\": \"\",\n", "                        \"upm_amount\": \"\",\n", "\n", "                    })\n", "                    di_pointer += 1\n", "                else:\n", "                    treatments.append({\n", "                        \"di_treatment_date\": \"\",\n", "                        \"di_treatment\": \"\",\n", "                        \"di_amount\": \"\",\n", "\n", "                        \"upm_treatment_date\": upm_treatment[\"date_treatment\"],\n", "                        \"upm_treatment\": upm_treatment[\"treatment\"],\n", "                        \"upm_amount\": upm_treatment[\"amount\"],\n", "\n", "                    })\n", "                    upm_pointer += 1\n", "            summary[\"treatments\"] = treatments\n", "            summary_list.append(summary)\n", "    return summary_list"]}], "metadata": {"kernelspec": {"display_name": "OCR_in_house", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}