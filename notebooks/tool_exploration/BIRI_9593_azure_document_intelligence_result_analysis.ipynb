# import libraries
import json
import pickle as pk
import os
import glob
import time
from dotenv import load_dotenv
from loguru import logger
import pandas as pd

load_dotenv()
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import HttpResponseError

pd.set_option("display.max_columns", None)

# set `<your-endpoint>` and `<your-key>` variables with the values from the Azure portal
endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPONT")
key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")

# RAW_DATA_FOLDER = "../../data/OCR_in_house/samples/100_samples_DI/"
# MAX_RETRY = 3

# 100 SAMPLES
DATA_FOLDER = "../../data/OCR_in_house/samples/100_samples_DI_split_pages/"
RAW_DATA_STAT_FILE = "../../data/OCR_in_house/data/100_samples_DI.csv"
OUPUT_DATA_FOLDER = "../../data/OCR_in_house/samples/100_samples_DI_res/"
DI_RES_OUTPUT_PATH = "../../data/OCR_in_house/data/100_samples_DI_res_conf.xlsx"
UPM_TRUTH_PATH = "../../data/OCR_in_house/data/100_SourceOfTruth.xlsx"
DI_SUMMARY_PATH = "../../data/OCR_in_house/data/100_samples_DI_summary.xlsx"
# 350 SAMPLES
DATA_FOLDER = "../../data/OCR_in_house/samples/350_samples_DI_split_pages/"
RAW_DATA_STAT_FILE = "../../data/OCR_in_house/data/350_samples_DI.csv"
OUPUT_DATA_FOLDER = "../../data/OCR_in_house/samples/350_samples_DI_res/"
DI_RES_OUTPUT_PATH = "../../data/OCR_in_house/data/350_samples_DI_res_conf.xlsx"
UPM_TRUTH_PATH = "../../data/OCR_in_house/data/350_SourceOfTruth.xlsx"
DI_SUMMARY_PATH = "../../data/OCR_in_house/data/350_samples_DI_summary.xlsx"

# 100 SAMPLES
DATA_FOLDER = "../../data/samples/baldivis_incorrect_invoice_split_pages/"
RAW_DATA_STAT_FILE = "../../data/baldivis_incorrect_invoice.csv"
OUPUT_DATA_FOLDER = "../../data/samples/baldivis_incorrect_invoice_res/"
DI_RES_OUTPUT_PATH = "../../data/baldivis_incorrect_invoice_res_conf.xlsx"
UPM_TRUTH_PATH = "../../data/OCR_in_house/data/100_SourceOfTruth.xlsx"
DI_SUMMARY_PATH = "../../data/OCR_in_house/data/100_samples_DI_summary.xlsx"

from pathlib import Path

try:
    with open(os.path.join(OUPUT_DATA_FOLDER, "ans.pk"), "rb") as fin:
        ans = pk.load(fin)
except FileNotFoundError:
    original_file_path_list = sorted(os.listdir(DATA_FOLDER))
    original_file_path_dict = {}
    for p in original_file_path_list:
        original_file_path_dict[str(Path(p).stem)] = p
    ans = []
    for file_path in sorted(os.listdir(OUPUT_DATA_FOLDER)):
        if file_path.endswith(".json"):
            with open(os.path.join(OUPUT_DATA_FOLDER, file_path), "r") as fin:
                ans.append(
                    {
                        "file_path": original_file_path_dict[str(Path(file_path).stem)],
                        "invoice": json.load(fin),
                    }
                )

stat_df = pd.read_csv(RAW_DATA_STAT_FILE)[
    [
        "Claim Number",
        "Claim Date Created",
        "Document Confidence",
        "AI Type",
        "Is GapOnly",
        "Amount Claimed (OCR)",
        "Amount Claimed (UPM)",
        "Amount Claimed (Difference)",
        "Invoice Count (OCR)",
        "Invoice Count (UPM)",
        "Invoice Count (Difference)",
        "Are Invoice Numbers Different",
        "Treatment Count (OCR)",
        "Treatment Count (UPM)",
        "Treatment Count (Difference)",
        "Is Service Provider Different",
        "Audit Category",
        "Risk Category",
        "OCR_ServiceProviderName",
        "UPM_ServiceProviderName",
        "InvoiceId",
        "ClaimId",
        "CspReferenceNo",
        "ClaimNo",
        "InvoiceNo",
        "DateInvoice",
        "AmountInclVat",
        "DocumentId",
        "ClaimRefNumber",
        "DocumentName",
        "DocumentDate",
        "VetPracticeId",
        "Source",
        "DocumentPath",
        "FileSize",
        "InvoiceLineNo",
        "MatchedOn",
        "ConfidenceRange",
        "DocContainer",
        "DocFile",
    ]
]

stat_df.shape

stat_df.head()



from typing import List, Dict, Tuple, Optional, Union
from tqdm import tqdm


def ckeckpoint_extraction(data_info: List[Dict] | Dict) -> Dict:
    if isinstance(data_info, Dict):
        data_info = [data_info]

    assert isinstance(data_info, List) and all(
        isinstance(data, Dict) for data in data_info
    )

    ans = {}
    for data in tqdm(data_info):
        file_path = data["file_path"]
        invoice = data["invoice"]

        invoice_info = []
        for document in invoice["documents"]:
            service_provider = document["fields"].get("VendorName", {}).get(
                "value", ""
            ) + document["fields"].get("VendorAddressRecipient", {}).get("value", "")
            service_provider_conf = 0
            service_provider_count = int(
                document["fields"].get("VendorName", {}).get("value", "") != ""
            ) + int(
                document["fields"].get("VendorAddressRecipient", {}).get("value", "")
                != ""
            )
            service_provider_conf = document["fields"].get("VendorName", {}).get(
                "confidence", 0.0
            ) + document["fields"].get("VendorAddressRecipient", {}).get(
                "confidence", 0.0
            )
            if service_provider_conf > 0.0:
                service_provider_conf /= service_provider_count

            invoice_no = document["fields"].get("InvoiceId", {}).get("value", "")
            invoice_date = document["fields"].get("InvoiceDate", {}).get("value", "")
            invoice_total = (
                document["fields"]
                .get("InvoiceTotal", {})
                .get("value", {})
                .get("amount", -1)
            )

            invoice_no_conf = (
                document["fields"].get("InvoiceId", {}).get("confidence", 0.0)
            )
            invoice_date_conf = (
                document["fields"].get("InvoiceDate", {}).get("confidence", 0.0)
            )
            invoice_total_conf = (
                document["fields"].get("InvoiceTotal", {}).get("confidence", 0.0)
            )

            treatments = []

            for item in document["fields"].get("Items", {}).get("value", []):
                item_conf = item.get("confidence", 0.0)

                treatment_date = (
                    item.get("value", {}).get("Date", {}).get("value", invoice_date)
                )
                treatment_date_conf = (
                    item.get("value", {})
                    .get("Date", {})
                    .get("confidence", invoice_date_conf)
                )
                if treatment_date_conf is None:
                    treatment_date_conf = item_conf

                desc = item.get("value", {}).get("Description", {}).get("content", "")
                product = (
                    item.get("value", {}).get("ProductCode", {}).get("content", "")
                )
                desc_conf = (
                    item.get("value", {})
                    .get("Description", {})
                    .get("confidence", item_conf)
                    or item_conf
                )
                product_conf = (
                    item.get("value", {})
                    .get("ProductCode", {})
                    .get("confidence", item_conf)
                    or item_conf
                )

                # Ensure desc_conf and product_conf are not None
                desc_conf = desc_conf if desc_conf is not None else 0.0
                product_conf = product_conf if product_conf is not None else 0.0

                desc_conf = (
                    desc_conf * int(desc != "") + product_conf * int(product != "")
                ) / (int(desc != "") + int(product != "") + 1e-7)
                desc = product + " " + desc

                amount = (
                    item.get("value", {})
                    .get("Amount", {})
                    .get("value", {})
                    .get("amount", -1)
                )
                amount_conf = (
                    item.get("value", {}).get("Amount", {}).get("confidence", 0.0)
                )
                if amount_conf is None:
                    amount_conf = item_conf

                treatments.append(
                    {
                        "treatment_date": treatment_date,
                        "treatment": desc,
                        "amount": amount,
                        "treatment_date_conf": treatment_date_conf,
                        "treatment_conf": desc_conf,
                        "amount_conf": amount_conf,
                        "treatmentline_conf": item_conf,
                    }
                )

            invoice_info.append(
                {
                    "service_provider": service_provider,
                    "invoice_no": invoice_no,
                    "invoice_date": invoice_date,
                    "invoice_total": invoice_total,
                    "service_provider_conf": service_provider_conf,
                    "invoice_no_conf": invoice_no_conf,
                    "invoice_date_conf": invoice_date_conf,
                    "invoice_total_conf": invoice_total_conf,
                    "treatments": treatments,
                }
            )

        ans[file_path] = invoice_info

    return ans

ans[50]["invoice"]["documents"][0]["fields"]["Items"]["value"][0]["confidence"]

ans_info = ckeckpoint_extraction(ans)

len(ans_info)

def info2csv(info, path):
    df_data = []
    for k, v in info.items():
        for doc in v:
            for treatment in doc.get("treatments", []):
                date = doc.get("invoice_date", "")
                if not isinstance(date, str):
                    date = date.isoformat()

                treatment_date = treatment.get("treatment_date", "")
                if not isinstance(date, str):
                    treatment_date = treatment_date.isoformat()

                tmp = {
                    "file_path": k,
                    "Invoice No (DI)": doc.get("invoice_no", ""),
                    "Invoice No (DI) conf": doc.get("invoice_no_conf", 0.0),
                    "Service Provider Name (DI)": doc.get("service_provider", ""),
                    "Service Provider Name (DI) conf": doc.get(
                        "service_provider_conf", 0.0
                    ),
                    "Date Treatment (DI)": treatment_date,
                    "Date Treatment (DI) conf": treatment.get(
                        "treatment_date_conf", 0.0
                    ),
                    "Treatment Drug Description (DI)": treatment.get("treatment", ""),
                    "Treatment Drug Description (DI) conf": treatment.get(
                        "treatment_conf", 0.0
                    ),
                    "Amount Inc Vat (DI)": treatment.get("amount", ""),
                    "Amount Inc Vat (DI) conf": treatment.get("amount_conf", 0.0),
                    "DateInvoice (DI)": date,
                    "DateInvoice (DI) conf": doc.get("invoice_date_conf", 0.0),
                    "Amount Claimed (DI)": doc.get("invoice_total", ""),
                    "Amount Claimed (DI) conf": doc.get("invoice_total_conf", 0.0),
                }
                df_data.append(tmp)
    df = pd.DataFrame.from_records(df_data)
    df.to_excel(path)
    return df

di_res_df = info2csv(ans_info, DI_RES_OUTPUT_PATH)

from rich import print

for k in sorted(ans_info.keys()):
    print(k, ans_info[k])
    print("-" * 40)

di_res_df.head()

stat_df[["AmountInclVat", "Amount Claimed (OCR)"]] = stat_df[
    ["AmountInclVat", "Amount Claimed (OCR)"]
].fillna(value=0)

stat_df.head()

def gather_stat_info(df: pd.DataFrame = stat_df) -> Dict:
    ans = {}

    for info in df.to_dict(orient="records"):
        claim_no = info["Claim Number"]
        doc_conf = info["Document Confidence"]

        invoice_total_ocr = info["Amount Claimed (OCR)"]
        invoice_total_upm = info["Amount Claimed (UPM)"]
        invoice_total_diff = info["Amount Claimed (Difference)"]

        invoice_count_ocr = info["Invoice Count (OCR)"]
        invoice_count_upm = info["Invoice Count (UPM)"]
        invoice_count_diff = info["Invoice Count (Difference)"]
        is_invoice_num_diff = info["Are Invoice Numbers Different"]

        treatment_count_ocr = info["Treatment Count (OCR)"]
        treatment_count_upm = info["Treatment Count (UPM)"]
        treatment_count_diff = info["Treatment Count (Difference)"]

        invoice_no = info["InvoiceNo"]
        invoice_date = info["DateInvoice"]

        service_provider_ocr = info["OCR_ServiceProviderName"]
        service_provider_upm = info["UPM_ServiceProviderName"]
        is_service_provider_diff = info["Is Service Provider Different"]

        amount = info["AmountInclVat"]
        doc_file = info["DocFile"]

        ans[claim_no] = {
            "claim_no": claim_no,
            "doc_conf": doc_conf,
            "invoice_total_ocr": invoice_total_ocr,
            "invoice_total_upm": invoice_total_upm,
            "invoice_total_diff": invoice_total_diff,
            "invoice_count_ocr": invoice_count_ocr,
            "invoice_count_upm": invoice_count_upm,
            "invoice_count_diff": invoice_count_diff,
            "is_invoice_num_diff": is_invoice_num_diff,
            "treatment_count_ocr": treatment_count_ocr,
            "treatment_count_upm": treatment_count_upm,
            "treatment_count_diff": treatment_count_diff,
            "invoice_no": invoice_no,
            "invoice_date": invoice_date,
            "service_provider_ocr": service_provider_ocr,
            "service_provider_upm": service_provider_upm,
            "is_service_provider_diff": is_service_provider_diff,
            "amount": amount,
            "doc_file": doc_file,
        }
    return ans

stat_info_dict = gather_stat_info(stat_df)

len(stat_info_dict)

upm_truth_df = pd.read_excel(UPM_TRUTH_PATH)[
    [
        "Claim No",
        "Invoice No",
        "Service Provider Name",
        "Date Treatment",
        "Treatment Drug Description",
        "Amount Inc Vat",
        "Amount Claimed (UPM)",
    ]
]  # 'DateInvoice',
upm_truth_df.head()

upm_truth_df.shape

def gather_upm_info(df: pd.DataFrame = upm_truth_df) -> Dict:
    ans = {}

    for info in df.to_dict(orient="records"):
        claim_no = info["Claim No"]
        invoice_no = info["Invoice No"]
        service_provider = info["Service Provider Name"]
        date_treatment = info["Date Treatment"]
        treatment = info["Treatment Drug Description"]
        amount = info["Amount Inc Vat"]
        invoice_date = info.get("DateInvoice", "")
        invoice_total = info["Amount Claimed (UPM)"]

        if claim_no not in ans:
            ans[claim_no] = {
                "claim_no": claim_no,
                "invoice_no": invoice_no,
                "invoice_date": invoice_date,
                "invoice_total": invoice_total,
                "service_provider": service_provider,
                "treatments": [
                    {
                        "date_treatment": date_treatment,
                        "treatment": treatment,
                        "amount": amount,
                    }
                ],
            }
        else:
            ans[claim_no]["treatments"].append(
                {
                    "date_treatment": date_treatment,
                    "treatment": treatment,
                    "amount": amount,
                }
            )
    return ans

upm_info_dict = gather_upm_info(upm_truth_df)

len(upm_info_dict)

from pathlib import Path


def post_process_di_info(info: Dict) -> Dict:
    ans = {}
    for k, v in info.items():
        if k.lower().endswith("pdf"):
            if len(v[0]["treatments"]) == 0:
                continue
            stem = Path(k).stem
            if str(stem[-1]).isdigit() and str(stem[-2] == "-"):
                stem = str(stem)[:-2]
                if f"{stem}.{k[-3:]}" not in ans:
                    ans[f"{stem}.{k[-3:]}"] = [v[0]]
                else:
                    ans[f"{stem}.{k[-3:]}"].append(v[0])
        else:
            ans[k] = v
    return ans

di_info_dict = post_process_di_info(ans_info)

def map_claimno_docfile(df: pd.DataFrame = stat_df) -> Tuple[Dict, Dict]:
    claimno2docfile = {}
    docfile2claimno = {}

    for claimno, docfile in zip(df["Claim Number"], df["DocFile"]):
        docfile = docfile.lower()
        if claimno not in claimno2docfile:
            claimno2docfile[claimno] = docfile

        if docfile not in docfile2claimno:
            docfile2claimno[docfile] = claimno

    return claimno2docfile, docfile2claimno

claimno2docfile, docfile2claimno = map_claimno_docfile(stat_df)

summary_list = []
for k, v in di_info_dict.items():
    docfile = k
    # [{'service_provider': 'Baldivis Vet Hospital Pty Ltd',
    # 'invoice_no': '1/928537',
    # 'invoice_date': datetime.date(2024, 9, 22),
    # 'invoice_total': 2634.57,
    # 'service_provider_conf': 0.888,
    # 'invoice_no_conf': 0.94,
    # 'invoice_date_conf': 0.941,
    # 'invoice_total_conf': 0.93,
    # 'treatments': [{'treatment': 'Consult 19:00 - 23:30',
    #     'amount': 190.0,
    #     'treatment_conf': 0.888,
    #     'amount_conf': 0.889,
    #     'treatmentline_conf': 0.85},
    invoices = v

    mapping_claimno = docfile2claimno[docfile.lower()]

    if len(invoices) > 1:
        print(k, mapping_claimno, len(invoices))
        print("-" * 40)
        continue

    # "claim_no": claim_no,
    # "invoice_no": invoice_no,
    # "invoice_date": invoice_date,
    # "invoice_total": invoice_total,
    # "service_provider": service_provider,
    # "treatments": [
    #     {
    #         "date_treatment": date_treatment,
    #         "treatment": treatment,
    #         "amount": amount,
    #     }
    # ]
    upm_info = upm_info_dict[mapping_claimno]
    # "claim_no"
    # "doc_conf"
    # "invoice_total_ocr"
    # "invoice_total_upm"
    # "invoice_total_diff"
    # "invoice_count_ocr"
    # "invoice_count_upm"
    # "invoice_count_diff"
    # "is_invoice_num_diff"
    # "treatment_count_ocr"
    # "treatment_count_upm"
    # "treatment_count_diff"
    # "invoice_no"
    # "invoice_date"
    # "service_provider_ocr"
    # "service_provider_upm"
    # "is_service_provider_diff"
    # "amount"
    # "doc_file"
    stat_info = stat_info_dict[mapping_claimno]

    ocr_conf = stat_info["doc_conf"]
    # check invoice no
    di_invoice_no = invoices[0]["invoice_no"]
    upm_invoice_no = str(upm_info["invoice_no"])
    if stat_info["is_invoice_num_diff"] == 1:
        ocr_invoice_no = ""
    else:
        ocr_invoice_no = upm_invoice_no
    di_invoice_no_correct = int(di_invoice_no == upm_invoice_no)
    ocr_invoice_no_correct = int(ocr_invoice_no == upm_invoice_no)

    # check service provider
    di_service_provider = invoices[0]["service_provider"]
    upm_service_provider = stat_info["service_provider_upm"]
    if upm_info["service_provider"] != upm_service_provider:
        print("service_provider mismatch", k, mapping_claimno)
    ocr_service_provider = stat_info["service_provider_ocr"]
    is_service_provider_diff = stat_info["is_service_provider_diff"]
    di_service_provider_correct = int(di_service_provider == upm_service_provider)
    ocr_service_provider_correct = int(is_service_provider_diff == 0)

    # check invoice date TODO cannot check Truuth results now 15OCT24
    di_invoice_date = (
        invoices[0]["invoice_date"].isoformat()
        if not isinstance(invoices[0]["invoice_date"], str)
        else invoices[0]["invoice_date"]
    )
    upm_invoice_date = stat_info["invoice_date"]
    if (
        upm_info["invoice_date"]
        and upm_invoice_date != upm_info["invoice_date"].date().isoformat()
    ):
        print("invoice_date mismatch", k, mapping_claimno)
    di_invoice_date_correct = int(di_invoice_date == upm_invoice_date)
    # TODO
    ocr_invoice_date_correct = 1

    # check total amount
    di_total_amount = invoices[0]["invoice_total"]
    upm_total_amount = stat_info["invoice_total_upm"]
    ocr_total_amount = stat_info["invoice_total_ocr"]
    if upm_total_amount != upm_info["invoice_total"]:
        print("invoice_total mismatch", k, mapping_claimno)
    di_total_amount_correct = int(di_total_amount == upm_total_amount)
    ocr_total_amount_correct = int(ocr_total_amount == upm_total_amount)

    summary = {
        "claimno": mapping_claimno,
        "docfile": docfile,
        "ocr_conf": ocr_conf,
        "di_invoice_no": di_invoice_no,
        "upm_invoice_no": upm_invoice_no,
        "di_invoice_no_correct": di_invoice_no_correct,
        "ocr_invoice_no_correct": ocr_invoice_no_correct,
        "di_invoice_no_conf": invoices[0]["invoice_no_conf"],
        "di_service_provider": di_service_provider,
        "upm_service_provider": upm_service_provider,
        "ocr_service_provider": ocr_service_provider,
        "di_service_provider_correct": di_service_provider_correct,
        "ocr_service_provider_correct": ocr_service_provider_correct,
        "di_service_provider_conf": invoices[0]["service_provider_conf"],
        "di_invoice_date": di_invoice_date,
        "upm_invoice_date": upm_invoice_date,
        "di_invoice_date_correct": di_invoice_date_correct,
        "ocr_invoice_date_correct": ocr_invoice_date_correct,
        "di_invoice_date_conf": invoices[0]["invoice_date_conf"],
        "di_total_amount": di_total_amount,
        "upm_total_amount": upm_total_amount,
        "ocr_total_amount": ocr_total_amount,
        "di_total_amount_correct": di_total_amount_correct,
        "ocr_total_amount_correct": ocr_total_amount_correct,
        "di_total_amount_conf": invoices[0]["invoice_total_conf"],
    }
    summary_list.append(summary)

len(summary_list)

pd.DataFrame(summary_list).to_excel(DI_SUMMARY_PATH, index=False)