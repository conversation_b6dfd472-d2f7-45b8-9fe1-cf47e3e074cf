{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "\n", "sys.path.append(\"..\")\n", "import json\n", "import pickle as pk\n", "import os\n", "import shutil\n", "from pathlib import Path\n", "from typing import Dict, List\n", "from tqdm import tqdm\n", "\n", "import pandas as pd\n", "from loguru import logger\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# load Azure Document Intelligence results from OUTPUT_DATA_FOLDER\n", "def load_document_intelligence_res(res_data_folder: str, raw_data_folder: str) -> List[Dict]:\n", "    try:\n", "        with open(os.path.join(res_data_folder, \"ans.pk\"), \"rb\") as fin:\n", "            ans = pk.load(fin)\n", "    except FileNotFoundError:\n", "        original_file_path_list = sorted(os.listdir(raw_data_folder))\n", "        original_file_path_dict = {}\n", "        for p in original_file_path_list:\n", "            original_file_path_dict[str(Path(p).stem)] = p\n", "        ans = []\n", "        for file_path in sorted(os.listdir(res_data_folder)):\n", "            if file_path.endswith(\".json\"):\n", "                with open(os.path.join(res_data_folder, file_path), \"r\") as fin:\n", "                    ans.append(\n", "                        {\n", "                            \"file_path\": original_file_path_dict[str(Path(file_path).stem)],\n", "                            \"invoice\": json.load(fin),\n", "                            }\n", "                    )\n", "    return ans"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# # sample prefix\n", "# sample_prefix1 = \"1000_600\"\n", "# # Data location\n", "# RAW_DATA_FOLDER1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix1}_samples_DI\"\n", "# DATA_FOLDER1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix1}_samples_DI_split_pages\"\n", "# RAW_DATA_STAT_FILE1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix1}_samples_DI.csv\"\n", "# OUTPUT_DATA_FOLDER1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix1}_samples_DI_res\"\n", "# UPM_GROUND_TRUTH_PATH1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix1}_SourceOfTruth.xlsx\"\n", "# PADDLEOCR_RES_PATH1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix1}_samples_paddleocr.csv\"\n", "\n", "# # sample prefix\n", "# sample_prefix2 = \"1000_400\"\n", "# # Data location\n", "# RAW_DATA_FOLDER2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix2}_samples_DI\"\n", "# DATA_FOLDER2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix2}_samples_DI_split_pages\"\n", "# RAW_DATA_STAT_FILE2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix2}_samples_DI.csv\"\n", "# OUTPUT_DATA_FOLDER2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix2}_samples_DI_res\"\n", "# UPM_GROUND_TRUTH_PATH2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix2}_SourceOfTruth.xlsx\"\n", "# PADDLEOCR_RES_PATH2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix2}_samples_paddleocr.csv\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "ROOTDIR = Path(\"/workspaces/OCR_in_house\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# sample prefix\n", "sample_prefix1 = \"1000_600\"\n", "# Data location\n", "RAW_DATA_FOLDER1 = (ROOTDIR/ f\"samples/{sample_prefix1}_samples_DI\")\n", "DATA_FOLDER1 = (ROOTDIR/ f\"data/samples/{sample_prefix1}_samples_DI_split_pages\")\n", "RAW_DATA_STAT_FILE1 = (ROOTDIR/ f\"data/{sample_prefix1}_samples_DI.csv\")\n", "OUTPUT_DATA_FOLDER1 = (ROOTDIR/ f\"data/samples/{sample_prefix1}_samples_DI_res\")\n", "UPM_GROUND_TRUTH_PATH1 = (ROOTDIR/ f\"data/{sample_prefix1}_SourceOfTruth.xlsx\")\n", "PADDLEOCR_RES_PATH1 = (ROOTDIR/ f\"data/{sample_prefix1}_samples_paddleocr.csv\")\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["946"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["document_intelligence_res1 = load_document_intelligence_res(OUTPUT_DATA_FOLDER1, DATA_FOLDER1)\n", "len(document_intelligence_res1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# sample prefix\n", "sample_prefix2 = \"1000_400\"\n", "# Data location\n", "RAW_DATA_FOLDER2 = (ROOTDIR/ f\"samples/{sample_prefix2}_samples_DI\")\n", "DATA_FOLDER2 = (ROOTDIR/ f\"data/samples/{sample_prefix2}_samples_DI_split_pages\")\n", "RAW_DATA_STAT_FILE2 = (ROOTDIR/ f\"data/{sample_prefix2}_samples_DI.csv\")\n", "OUTPUT_DATA_FOLDER2 = (ROOTDIR/ f\"data/samples/{sample_prefix2}_samples_DI_res\")\n", "UPM_GROUND_TRUTH_PATH2 = (ROOTDIR/ f\"data/{sample_prefix2}_SourceOfTruth.xlsx\")\n", "PADDLEOCR_RES_PATH2 = (ROOTDIR/ f\"data/{sample_prefix2}_samples_paddleocr.csv\")"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["\n", "sample_prefix = \"1000\"\n", "sample = \"nz_csp\"\n", "# Data location\n", "RAW_DATA_FOLDER = f\"/home/<USER>/repos/OCR_in_house/data/samples/{sample}_samples_DI\"\n", "# DATA_FOLDER = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix}_samples_DI_split_pages\"\n", "RAW_DATA_STAT_FILE = f\"/home/<USER>/repos/OCR_in_house/data/OCR_in_house/data/{sample}_samples_DI.csv\"\n", "OUTPUT_DATA_FOLDER = f\"/home/<USER>/repos/OCR_in_house/data/samples/{sample}_samples_DI_res\"\n"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["1039"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["document_intelligence_res = load_document_intelligence_res(OUTPUT_DATA_FOLDER, RAW_DATA_FOLDER)\n", "len(document_intelligence_res)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["1421"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["document_intelligence_res = document_intelligence_res1 + document_intelligence_res2\n", "len(document_intelligence_res)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'file_path': '/workspaces/OCR_in_house/data/samples/1000_600_samples_DI_split_pages/0058d496-8395-402f-8d2d-2d4a63201dfe-0.pdf',\n", " 'invoice': {'api_version': '2023-07-31',\n", "  'model_id': 'prebuilt-invoice',\n", "  'content': 'PET CHEMIST\\nTax Invoice\\nInvoice Date\\nInvoice No\\nCreated Date\\nTracking Code\\n838901\\n03 Oct 2024\\nCustomer:\\nShip To:\\n<PERSON><PERSON><PERSON>\\<PERSON><PERSON><PERSON><PERSON>\\nPO Box 6026\\nPO Box 6026\\nManly, Queensland 4179\\nManly, Queensland 4179\\nAustralia\\nAustralia\\nPhone: **********\\<EMAIL>\\nItem\\nOptions\\nQty\\nUnit Price\\nDiscount\\nSubtotal\\nUricosal Potassium Citrate Mixture (200ml)\\n1\\n$39.68\\n$39.68\\nPayments\\nProduct Cost:\\n$39.68\\nAusPost eParcel\\nPayments\\nMethod\\nRef\\nAmount\\nDelivery Method:\\n(Parcel Post)\\n03 Oct 2024\\nCredit Card\\n$52.39\\n$7.95\\nDiscount:\\n$0.00\\nSub Total:\\n$47.63\\nGST (10%):\\n$4.76\\nTax Invoice Total:\\n$52.39\\nTotal Paid:\\n$52.39\\nOutstanding:\\n$0.00\\nPet Chemist Online\\n29-31 Corporation Circuit\\nP\\n1300 000 738\\nAussie Pet Meds Pty Ltd\\nTweed Heads South\\nE\\<EMAIL>\\nABN: **************\\nNSW 2486\\nW\\nwww.petchemist.com.au',\n", "  'languages': [],\n", "  'pages': [{'page_number': 1,\n", "    'angle': None,\n", "    'width': 8.2639,\n", "    'height': 11.6944,\n", "    'unit': 'inch',\n", "    'lines': [{'content': 'PET CHEMIST',\n", "      'polygon': [{'x': 0.9391, 'y': 0.401},\n", "       {'x': 2.8528, 'y': 0.3959},\n", "       {'x': 2.8528, 'y': 0.6649},\n", "       {'x': 0.9391, 'y': 0.6751}],\n", "      'spans': [{'offset': 0, 'length': 11}]},\n", "     {'content': 'Tax Invoice',\n", "      'polygon': [{'x': 6.8832, 'y': 0.4264},\n", "       {'x': 7.9644, 'y': 0.4264},\n", "       {'x': 7.9644, 'y': 0.6142},\n", "       {'x': 6.8832, 'y': 0.6142}],\n", "      'spans': [{'offset': 12, 'length': 11}]},\n", "     {'content': 'Invoice Date',\n", "      'polygon': [{'x': 0.2893, 'y': 1.0507},\n", "       {'x': 1.0, 'y': 1.0507},\n", "       {'x': 1.0, 'y': 1.1674},\n", "       {'x': 0.2893, 'y': 1.1725}],\n", "      'spans': [{'offset': 24, 'length': 12}]},\n", "     {'content': 'Invoice No',\n", "      'polygon': [{'x': 2.2386, 'y': 1.0456},\n", "       {'x': 2.8477, 'y': 1.0456},\n", "       {'x': 2.8477, 'y': 1.1725},\n", "       {'x': 2.2386, 'y': 1.1725}],\n", "      'spans': [{'offset': 37, 'length': 10}]},\n", "     {'content': 'Created Date',\n", "      'polygon': [{'x': 4.203, 'y': 1.0456},\n", "       {'x': 4.9746, 'y': 1.0456},\n", "       {'x': 4.9746, 'y': 1.1776},\n", "       {'x': 4.203, 'y': 1.1776}],\n", "      'spans': [{'offset': 48, 'length': 12}]},\n", "     {'content': 'Tracking Code',\n", "      'polygon': [{'x': 6.1573, 'y': 1.0405},\n", "       {'x': 7.0101, 'y': 1.0405},\n", "       {'x': 7.0101, 'y': 1.1877},\n", "       {'x': 6.1573, 'y': 1.1877}],\n", "      'spans': [{'offset': 61, 'length': 13}]},\n", "     {'content': '838901',\n", "      'polygon': [{'x': 2.2487, 'y': 1.2029},\n", "       {'x': 2.67, 'y': 1.1979},\n", "       {'x': 2.67, 'y': 1.3146},\n", "       {'x': 2.2487, 'y': 1.3146}],\n", "      'spans': [{'offset': 75, 'length': 6}]},\n", "     {'content': '03 Oct 2024',\n", "      'polygon': [{'x': 4.203, 'y': 1.1979},\n", "       {'x': 4.8883, 'y': 1.1979},\n", "       {'x': 4.8883, 'y': 1.3197},\n", "       {'x': 4.203, 'y': 1.3197}],\n", "      'spans': [{'offset': 82, 'length': 11}]},\n", "     {'content': 'Customer:',\n", "      'polygon': [{'x': 0.335, 'y': 1.6598},\n", "       {'x': 0.9492, 'y': 1.6598},\n", "       {'x': 0.9492, 'y': 1.7867},\n", "       {'x': 0.335, 'y': 1.7816}],\n", "      'spans': [{'offset': 94, 'length': 9}]},\n", "     {'content': 'Ship To:',\n", "      'polygon': [{'x': 4.2284, 'y': 1.6598},\n", "       {'x': 4.6903, 'y': 1.6598},\n", "       {'x': 4.6903, 'y': 1.7968},\n", "       {'x': 4.2284, 'y': 1.7968}],\n", "      'spans': [{'offset': 104, 'length': 8}]},\n", "     {'content': '<PERSON>',\n", "      'polygon': [{'x': 0.3299, 'y': 1.807},\n", "       {'x': 1.0964, 'y': 1.807},\n", "       {'x': 1.0964, 'y': 1.9389},\n", "       {'x': 0.3299, 'y': 1.9389}],\n", "      'spans': [{'offset': 113, 'length': 12}]},\n", "     {'content': '<PERSON>',\n", "      'polygon': [{'x': 4.2182, 'y': 1.807},\n", "       {'x': 4.9898, 'y': 1.807},\n", "       {'x': 4.9898, 'y': 1.9389},\n", "       {'x': 4.2182, 'y': 1.9389}],\n", "      'spans': [{'offset': 126, 'length': 12}]},\n", "     {'content': 'PO Box 6026',\n", "      'polygon': [{'x': 0.335, 'y': 1.9541},\n", "       {'x': 1.0609, 'y': 1.9592},\n", "       {'x': 1.0609, 'y': 2.081},\n", "       {'x': 0.335, 'y': 2.076}],\n", "      'spans': [{'offset': 139, 'length': 11}]},\n", "     {'content': 'PO Box 6026',\n", "      'polygon': [{'x': 4.2233, 'y': 1.9541},\n", "       {'x': 4.9543, 'y': 1.9541},\n", "       {'x': 4.9543, 'y': 2.081},\n", "       {'x': 4.2233, 'y': 2.081}],\n", "      'spans': [{'offset': 151, 'length': 11}]},\n", "     {'content': 'Manly, Queensland 4179',\n", "      'polygon': [{'x': 0.335, 'y': 2.1013},\n", "       {'x': 1.7005, 'y': 2.0912},\n", "       {'x': 1.7005, 'y': 2.2384},\n", "       {'x': 0.335, 'y': 2.2435}],\n", "      'spans': [{'offset': 163, 'length': 22}]},\n", "     {'content': 'Manly, Queensland 4179',\n", "      'polygon': [{'x': 4.2284, 'y': 2.1013},\n", "       {'x': 5.5939, 'y': 2.0963},\n", "       {'x': 5.5939, 'y': 2.2333},\n", "       {'x': 4.2284, 'y': 2.2384}],\n", "      'spans': [{'offset': 186, 'length': 22}]},\n", "     {'content': 'Australia',\n", "      'polygon': [{'x': 0.3299, 'y': 2.2587},\n", "       {'x': 0.8325, 'y': 2.2587},\n", "       {'x': 0.8325, 'y': 2.3856},\n", "       {'x': 0.3299, 'y': 2.3856}],\n", "      'spans': [{'offset': 209, 'length': 9}]},\n", "     {'content': 'Australia',\n", "      'polygon': [{'x': 4.2182, 'y': 2.2587},\n", "       {'x': 4.7309, 'y': 2.2536},\n", "       {'x': 4.736, 'y': 2.3805},\n", "       {'x': 4.2182, 'y': 2.3856}],\n", "      'spans': [{'offset': 219, 'length': 9}]},\n", "     {'content': 'Phone: **********',\n", "      'polygon': [{'x': 0.335, 'y': 2.5226},\n", "       {'x': 1.467, 'y': 2.5226},\n", "       {'x': 1.467, 'y': 2.6495},\n", "       {'x': 0.335, 'y': 2.6546}],\n", "      'spans': [{'offset': 229, 'length': 17}]},\n", "     {'content': '<EMAIL>',\n", "      'polygon': [{'x': 0.3299, 'y': 2.6851},\n", "       {'x': 1.7513, 'y': 2.68},\n", "       {'x': 1.7513, 'y': 2.8119},\n", "       {'x': 0.3299, 'y': 2.817}],\n", "      'spans': [{'offset': 247, 'length': 20}]},\n", "     {'content': 'Item',\n", "      'polygon': [{'x': 0.2233, 'y': 3.2079},\n", "       {'x': 0.4924, 'y': 3.2079},\n", "       {'x': 0.4924, 'y': 3.3195},\n", "       {'x': 0.2233, 'y': 3.3094}],\n", "      'spans': [{'offset': 268, 'length': 4}]},\n", "     {'content': 'Options',\n", "      'polygon': [{'x': 3.0355, 'y': 3.1977},\n", "       {'x': 3.4974, 'y': 3.1977},\n", "       {'x': 3.4924, 'y': 3.3347},\n", "       {'x': 3.0355, 'y': 3.3297}],\n", "      'spans': [{'offset': 273, 'length': 7}]},\n", "     {'content': 'Qty',\n", "      'polygon': [{'x': 4.8071, 'y': 3.1977},\n", "       {'x': 5.0152, 'y': 3.2028},\n", "       {'x': 5.0152, 'y': 3.3347},\n", "       {'x': 4.8071, 'y': 3.3246}],\n", "      'spans': [{'offset': 281, 'length': 3}]},\n", "     {'content': 'Unit Price',\n", "      'polygon': [{'x': 5.4517, 'y': 3.1926},\n", "       {'x': 6.0203, 'y': 3.1977},\n", "       {'x': 6.0203, 'y': 3.3246},\n", "       {'x': 5.4517, 'y': 3.3195}],\n", "      'spans': [{'offset': 285, 'length': 10}]},\n", "     {'content': 'Discount',\n", "      'polygon': [{'x': 6.5025, 'y': 3.2028},\n", "       {'x': 7.0253, 'y': 3.2079},\n", "       {'x': 7.0202, 'y': 3.3195},\n", "       {'x': 6.5025, 'y': 3.3144}],\n", "      'spans': [{'offset': 296, 'length': 8}]},\n", "     {'content': 'Subtotal',\n", "      'polygon': [{'x': 7.538, 'y': 3.1977},\n", "       {'x': 8.0304, 'y': 3.1977},\n", "       {'x': 8.0304, 'y': 3.3195},\n", "       {'x': 7.538, 'y': 3.3144}],\n", "      'spans': [{'offset': 305, 'length': 8}]},\n", "     {'content': 'Uricosal Potassium Citrate Mixture (200ml)',\n", "      'polygon': [{'x': 0.2792, 'y': 3.4109},\n", "       {'x': 2.6446, 'y': 3.4109},\n", "       {'x': 2.6446, 'y': 3.5581},\n", "       {'x': 0.2792, 'y': 3.553}],\n", "      'spans': [{'offset': 314, 'length': 42}]},\n", "     {'content': '1',\n", "      'polygon': [{'x': 4.8629, 'y': 3.421},\n", "       {'x': 4.9238, 'y': 3.416},\n", "       {'x': 4.9289, 'y': 3.5327},\n", "       {'x': 4.868, 'y': 3.5327}],\n", "      'spans': [{'offset': 357, 'length': 1}]},\n", "     {'content': '$39.68',\n", "      'polygon': [{'x': 5.538, 'y': 3.4109},\n", "       {'x': 5.939, 'y': 3.4109},\n", "       {'x': 5.939, 'y': 3.5378},\n", "       {'x': 5.538, 'y': 3.5378}],\n", "      'spans': [{'offset': 359, 'length': 6}]},\n", "     {'content': '$39.68',\n", "      'polygon': [{'x': 7.5634, 'y': 3.416},\n", "       {'x': 7.9644, 'y': 3.4109},\n", "       {'x': 7.9644, 'y': 3.5378},\n", "       {'x': 7.5634, 'y': 3.5378}],\n", "      'spans': [{'offset': 366, 'length': 6}]},\n", "     {'content': 'Payments',\n", "      'polygon': [{'x': 0.3096, 'y': 4.0961},\n", "       {'x': 0.9898, 'y': 4.1012},\n", "       {'x': 0.9848, 'y': 4.2585},\n", "       {'x': 0.3096, 'y': 4.2534}],\n", "      'spans': [{'offset': 373, 'length': 8}]},\n", "     {'content': 'Product Cost:',\n", "      'polygon': [{'x': 5.9543, 'y': 4.0149},\n", "       {'x': 6.7005, 'y': 4.02},\n", "       {'x': 6.7005, 'y': 4.1367},\n", "       {'x': 5.9543, 'y': 4.1316}],\n", "      'spans': [{'offset': 382, 'length': 13}]},\n", "     {'content': '$39.68',\n", "      'polygon': [{'x': 7.472, 'y': 4.0149},\n", "       {'x': 7.8629, 'y': 4.0098},\n", "       {'x': 7.8629, 'y': 4.1367},\n", "       {'x': 7.472, 'y': 4.1418}],\n", "      'spans': [{'offset': 396, 'length': 6}]},\n", "     {'content': 'AusPost eParcel',\n", "      'polygon': [{'x': 6.9492, 'y': 4.2636},\n", "       {'x': 7.8883, 'y': 4.2585},\n", "       {'x': 7.8883, 'y': 4.3854},\n", "       {'x': 6.9492, 'y': 4.3905}],\n", "      'spans': [{'offset': 403, 'length': 15}]},\n", "     {'content': 'Payments',\n", "      'polygon': [{'x': 0.5533, 'y': 4.4565},\n", "       {'x': 1.1421, 'y': 4.4565},\n", "       {'x': 1.1421, 'y': 4.5935},\n", "       {'x': 0.5533, 'y': 4.5884}],\n", "      'spans': [{'offset': 419, 'length': 8}]},\n", "     {'content': 'Method',\n", "      'polygon': [{'x': 1.7563, 'y': 4.4565},\n", "       {'x': 2.2081, 'y': 4.4565},\n", "       {'x': 2.2081, 'y': 4.5783},\n", "       {'x': 1.7563, 'y': 4.5783}],\n", "      'spans': [{'offset': 428, 'length': 6}]},\n", "     {'content': 'Ref',\n", "      'polygon': [{'x': 2.9137, 'y': 4.4565},\n", "       {'x': 3.132, 'y': 4.4514},\n", "       {'x': 3.132, 'y': 4.5732},\n", "       {'x': 2.9188, 'y': 4.5732}],\n", "      'spans': [{'offset': 435, 'length': 3}]},\n", "     {'content': 'Amount',\n", "      'polygon': [{'x': 3.5634, 'y': 4.4565},\n", "       {'x': 4.0355, 'y': 4.4565},\n", "       {'x': 4.0304, 'y': 4.5884},\n", "       {'x': 3.5634, 'y': 4.5834}],\n", "      'spans': [{'offset': 439, 'length': 6}]},\n", "     {'content': 'Delivery Method:',\n", "      'polygon': [{'x': 5.8121, 'y': 4.4159},\n", "       {'x': 6.7055, 'y': 4.4159},\n", "       {'x': 6.7055, 'y': 4.5478},\n", "       {'x': 5.8121, 'y': 4.5428}],\n", "      'spans': [{'offset': 446, 'length': 16}]},\n", "     {'content': '(Parcel Post)',\n", "      'polygon': [{'x': 7.1573, 'y': 4.4006},\n", "       {'x': 7.868, 'y': 4.3956},\n", "       {'x': 7.868, 'y': 4.5681},\n", "       {'x': 7.1573, 'y': 4.5732}],\n", "      'spans': [{'offset': 463, 'length': 13}]},\n", "     {'content': '03 Oct 2024',\n", "      'polygon': [{'x': 0.6041, 'y': 4.6747},\n", "       {'x': 1.2995, 'y': 4.6747},\n", "       {'x': 1.2995, 'y': 4.7965},\n", "       {'x': 0.6041, 'y': 4.7915}],\n", "      'spans': [{'offset': 477, 'length': 11}]},\n", "     {'content': 'Credit Card',\n", "      'polygon': [{'x': 1.8172, 'y': 4.6747},\n", "       {'x': 2.4771, 'y': 4.6747},\n", "       {'x': 2.4721, 'y': 4.7965},\n", "       {'x': 1.8172, 'y': 4.7915}],\n", "      'spans': [{'offset': 489, 'length': 11}]},\n", "     {'content': '$52.39',\n", "      'polygon': [{'x': 3.5685, 'y': 4.6747},\n", "       {'x': 3.9644, 'y': 4.6747},\n", "       {'x': 3.9644, 'y': 4.7915},\n", "       {'x': 3.5685, 'y': 4.7965}],\n", "      'spans': [{'offset': 501, 'length': 6}]},\n", "     {'content': '$7.95',\n", "      'polygon': [{'x': 7.5329, 'y': 4.5681},\n", "       {'x': 7.8629, 'y': 4.5681},\n", "       {'x': 7.8629, 'y': 4.69},\n", "       {'x': 7.538, 'y': 4.69}],\n", "      'spans': [{'offset': 508, 'length': 5}]},\n", "     {'content': 'Discount:',\n", "      'polygon': [{'x': 6.1827, 'y': 4.8219},\n", "       {'x': 6.7005, 'y': 4.8219},\n", "       {'x': 6.7005, 'y': 4.9437},\n", "       {'x': 6.1827, 'y': 4.9437}],\n", "      'spans': [{'offset': 514, 'length': 9}]},\n", "     {'content': '$0.00',\n", "      'polygon': [{'x': 7.5532, 'y': 4.7864},\n", "       {'x': 7.8629, 'y': 4.7813},\n", "       {'x': 7.868, 'y': 4.964},\n", "       {'x': 7.5532, 'y': 4.964}],\n", "      'spans': [{'offset': 524, 'length': 5}]},\n", "     {'content': 'Sub Total:',\n", "      'polygon': [{'x': 6.1421, 'y': 5.0757},\n", "       {'x': 6.7106, 'y': 5.0757},\n", "       {'x': 6.7106, 'y': 5.2026},\n", "       {'x': 6.1421, 'y': 5.1975}],\n", "      'spans': [{'offset': 530, 'length': 10}]},\n", "     {'content': '$47.63',\n", "      'polygon': [{'x': 7.472, 'y': 5.0706},\n", "       {'x': 7.8629, 'y': 5.0706},\n", "       {'x': 7.8629, 'y': 5.2026},\n", "       {'x': 7.472, 'y': 5.2026}],\n", "      'spans': [{'offset': 541, 'length': 6}]},\n", "     {'content': 'GST (10%):',\n", "      'polygon': [{'x': 6.0659, 'y': 5.3193},\n", "       {'x': 6.7055, 'y': 5.3193},\n", "       {'x': 6.7055, 'y': 5.4615},\n", "       {'x': 6.0659, 'y': 5.4564}],\n", "      'spans': [{'offset': 548, 'length': 10}]},\n", "     {'content': '$4.76',\n", "      'polygon': [{'x': 7.5431, 'y': 5.3244},\n", "       {'x': 7.873, 'y': 5.3193},\n", "       {'x': 7.8781, 'y': 5.4462},\n", "       {'x': 7.5431, 'y': 5.4462}],\n", "      'spans': [{'offset': 559, 'length': 5}]},\n", "     {'content': 'Tax Invoice Total:',\n", "      'polygon': [{'x': 5.7055, 'y': 5.8218},\n", "       {'x': 6.7106, 'y': 5.8218},\n", "       {'x': 6.7106, 'y': 5.9538},\n", "       {'x': 5.7055, 'y': 5.9487}],\n", "      'spans': [{'offset': 565, 'length': 18}]},\n", "     {'content': '$52.39',\n", "      'polygon': [{'x': 7.472, 'y': 5.8218},\n", "       {'x': 7.8629, 'y': 5.8269},\n", "       {'x': 7.8629, 'y': 5.9538},\n", "       {'x': 7.472, 'y': 5.9437}],\n", "      'spans': [{'offset': 584, 'length': 6}]},\n", "     {'content': 'Total Paid:',\n", "      'polygon': [{'x': 6.1015, 'y': 6.0655},\n", "       {'x': 6.7106, 'y': 6.0655},\n", "       {'x': 6.7106, 'y': 6.1924},\n", "       {'x': 6.1015, 'y': 6.1924}],\n", "      'spans': [{'offset': 591, 'length': 11}]},\n", "     {'content': '$52.39',\n", "      'polygon': [{'x': 7.472, 'y': 6.0706},\n", "       {'x': 7.8629, 'y': 6.0706},\n", "       {'x': 7.8629, 'y': 6.1974},\n", "       {'x': 7.472, 'y': 6.1924}],\n", "      'spans': [{'offset': 603, 'length': 6}]},\n", "     {'content': 'Outstanding:',\n", "      'polygon': [{'x': 5.9796, 'y': 6.299},\n", "       {'x': 6.7055, 'y': 6.2939},\n", "       {'x': 6.7055, 'y': 6.4715},\n", "       {'x': 5.9796, 'y': 6.4715}],\n", "      'spans': [{'offset': 610, 'length': 12}]},\n", "     {'content': '$0.00',\n", "      'polygon': [{'x': 7.5431, 'y': 6.3142},\n", "       {'x': 7.868, 'y': 6.3142},\n", "       {'x': 7.868, 'y': 6.4462},\n", "       {'x': 7.5431, 'y': 6.4462}],\n", "      'spans': [{'offset': 623, 'length': 5}]},\n", "     {'content': 'Pet Chemist Online',\n", "      'polygon': [{'x': 0.4162, 'y': 10.8011},\n", "       {'x': 1.5279, 'y': 10.8011},\n", "       {'x': 1.5279, 'y': 10.9331},\n", "       {'x': 0.4162, 'y': 10.9331}],\n", "      'spans': [{'offset': 629, 'length': 18}]},\n", "     {'content': '29-31 Corporation Circuit',\n", "      'polygon': [{'x': 2.67, 'y': 10.796},\n", "       {'x': 4.0761, 'y': 10.796},\n", "       {'x': 4.0761, 'y': 10.9432},\n", "       {'x': 2.67, 'y': 10.9432}],\n", "      'spans': [{'offset': 648, 'length': 25}]},\n", "     {'content': 'P',\n", "      'polygon': [{'x': 5.0203, 'y': 10.8113},\n", "       {'x': 5.1116, 'y': 10.8062},\n", "       {'x': 5.1167, 'y': 10.9229},\n", "       {'x': 5.0253, 'y': 10.928}],\n", "      'spans': [{'offset': 674, 'length': 1}]},\n", "     {'content': '1300 000 738',\n", "      'polygon': [{'x': 5.4873, 'y': 10.8011},\n", "       {'x': 6.2487, 'y': 10.8011},\n", "       {'x': 6.2487, 'y': 10.928},\n", "       {'x': 5.4873, 'y': 10.928}],\n", "      'spans': [{'offset': 676, 'length': 12}]},\n", "     {'content': 'Aussie Pet Meds Pty Ltd',\n", "      'polygon': [{'x': 0.4213, 'y': 10.9483},\n", "       {'x': 1.7513, 'y': 10.9483},\n", "       {'x': 1.7513, 'y': 11.0904},\n", "       {'x': 0.4213, 'y': 11.0854}],\n", "      'spans': [{'offset': 689, 'length': 23}]},\n", "     {'content': 'Tweed Heads South',\n", "      'polygon': [{'x': 2.6802, 'y': 10.9432},\n", "       {'x': 3.8122, 'y': 10.9432},\n", "       {'x': 3.8122, 'y': 11.0803},\n", "       {'x': 2.6802, 'y': 11.0803}],\n", "      'spans': [{'offset': 713, 'length': 17}]},\n", "     {'content': 'E',\n", "      'polygon': [{'x': 5.0203, 'y': 10.9483},\n", "       {'x': 5.1167, 'y': 10.9483},\n", "       {'x': 5.1167, 'y': 11.0701},\n", "       {'x': 5.0203, 'y': 11.0651}],\n", "      'spans': [{'offset': 731, 'length': 1}]},\n", "     {'content': '<EMAIL>',\n", "      'polygon': [{'x': 5.4771, 'y': 10.9483},\n", "       {'x': 6.8629, 'y': 10.9585},\n", "       {'x': 6.8578, 'y': 11.0854},\n", "       {'x': 5.4771, 'y': 11.0803}],\n", "      'spans': [{'offset': 733, 'length': 22}]},\n", "     {'content': 'ABN: **************',\n", "      'polygon': [{'x': 0.4213, 'y': 11.1006},\n", "       {'x': 1.599, 'y': 11.1006},\n", "       {'x': 1.599, 'y': 11.2275},\n", "       {'x': 0.4213, 'y': 11.2326}],\n", "      'spans': [{'offset': 756, 'length': 19}]},\n", "     {'content': 'NSW 2486',\n", "      'polygon': [{'x': 2.6802, 'y': 11.1057},\n", "       {'x': 3.2893, 'y': 11.1057},\n", "       {'x': 3.2893, 'y': 11.2275},\n", "       {'x': 2.6802, 'y': 11.2275}],\n", "      'spans': [{'offset': 776, 'length': 8}]},\n", "     {'content': 'W',\n", "      'polygon': [{'x': 5.0304, 'y': 11.1107},\n", "       {'x': 5.137, 'y': 11.1057},\n", "       {'x': 5.1421, 'y': 11.2173},\n", "       {'x': 5.0355, 'y': 11.2173}],\n", "      'spans': [{'offset': 785, 'length': 1}]},\n", "     {'content': 'www.petchemist.com.au',\n", "      'polygon': [{'x': 5.472, 'y': 11.1057},\n", "       {'x': 6.8426, 'y': 11.1057},\n", "       {'x': 6.8426, 'y': 11.2427},\n", "       {'x': 5.472, 'y': 11.2427}],\n", "      'spans': [{'offset': 787, 'length': 21}]}],\n", "    'words': [{'content': 'PET',\n", "      'polygon': [{'x': 0.9492, 'y': 0.4111},\n", "       {'x': 1.4771, 'y': 0.401},\n", "       {'x': 1.4721, 'y': 0.6751},\n", "       {'x': 0.9391, 'y': 0.6751}],\n", "      'span': {'offset': 0, 'length': 3},\n", "      'confidence': 0.996},\n", "     {'content': 'CHEMIST',\n", "      'polygon': [{'x': 1.5685, 'y': 0.401},\n", "       {'x': 2.8426, 'y': 0.4061},\n", "       {'x': 2.8426, 'y': 0.6649},\n", "       {'x': 1.5584, 'y': 0.6751}],\n", "      'span': {'offset': 4, 'length': 7},\n", "      'confidence': 0.992},\n", "     {'content': 'Tax',\n", "      'polygon': [{'x': 6.9086, 'y': 0.4264},\n", "       {'x': 7.2334, 'y': 0.4264},\n", "       {'x': 7.2334, 'y': 0.6192},\n", "       {'x': 6.9136, 'y': 0.6192}],\n", "      'span': {'offset': 12, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': 'Invoice',\n", "      'polygon': [{'x': 7.269, 'y': 0.4264},\n", "       {'x': 7.9542, 'y': 0.4314},\n", "       {'x': 7.9542, 'y': 0.6192},\n", "       {'x': 7.269, 'y': 0.6192}],\n", "      'span': {'offset': 16, 'length': 7},\n", "      'confidence': 0.982},\n", "     {'content': 'Invoice',\n", "      'polygon': [{'x': 0.2893, 'y': 1.0557},\n", "       {'x': 0.7005, 'y': 1.0557},\n", "       {'x': 0.7056, 'y': 1.1725},\n", "       {'x': 0.2944, 'y': 1.1776}],\n", "      'span': {'offset': 24, 'length': 7},\n", "      'confidence': 0.962},\n", "     {'content': 'Date',\n", "      'polygon': [{'x': 0.7411, 'y': 1.0557},\n", "       {'x': 0.9898, 'y': 1.0507},\n", "       {'x': 0.9898, 'y': 1.1725},\n", "       {'x': 0.7411, 'y': 1.1725}],\n", "      'span': {'offset': 32, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'Invoice',\n", "      'polygon': [{'x': 2.2487, 'y': 1.0507},\n", "       {'x': 2.6548, 'y': 1.0557},\n", "       {'x': 2.6548, 'y': 1.1725},\n", "       {'x': 2.2538, 'y': 1.1776}],\n", "      'span': {'offset': 37, 'length': 7},\n", "      'confidence': 0.961},\n", "     {'content': 'No',\n", "      'polygon': [{'x': 2.6751, 'y': 1.0557},\n", "       {'x': 2.8375, 'y': 1.0507},\n", "       {'x': 2.8375, 'y': 1.1725},\n", "       {'x': 2.6802, 'y': 1.1725}],\n", "      'span': {'offset': 45, 'length': 2},\n", "      'confidence': 0.993},\n", "     {'content': 'Created',\n", "      'polygon': [{'x': 4.2132, 'y': 1.0507},\n", "       {'x': 4.6548, 'y': 1.0507},\n", "       {'x': 4.6599, 'y': 1.1826},\n", "       {'x': 4.2132, 'y': 1.1776}],\n", "      'span': {'offset': 48, 'length': 7},\n", "      'confidence': 0.994},\n", "     {'content': 'Date',\n", "      'polygon': [{'x': 4.6903, 'y': 1.0507},\n", "       {'x': 4.9492, 'y': 1.0507},\n", "       {'x': 4.9492, 'y': 1.1826},\n", "       {'x': 4.6954, 'y': 1.1826}],\n", "      'span': {'offset': 56, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'Tracking',\n", "      'polygon': [{'x': 6.1725, 'y': 1.0507},\n", "       {'x': 6.67, 'y': 1.0456},\n", "       {'x': 6.67, 'y': 1.1928},\n", "       {'x': 6.1725, 'y': 1.1877}],\n", "      'span': {'offset': 61, 'length': 8},\n", "      'confidence': 0.994},\n", "     {'content': 'Code',\n", "      'polygon': [{'x': 6.7005, 'y': 1.0456},\n", "       {'x': 6.9999, 'y': 1.0456},\n", "       {'x': 6.9999, 'y': 1.1928},\n", "       {'x': 6.7005, 'y': 1.1928}],\n", "      'span': {'offset': 70, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': '838901',\n", "      'polygon': [{'x': 2.2589, 'y': 1.208},\n", "       {'x': 2.67, 'y': 1.2029},\n", "       {'x': 2.67, 'y': 1.3197},\n", "       {'x': 2.2589, 'y': 1.3197}],\n", "      'span': {'offset': 75, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': '03',\n", "      'polygon': [{'x': 4.2132, 'y': 1.2029},\n", "       {'x': 4.3502, 'y': 1.2029},\n", "       {'x': 4.3502, 'y': 1.3248},\n", "       {'x': 4.2132, 'y': 1.3197}],\n", "      'span': {'offset': 82, 'length': 2},\n", "      'confidence': 0.996},\n", "     {'content': 'Oct',\n", "      'polygon': [{'x': 4.3807, 'y': 1.2029},\n", "       {'x': 4.5888, 'y': 1.2029},\n", "       {'x': 4.5939, 'y': 1.3248},\n", "       {'x': 4.3807, 'y': 1.3248}],\n", "      'span': {'offset': 85, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': '2024',\n", "      'polygon': [{'x': 4.6142, 'y': 1.2029},\n", "       {'x': 4.8781, 'y': 1.1979},\n", "       {'x': 4.8781, 'y': 1.3248},\n", "       {'x': 4.6142, 'y': 1.3248}],\n", "      'span': {'offset': 89, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'Customer:',\n", "      'polygon': [{'x': 0.3452, 'y': 1.6648},\n", "       {'x': 0.9391, 'y': 1.6598},\n", "       {'x': 0.9442, 'y': 1.7867},\n", "       {'x': 0.3452, 'y': 1.7816}],\n", "      'span': {'offset': 94, 'length': 9},\n", "      'confidence': 0.995},\n", "     {'content': 'Ship',\n", "      'polygon': [{'x': 4.2335, 'y': 1.6598},\n", "       {'x': 4.4771, 'y': 1.6598},\n", "       {'x': 4.4822, 'y': 1.7968},\n", "       {'x': 4.2385, 'y': 1.8019}],\n", "      'span': {'offset': 104, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'To:',\n", "      'polygon': [{'x': 4.5127, 'y': 1.6598},\n", "       {'x': 4.6903, 'y': 1.6598},\n", "       {'x': 4.6903, 'y': 1.7968},\n", "       {'x': 4.5177, 'y': 1.7968}],\n", "      'span': {'offset': 109, 'length': 3},\n", "      'confidence': 0.993},\n", "     {'content': '<PERSON>',\n", "      'polygon': [{'x': 0.335, 'y': 1.812},\n", "       {'x': 0.7157, 'y': 1.812},\n", "       {'x': 0.7157, 'y': 1.944},\n", "       {'x': 0.3401, 'y': 1.944}],\n", "      'span': {'offset': 113, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': '<PERSON>',\n", "      'polygon': [{'x': 0.7411, 'y': 1.812},\n", "       {'x': 1.0964, 'y': 1.812},\n", "       {'x': 1.0964, 'y': 1.9389},\n", "       {'x': 0.7411, 'y': 1.944}],\n", "      'span': {'offset': 120, 'length': 5},\n", "      'confidence': 0.995},\n", "     {'content': '<PERSON>',\n", "      'polygon': [{'x': 4.2335, 'y': 1.812},\n", "       {'x': 4.6091, 'y': 1.807},\n", "       {'x': 4.6091, 'y': 1.944},\n", "       {'x': 4.2385, 'y': 1.9389}],\n", "      'span': {'offset': 126, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': '<PERSON>',\n", "      'polygon': [{'x': 4.6345, 'y': 1.807},\n", "       {'x': 4.9797, 'y': 1.812},\n", "       {'x': 4.9797, 'y': 1.944},\n", "       {'x': 4.6345, 'y': 1.944}],\n", "      'span': {'offset': 133, 'length': 5},\n", "      'confidence': 0.995},\n", "     {'content': 'PO',\n", "      'polygon': [{'x': 0.335, 'y': 1.9592},\n", "       {'x': 0.4873, 'y': 1.9592},\n", "       {'x': 0.4873, 'y': 2.081},\n", "       {'x': 0.335, 'y': 2.081}],\n", "      'span': {'offset': 139, 'length': 2},\n", "      'confidence': 0.995},\n", "     {'content': 'Box',\n", "      'polygon': [{'x': 0.5431, 'y': 1.9592},\n", "       {'x': 0.7563, 'y': 1.9592},\n", "       {'x': 0.7563, 'y': 2.081},\n", "       {'x': 0.5431, 'y': 2.081}],\n", "      'span': {'offset': 142, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': '6026',\n", "      'polygon': [{'x': 0.7817, 'y': 1.9592},\n", "       {'x': 1.0508, 'y': 1.9643},\n", "       {'x': 1.0558, 'y': 2.081},\n", "       {'x': 0.7817, 'y': 2.081}],\n", "      'span': {'offset': 146, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'PO',\n", "      'polygon': [{'x': 4.2335, 'y': 1.9592},\n", "       {'x': 4.3807, 'y': 1.9592},\n", "       {'x': 4.3807, 'y': 2.0861},\n", "       {'x': 4.2335, 'y': 2.0861}],\n", "      'span': {'offset': 151, 'length': 2},\n", "      'confidence': 0.995},\n", "     {'content': 'Box',\n", "      'polygon': [{'x': 4.4365, 'y': 1.9592},\n", "       {'x': 4.6548, 'y': 1.9592},\n", "       {'x': 4.6548, 'y': 2.081},\n", "       {'x': 4.4365, 'y': 2.0861}],\n", "      'span': {'offset': 154, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': '6026',\n", "      'polygon': [{'x': 4.6802, 'y': 1.9592},\n", "       {'x': 4.9492, 'y': 1.9592},\n", "       {'x': 4.9492, 'y': 2.081},\n", "       {'x': 4.6802, 'y': 2.081}],\n", "      'span': {'offset': 158, 'length': 4},\n", "      'confidence': 0.986},\n", "     {'content': 'Man<PERSON>,',\n", "      'polygon': [{'x': 0.335, 'y': 2.1013},\n", "       {'x': 0.7056, 'y': 2.1064},\n", "       {'x': 0.7056, 'y': 2.2435},\n", "       {'x': 0.3401, 'y': 2.2435}],\n", "      'span': {'offset': 163, 'length': 6},\n", "      'confidence': 0.994},\n", "     {'content': 'Queensland',\n", "      'polygon': [{'x': 0.731, 'y': 2.1064},\n", "       {'x': 1.401, 'y': 2.1013},\n", "       {'x': 1.401, 'y': 2.2435},\n", "       {'x': 0.736, 'y': 2.2435}],\n", "      'span': {'offset': 170, 'length': 10},\n", "      'confidence': 0.994},\n", "     {'content': '4179',\n", "      'polygon': [{'x': 1.4315, 'y': 2.0963},\n", "       {'x': 1.7005, 'y': 2.0963},\n", "       {'x': 1.7005, 'y': 2.2384},\n", "       {'x': 1.4315, 'y': 2.2435}],\n", "      'span': {'offset': 181, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'Man<PERSON>,',\n", "      'polygon': [{'x': 4.2284, 'y': 2.1064},\n", "       {'x': 4.5989, 'y': 2.1064},\n", "       {'x': 4.604, 'y': 2.2435},\n", "       {'x': 4.2335, 'y': 2.2435}],\n", "      'span': {'offset': 186, 'length': 6},\n", "      'confidence': 0.994},\n", "     {'content': 'Queensland',\n", "      'polygon': [{'x': 4.6294, 'y': 2.1064},\n", "       {'x': 5.2893, 'y': 2.1013},\n", "       {'x': 5.2944, 'y': 2.2384},\n", "       {'x': 4.6294, 'y': 2.2435}],\n", "      'span': {'offset': 193, 'length': 10},\n", "      'confidence': 0.993},\n", "     {'content': '4179',\n", "      'polygon': [{'x': 5.3198, 'y': 2.1013},\n", "       {'x': 5.5939, 'y': 2.1013},\n", "       {'x': 5.5939, 'y': 2.2384},\n", "       {'x': 5.3198, 'y': 2.2384}],\n", "      'span': {'offset': 204, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'Australia',\n", "      'polygon': [{'x': 0.3452, 'y': 2.2638},\n", "       {'x': 0.8173, 'y': 2.2587},\n", "       {'x': 0.8223, 'y': 2.3907},\n", "       {'x': 0.3503, 'y': 2.3856}],\n", "      'span': {'offset': 209, 'length': 9},\n", "      'confidence': 0.992},\n", "     {'content': 'Australia',\n", "      'polygon': [{'x': 4.2385, 'y': 2.2638},\n", "       {'x': 4.7157, 'y': 2.2587},\n", "       {'x': 4.7208, 'y': 2.3856},\n", "       {'x': 4.2385, 'y': 2.3805}],\n", "      'span': {'offset': 219, 'length': 9},\n", "      'confidence': 0.994},\n", "     {'content': 'Phone:',\n", "      'polygon': [{'x': 0.335, 'y': 2.5226},\n", "       {'x': 0.7411, 'y': 2.5277},\n", "       {'x': 0.7411, 'y': 2.6546},\n", "       {'x': 0.3401, 'y': 2.6597}],\n", "      'span': {'offset': 229, 'length': 6},\n", "      'confidence': 0.994},\n", "     {'content': '**********',\n", "      'polygon': [{'x': 0.7665, 'y': 2.5277},\n", "       {'x': 1.4619, 'y': 2.5277},\n", "       {'x': 1.4619, 'y': 2.6546},\n", "       {'x': 0.7665, 'y': 2.6546}],\n", "      'span': {'offset': 236, 'length': 10},\n", "      'confidence': 0.991},\n", "     {'content': '<EMAIL>',\n", "      'polygon': [{'x': 0.335, 'y': 2.6901},\n", "       {'x': 1.7056, 'y': 2.68},\n", "       {'x': 1.7106, 'y': 2.817},\n", "       {'x': 0.335, 'y': 2.8221}],\n", "      'span': {'offset': 247, 'length': 20},\n", "      'confidence': 0.95},\n", "     {'content': 'Item',\n", "      'polygon': [{'x': 0.2284, 'y': 3.2079},\n", "       {'x': 0.4365, 'y': 3.2079},\n", "       {'x': 0.4365, 'y': 3.3195},\n", "       {'x': 0.2284, 'y': 3.3144}],\n", "      'span': {'offset': 268, 'length': 4},\n", "      'confidence': 0.914},\n", "     {'content': 'Options',\n", "      'polygon': [{'x': 3.0406, 'y': 3.2028},\n", "       {'x': 3.4924, 'y': 3.2028},\n", "       {'x': 3.4924, 'y': 3.3347},\n", "       {'x': 3.0507, 'y': 3.3347}],\n", "      'span': {'offset': 273, 'length': 7},\n", "      'confidence': 0.994},\n", "     {'content': 'Qty',\n", "      'polygon': [{'x': 4.8121, 'y': 3.1977},\n", "       {'x': 5.0101, 'y': 3.2028},\n", "       {'x': 5.005, 'y': 3.3347},\n", "       {'x': 4.8071, 'y': 3.3246}],\n", "      'span': {'offset': 281, 'length': 3},\n", "      'confidence': 0.992},\n", "     {'content': 'Unit',\n", "      'polygon': [{'x': 5.4619, 'y': 3.1977},\n", "       {'x': 5.7005, 'y': 3.1977},\n", "       {'x': 5.7005, 'y': 3.3246},\n", "       {'x': 5.4619, 'y': 3.3195}],\n", "      'span': {'offset': 285, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'Price',\n", "      'polygon': [{'x': 5.7258, 'y': 3.1977},\n", "       {'x': 6.0101, 'y': 3.1977},\n", "       {'x': 6.0101, 'y': 3.3297},\n", "       {'x': 5.7258, 'y': 3.3246}],\n", "      'span': {'offset': 290, 'length': 5},\n", "      'confidence': 0.995},\n", "     {'content': 'Discount',\n", "      'polygon': [{'x': 6.5126, 'y': 3.2028},\n", "       {'x': 7.0202, 'y': 3.2079},\n", "       {'x': 7.0202, 'y': 3.3246},\n", "       {'x': 6.5076, 'y': 3.3144}],\n", "      'span': {'offset': 296, 'length': 8},\n", "      'confidence': 0.982},\n", "     {'content': 'Subtotal',\n", "      'polygon': [{'x': 7.5532, 'y': 3.2028},\n", "       {'x': 8.0304, 'y': 3.1977},\n", "       {'x': 8.0304, 'y': 3.3195},\n", "       {'x': 7.5532, 'y': 3.3144}],\n", "      'span': {'offset': 305, 'length': 8},\n", "      'confidence': 0.993},\n", "     {'content': 'Uricosal',\n", "      'polygon': [{'x': 0.2893, 'y': 3.416},\n", "       {'x': 0.7411, 'y': 3.416},\n", "       {'x': 0.7462, 'y': 3.5479},\n", "       {'x': 0.2893, 'y': 3.5479}],\n", "      'span': {'offset': 314, 'length': 8},\n", "      'confidence': 0.994},\n", "     {'content': 'Potassium',\n", "      'polygon': [{'x': 0.7665, 'y': 3.416},\n", "       {'x': 1.3096, 'y': 3.416},\n", "       {'x': 1.3147, 'y': 3.5479},\n", "       {'x': 0.7716, 'y': 3.5479}],\n", "      'span': {'offset': 323, 'length': 9},\n", "      'confidence': 0.993},\n", "     {'content': 'Citrate',\n", "      'polygon': [{'x': 1.3807, 'y': 3.416},\n", "       {'x': 1.7462, 'y': 3.416},\n", "       {'x': 1.7462, 'y': 3.5479},\n", "       {'x': 1.3858, 'y': 3.5479}],\n", "      'span': {'offset': 333, 'length': 7},\n", "      'confidence': 0.993},\n", "     {'content': 'Mixture',\n", "      'polygon': [{'x': 1.7716, 'y': 3.416},\n", "       {'x': 2.1878, 'y': 3.416},\n", "       {'x': 2.1929, 'y': 3.553},\n", "       {'x': 1.7766, 'y': 3.5479}],\n", "      'span': {'offset': 341, 'length': 7},\n", "      'confidence': 0.993},\n", "     {'content': '(200ml)',\n", "      'polygon': [{'x': 2.2132, 'y': 3.416},\n", "       {'x': 2.6446, 'y': 3.4109},\n", "       {'x': 2.6446, 'y': 3.5632},\n", "       {'x': 2.2183, 'y': 3.5581}],\n", "      'span': {'offset': 349, 'length': 7},\n", "      'confidence': 0.993},\n", "     {'content': '1',\n", "      'polygon': [{'x': 4.8629, 'y': 3.416},\n", "       {'x': 4.9187, 'y': 3.416},\n", "       {'x': 4.9238, 'y': 3.5276},\n", "       {'x': 4.868, 'y': 3.5327}],\n", "      'span': {'offset': 357, 'length': 1},\n", "      'confidence': 0.995},\n", "     {'content': '$39.68',\n", "      'polygon': [{'x': 5.5431, 'y': 3.416},\n", "       {'x': 5.9441, 'y': 3.416},\n", "       {'x': 5.9441, 'y': 3.5428},\n", "       {'x': 5.5482, 'y': 3.5428}],\n", "      'span': {'offset': 359, 'length': 6},\n", "      'confidence': 0.994},\n", "     {'content': '$39.68',\n", "      'polygon': [{'x': 7.5735, 'y': 3.421},\n", "       {'x': 7.9593, 'y': 3.416},\n", "       {'x': 7.9593, 'y': 3.5428},\n", "       {'x': 7.5786, 'y': 3.5378}],\n", "      'span': {'offset': 366, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': 'Payments',\n", "      'polygon': [{'x': 0.3096, 'y': 4.1012},\n", "       {'x': 0.9848, 'y': 4.1113},\n", "       {'x': 0.9848, 'y': 4.2534},\n", "       {'x': 0.3096, 'y': 4.2534}],\n", "      'span': {'offset': 373, 'length': 8},\n", "      'confidence': 0.993},\n", "     {'content': 'Product',\n", "      'polygon': [{'x': 5.9543, 'y': 4.02},\n", "       {'x': 6.3908, 'y': 4.02},\n", "       {'x': 6.3908, 'y': 4.1367},\n", "       {'x': 5.9593, 'y': 4.1367}],\n", "      'span': {'offset': 382, 'length': 7},\n", "      'confidence': 0.971},\n", "     {'content': 'Cost:',\n", "      'polygon': [{'x': 6.4111, 'y': 4.02},\n", "       {'x': 6.6954, 'y': 4.025},\n", "       {'x': 6.7005, 'y': 4.1367},\n", "       {'x': 6.4162, 'y': 4.1367}],\n", "      'span': {'offset': 390, 'length': 5},\n", "      'confidence': 0.994},\n", "     {'content': '$39.68',\n", "      'polygon': [{'x': 7.4771, 'y': 4.02},\n", "       {'x': 7.8629, 'y': 4.0149},\n", "       {'x': 7.8629, 'y': 4.1418},\n", "       {'x': 7.4771, 'y': 4.1418}],\n", "      'span': {'offset': 396, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': 'AusPost',\n", "      'polygon': [{'x': 6.9644, 'y': 4.2636},\n", "       {'x': 7.4213, 'y': 4.2636},\n", "       {'x': 7.4213, 'y': 4.3905},\n", "       {'x': 6.9644, 'y': 4.3956}],\n", "      'span': {'offset': 403, 'length': 7},\n", "      'confidence': 0.983},\n", "     {'content': 'eParcel',\n", "      'polygon': [{'x': 7.4466, 'y': 4.2636},\n", "       {'x': 7.8832, 'y': 4.2585},\n", "       {'x': 7.8832, 'y': 4.3905},\n", "       {'x': 7.4466, 'y': 4.3905}],\n", "      'span': {'offset': 411, 'length': 7},\n", "      'confidence': 0.965},\n", "     {'content': 'Payments',\n", "      'polygon': [{'x': 0.5584, 'y': 4.4616},\n", "       {'x': 1.1421, 'y': 4.4616},\n", "       {'x': 1.1421, 'y': 4.5935},\n", "       {'x': 0.5634, 'y': 4.5884}],\n", "      'span': {'offset': 419, 'length': 8},\n", "      'confidence': 0.994},\n", "     {'content': 'Method',\n", "      'polygon': [{'x': 1.7614, 'y': 4.4565},\n", "       {'x': 2.1776, 'y': 4.4616},\n", "       {'x': 2.1776, 'y': 4.5834},\n", "       {'x': 1.7614, 'y': 4.5783}],\n", "      'span': {'offset': 428, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': 'Ref',\n", "      'polygon': [{'x': 2.9289, 'y': 4.4514},\n", "       {'x': 3.1218, 'y': 4.4514},\n", "       {'x': 3.1269, 'y': 4.5732},\n", "       {'x': 2.9289, 'y': 4.5732}],\n", "      'span': {'offset': 435, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': 'Amount',\n", "      'polygon': [{'x': 3.5786, 'y': 4.4616},\n", "       {'x': 4.0355, 'y': 4.4565},\n", "       {'x': 4.0304, 'y': 4.5884},\n", "       {'x': 3.5786, 'y': 4.5783}],\n", "      'span': {'offset': 439, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': 'Delivery',\n", "      'polygon': [{'x': 5.8121, 'y': 4.4209},\n", "       {'x': 6.2334, 'y': 4.4159},\n", "       {'x': 6.2334, 'y': 4.5478},\n", "       {'x': 5.8121, 'y': 4.5478}],\n", "      'span': {'offset': 446, 'length': 8},\n", "      'confidence': 0.995},\n", "     {'content': 'Method:',\n", "      'polygon': [{'x': 6.2588, 'y': 4.4159},\n", "       {'x': 6.7005, 'y': 4.4209},\n", "       {'x': 6.7055, 'y': 4.5529},\n", "       {'x': 6.2639, 'y': 4.5529}],\n", "      'span': {'offset': 455, 'length': 7},\n", "      'confidence': 0.987},\n", "     {'content': '(<PERSON><PERSON><PERSON>',\n", "      'polygon': [{'x': 7.1624, 'y': 4.4057},\n", "       {'x': 7.5431, 'y': 4.4057},\n", "       {'x': 7.538, 'y': 4.5732},\n", "       {'x': 7.1624, 'y': 4.5681}],\n", "      'span': {'offset': 463, 'length': 7},\n", "      'confidence': 0.99},\n", "     {'content': 'Post)',\n", "      'polygon': [{'x': 7.5735, 'y': 4.4006},\n", "       {'x': 7.868, 'y': 4.4006},\n", "       {'x': 7.868, 'y': 4.5732},\n", "       {'x': 7.5735, 'y': 4.5732}],\n", "      'span': {'offset': 471, 'length': 5},\n", "      'confidence': 0.994},\n", "     {'content': '03',\n", "      'polygon': [{'x': 0.6142, 'y': 4.6747},\n", "       {'x': 0.7614, 'y': 4.6747},\n", "       {'x': 0.7614, 'y': 4.7965},\n", "       {'x': 0.6193, 'y': 4.7965}],\n", "      'span': {'offset': 477, 'length': 2},\n", "      'confidence': 0.994},\n", "     {'content': 'Oct',\n", "      'polygon': [{'x': 0.7817, 'y': 4.6747},\n", "       {'x': 0.9949, 'y': 4.6747},\n", "       {'x': 0.9949, 'y': 4.7965},\n", "       {'x': 0.7868, 'y': 4.7965}],\n", "      'span': {'offset': 480, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': '2024',\n", "      'polygon': [{'x': 1.0152, 'y': 4.6747},\n", "       {'x': 1.2843, 'y': 4.6747},\n", "       {'x': 1.2843, 'y': 4.7965},\n", "       {'x': 1.0203, 'y': 4.7965}],\n", "      'span': {'offset': 484, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'Credit',\n", "      'polygon': [{'x': 1.8274, 'y': 4.6747},\n", "       {'x': 2.1726, 'y': 4.6747},\n", "       {'x': 2.1726, 'y': 4.7965},\n", "       {'x': 1.8325, 'y': 4.7965}],\n", "      'span': {'offset': 489, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': 'Card',\n", "      'polygon': [{'x': 2.1929, 'y': 4.6747},\n", "       {'x': 2.4568, 'y': 4.6747},\n", "       {'x': 2.4619, 'y': 4.8016},\n", "       {'x': 2.198, 'y': 4.7965}],\n", "      'span': {'offset': 496, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': '$52.39',\n", "      'polygon': [{'x': 3.5685, 'y': 4.6798},\n", "       {'x': 3.9594, 'y': 4.6747},\n", "       {'x': 3.9594, 'y': 4.7965},\n", "       {'x': 3.5736, 'y': 4.8016}],\n", "      'span': {'offset': 501, 'length': 6},\n", "      'confidence': 0.994},\n", "     {'content': '$7.95',\n", "      'polygon': [{'x': 7.5482, 'y': 4.5681},\n", "       {'x': 7.8578, 'y': 4.5681},\n", "       {'x': 7.8578, 'y': 4.69},\n", "       {'x': 7.5482, 'y': 4.69}],\n", "      'span': {'offset': 508, 'length': 5},\n", "      'confidence': 0.994},\n", "     {'content': 'Discount:',\n", "      'polygon': [{'x': 6.1878, 'y': 4.8372},\n", "       {'x': 6.7005, 'y': 4.827},\n", "       {'x': 6.7005, 'y': 4.9488},\n", "       {'x': 6.1878, 'y': 4.9387}],\n", "      'span': {'offset': 514, 'length': 9},\n", "      'confidence': 0.993},\n", "     {'content': '$0.00',\n", "      'polygon': [{'x': 7.5532, 'y': 4.7813},\n", "       {'x': 7.8578, 'y': 4.7813},\n", "       {'x': 7.8578, 'y': 4.964},\n", "       {'x': 7.5532, 'y': 4.964}],\n", "      'span': {'offset': 524, 'length': 5},\n", "      'confidence': 0.986},\n", "     {'content': 'Sub',\n", "      'polygon': [{'x': 6.1522, 'y': 5.0808},\n", "       {'x': 6.3502, 'y': 5.0757},\n", "       {'x': 6.3553, 'y': 5.1975},\n", "       {'x': 6.1573, 'y': 5.1975}],\n", "      'span': {'offset': 530, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': 'Total:',\n", "      'polygon': [{'x': 6.4162, 'y': 5.0757},\n", "       {'x': 6.7157, 'y': 5.0757},\n", "       {'x': 6.7157, 'y': 5.2026},\n", "       {'x': 6.4162, 'y': 5.1975}],\n", "      'span': {'offset': 534, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': '$47.63',\n", "      'polygon': [{'x': 7.4771, 'y': 5.0808},\n", "       {'x': 7.8578, 'y': 5.0757},\n", "       {'x': 7.8578, 'y': 5.2077},\n", "       {'x': 7.4771, 'y': 5.2077}],\n", "      'span': {'offset': 541, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': 'GST',\n", "      'polygon': [{'x': 6.071, 'y': 5.3244},\n", "       {'x': 6.3147, 'y': 5.3193},\n", "       {'x': 6.3147, 'y': 5.4615},\n", "       {'x': 6.071, 'y': 5.4564}],\n", "      'span': {'offset': 548, 'length': 3},\n", "      'confidence': 0.994},\n", "     {'content': '(10%):',\n", "      'polygon': [{'x': 6.34, 'y': 5.3193},\n", "       {'x': 6.7106, 'y': 5.3244},\n", "       {'x': 6.7106, 'y': 5.4665},\n", "       {'x': 6.34, 'y': 5.4615}],\n", "      'span': {'offset': 552, 'length': 6},\n", "      'confidence': 0.775},\n", "     {'content': '$4.76',\n", "      'polygon': [{'x': 7.5431, 'y': 5.3193},\n", "       {'x': 7.8629, 'y': 5.3193},\n", "       {'x': 7.8629, 'y': 5.4462},\n", "       {'x': 7.5431, 'y': 5.4462}],\n", "      'span': {'offset': 559, 'length': 5},\n", "      'confidence': 0.995},\n", "     {'content': 'Tax',\n", "      'polygon': [{'x': 5.7258, 'y': 5.8218},\n", "       {'x': 5.9187, 'y': 5.8218},\n", "       {'x': 5.9187, 'y': 5.9538},\n", "       {'x': 5.7258, 'y': 5.9538}],\n", "      'span': {'offset': 565, 'length': 3},\n", "      'confidence': 0.997},\n", "     {'content': 'Invoice',\n", "      'polygon': [{'x': 5.9441, 'y': 5.8218},\n", "       {'x': 6.3553, 'y': 5.8269},\n", "       {'x': 6.3553, 'y': 5.9487},\n", "       {'x': 5.9441, 'y': 5.9538}],\n", "      'span': {'offset': 569, 'length': 7},\n", "      'confidence': 0.992},\n", "     {'content': 'Total:',\n", "      'polygon': [{'x': 6.3857, 'y': 5.8269},\n", "       {'x': 6.7106, 'y': 5.8269},\n", "       {'x': 6.7106, 'y': 5.9538},\n", "       {'x': 6.3908, 'y': 5.9487}],\n", "      'span': {'offset': 577, 'length': 6},\n", "      'confidence': 0.994},\n", "     {'content': '$52.39',\n", "      'polygon': [{'x': 7.472, 'y': 5.8218},\n", "       {'x': 7.8578, 'y': 5.832},\n", "       {'x': 7.8578, 'y': 5.9487},\n", "       {'x': 7.4771, 'y': 5.9487}],\n", "      'span': {'offset': 584, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': 'Total',\n", "      'polygon': [{'x': 6.1218, 'y': 6.0706},\n", "       {'x': 6.3959, 'y': 6.0706},\n", "       {'x': 6.3959, 'y': 6.1974},\n", "       {'x': 6.1218, 'y': 6.1924}],\n", "      'span': {'offset': 591, 'length': 5},\n", "      'confidence': 0.995},\n", "     {'content': 'Paid:',\n", "      'polygon': [{'x': 6.4213, 'y': 6.0706},\n", "       {'x': 6.7055, 'y': 6.0706},\n", "       {'x': 6.7055, 'y': 6.1924},\n", "       {'x': 6.4213, 'y': 6.1974}],\n", "      'span': {'offset': 597, 'length': 5},\n", "      'confidence': 0.995},\n", "     {'content': '$52.39',\n", "      'polygon': [{'x': 7.4771, 'y': 6.0756},\n", "       {'x': 7.8578, 'y': 6.0756},\n", "       {'x': 7.8629, 'y': 6.1974},\n", "       {'x': 7.4822, 'y': 6.1974}],\n", "      'span': {'offset': 603, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': 'Outstanding:',\n", "      'polygon': [{'x': 5.9847, 'y': 6.304},\n", "       {'x': 6.7005, 'y': 6.299},\n", "       {'x': 6.7005, 'y': 6.4766},\n", "       {'x': 5.9847, 'y': 6.4715}],\n", "      'span': {'offset': 610, 'length': 12},\n", "      'confidence': 0.991},\n", "     {'content': '$0.00',\n", "      'polygon': [{'x': 7.5431, 'y': 6.3142},\n", "       {'x': 7.8578, 'y': 6.3142},\n", "       {'x': 7.8578, 'y': 6.4462},\n", "       {'x': 7.5431, 'y': 6.4462}],\n", "      'span': {'offset': 623, 'length': 5},\n", "      'confidence': 0.993},\n", "     {'content': 'Pet',\n", "      'polygon': [{'x': 0.4264, 'y': 10.8011},\n", "       {'x': 0.6244, 'y': 10.8011},\n", "       {'x': 0.6244, 'y': 10.9382},\n", "       {'x': 0.4315, 'y': 10.9382}],\n", "      'span': {'offset': 629, 'length': 3},\n", "      'confidence': 0.996},\n", "     {'content': 'Chemist',\n", "      'polygon': [{'x': 0.6497, 'y': 10.8011},\n", "       {'x': 1.1218, 'y': 10.8011},\n", "       {'x': 1.1218, 'y': 10.9382},\n", "       {'x': 0.6497, 'y': 10.9382}],\n", "      'span': {'offset': 633, 'length': 7},\n", "      'confidence': 0.995},\n", "     {'content': 'Online',\n", "      'polygon': [{'x': 1.1472, 'y': 10.8011},\n", "       {'x': 1.5178, 'y': 10.8062},\n", "       {'x': 1.5178, 'y': 10.9382},\n", "       {'x': 1.1472, 'y': 10.9382}],\n", "      'span': {'offset': 641, 'length': 6},\n", "      'confidence': 0.995},\n", "     {'content': '29-31',\n", "      'polygon': [{'x': 2.6903, 'y': 10.8011},\n", "       {'x': 3.0051, 'y': 10.8011},\n", "       {'x': 3.0051, 'y': 10.9432},\n", "       {'x': 2.6954, 'y': 10.9432}],\n", "      'span': {'offset': 648, 'length': 5},\n", "      'confidence': 0.993},\n", "     {'content': 'Corporation',\n", "      'polygon': [{'x': 3.0304, 'y': 10.8011},\n", "       {'x': 3.6751, 'y': 10.8011},\n", "       {'x': 3.6802, 'y': 10.9483},\n", "       {'x': 3.0355, 'y': 10.9432}],\n", "      'span': {'offset': 654, 'length': 11},\n", "      'confidence': 0.991},\n", "     {'content': 'Circuit',\n", "      'polygon': [{'x': 3.7157, 'y': 10.8011},\n", "       {'x': 4.0761, 'y': 10.796},\n", "       {'x': 4.0812, 'y': 10.9432},\n", "       {'x': 3.7157, 'y': 10.9483}],\n", "      'span': {'offset': 666, 'length': 7},\n", "      'confidence': 0.993},\n", "     {'content': 'P',\n", "      'polygon': [{'x': 5.0203, 'y': 10.8113},\n", "       {'x': 5.0812, 'y': 10.8062},\n", "       {'x': 5.0863, 'y': 10.9229},\n", "       {'x': 5.0253, 'y': 10.928}],\n", "      'span': {'offset': 674, 'length': 1},\n", "      'confidence': 0.995},\n", "     {'content': '1300',\n", "      'polygon': [{'x': 5.4923, 'y': 10.8062},\n", "       {'x': 5.7563, 'y': 10.8062},\n", "       {'x': 5.7614, 'y': 10.9331},\n", "       {'x': 5.4974, 'y': 10.928}],\n", "      'span': {'offset': 676, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': '000',\n", "      'polygon': [{'x': 5.7918, 'y': 10.8062},\n", "       {'x': 5.9949, 'y': 10.8062},\n", "       {'x': 5.9999, 'y': 10.9331},\n", "       {'x': 5.7918, 'y': 10.9331}],\n", "      'span': {'offset': 681, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': '738',\n", "      'polygon': [{'x': 6.0406, 'y': 10.8062},\n", "       {'x': 6.2436, 'y': 10.8062},\n", "       {'x': 6.2436, 'y': 10.9331},\n", "       {'x': 6.0406, 'y': 10.9331}],\n", "      'span': {'offset': 685, 'length': 3},\n", "      'confidence': 0.996},\n", "     {'content': 'Aussie',\n", "      'polygon': [{'x': 0.4315, 'y': 10.9534},\n", "       {'x': 0.7969, 'y': 10.9534},\n", "       {'x': 0.802, 'y': 11.0854},\n", "       {'x': 0.4315, 'y': 11.0854}],\n", "      'span': {'offset': 689, 'length': 6},\n", "      'confidence': 0.993},\n", "     {'content': 'Pet',\n", "      'polygon': [{'x': 0.8223, 'y': 10.9534},\n", "       {'x': 1.0051, 'y': 10.9534},\n", "       {'x': 1.0051, 'y': 11.0854},\n", "       {'x': 0.8274, 'y': 11.0854}],\n", "      'span': {'offset': 696, 'length': 3},\n", "      'confidence': 0.992},\n", "     {'content': 'Meds',\n", "      'polygon': [{'x': 1.0304, 'y': 10.9534},\n", "       {'x': 1.335, 'y': 10.9534},\n", "       {'x': 1.335, 'y': 11.0904},\n", "       {'x': 1.0304, 'y': 11.0854}],\n", "      'span': {'offset': 700, 'length': 4},\n", "      'confidence': 0.992},\n", "     {'content': 'Pty',\n", "      'polygon': [{'x': 1.3604, 'y': 10.9534},\n", "       {'x': 1.5381, 'y': 10.9483},\n", "       {'x': 1.5431, 'y': 11.0955},\n", "       {'x': 1.3655, 'y': 11.0904}],\n", "      'span': {'offset': 705, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': 'Ltd',\n", "      'polygon': [{'x': 1.5634, 'y': 10.9483},\n", "       {'x': 1.7462, 'y': 10.9483},\n", "       {'x': 1.7462, 'y': 11.0955},\n", "       {'x': 1.5685, 'y': 11.0955}],\n", "      'span': {'offset': 709, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': 'Tweed',\n", "      'polygon': [{'x': 2.6954, 'y': 10.9483},\n", "       {'x': 3.0507, 'y': 10.9534},\n", "       {'x': 3.0558, 'y': 11.0854},\n", "       {'x': 2.7005, 'y': 11.0854}],\n", "      'span': {'offset': 713, 'length': 5},\n", "      'confidence': 0.995},\n", "     {'content': 'Heads',\n", "      'polygon': [{'x': 3.0761, 'y': 10.9534},\n", "       {'x': 3.4517, 'y': 10.9534},\n", "       {'x': 3.4568, 'y': 11.0854},\n", "       {'x': 3.0812, 'y': 11.0803}],\n", "      'span': {'offset': 719, 'length': 5},\n", "      'confidence': 0.995},\n", "     {'content': 'South',\n", "      'polygon': [{'x': 3.4873, 'y': 10.9534},\n", "       {'x': 3.802, 'y': 10.9483},\n", "       {'x': 3.802, 'y': 11.0854},\n", "       {'x': 3.4924, 'y': 11.0854}],\n", "      'span': {'offset': 725, 'length': 5},\n", "      'confidence': 0.995},\n", "     {'content': 'E',\n", "      'polygon': [{'x': 5.0253, 'y': 10.9483},\n", "       {'x': 5.0913, 'y': 10.9483},\n", "       {'x': 5.0863, 'y': 11.0701},\n", "       {'x': 5.0253, 'y': 11.0651}],\n", "      'span': {'offset': 731, 'length': 1},\n", "      'confidence': 0.995},\n", "     {'content': '<EMAIL>',\n", "      'polygon': [{'x': 5.4822, 'y': 10.9534},\n", "       {'x': 6.8527, 'y': 10.9585},\n", "       {'x': 6.8527, 'y': 11.0904},\n", "       {'x': 5.4822, 'y': 11.0854}],\n", "      'span': {'offset': 733, 'length': 22},\n", "      'confidence': 0.96},\n", "     {'content': 'ABN:',\n", "      'polygon': [{'x': 0.4315, 'y': 11.1057},\n", "       {'x': 0.731, 'y': 11.1057},\n", "       {'x': 0.736, 'y': 11.2326},\n", "       {'x': 0.4315, 'y': 11.2326}],\n", "      'span': {'offset': 756, 'length': 4},\n", "      'confidence': 0.992},\n", "     {'content': '13',\n", "      'polygon': [{'x': 0.7563, 'y': 11.1057},\n", "       {'x': 0.8832, 'y': 11.1057},\n", "       {'x': 0.8832, 'y': 11.2326},\n", "       {'x': 0.7614, 'y': 11.2326}],\n", "      'span': {'offset': 761, 'length': 2},\n", "      'confidence': 0.992},\n", "     {'content': '614',\n", "      'polygon': [{'x': 0.9086, 'y': 11.1057},\n", "       {'x': 1.1167, 'y': 11.1057},\n", "       {'x': 1.1218, 'y': 11.2326},\n", "       {'x': 0.9086, 'y': 11.2326}],\n", "      'span': {'offset': 764, 'length': 3},\n", "      'confidence': 0.993},\n", "     {'content': '035',\n", "      'polygon': [{'x': 1.1523, 'y': 11.1057},\n", "       {'x': 1.3502, 'y': 11.1057},\n", "       {'x': 1.3553, 'y': 11.2326},\n", "       {'x': 1.1523, 'y': 11.2326}],\n", "      'span': {'offset': 768, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': '282',\n", "      'polygon': [{'x': 1.3909, 'y': 11.1057},\n", "       {'x': 1.5939, 'y': 11.1057},\n", "       {'x': 1.599, 'y': 11.2326},\n", "       {'x': 1.3959, 'y': 11.2326}],\n", "      'span': {'offset': 772, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': 'NSW',\n", "      'polygon': [{'x': 2.6802, 'y': 11.1057},\n", "       {'x': 2.9188, 'y': 11.1057},\n", "       {'x': 2.9238, 'y': 11.2326},\n", "       {'x': 2.6853, 'y': 11.2275}],\n", "      'span': {'offset': 776, 'length': 3},\n", "      'confidence': 0.995},\n", "     {'content': '2486',\n", "      'polygon': [{'x': 3.0101, 'y': 11.1057},\n", "       {'x': 3.2741, 'y': 11.1107},\n", "       {'x': 3.2741, 'y': 11.2326},\n", "       {'x': 3.0101, 'y': 11.2326}],\n", "      'span': {'offset': 780, 'length': 4},\n", "      'confidence': 0.993},\n", "     {'content': 'W',\n", "      'polygon': [{'x': 5.0304, 'y': 11.1057},\n", "       {'x': 5.0863, 'y': 11.1057},\n", "       {'x': 5.0863, 'y': 11.2173},\n", "       {'x': 5.0304, 'y': 11.2173}],\n", "      'span': {'offset': 785, 'length': 1},\n", "      'confidence': 0.946},\n", "     {'content': 'www.petchemist.com.au',\n", "      'polygon': [{'x': 5.4771, 'y': 11.1158},\n", "       {'x': 6.8324, 'y': 11.1057},\n", "       {'x': 6.8324, 'y': 11.2478},\n", "       {'x': 5.4771, 'y': 11.2427}],\n", "      'span': {'offset': 787, 'length': 21},\n", "      'confidence': 0.971}],\n", "    'selection_marks': [],\n", "    'spans': [{'offset': 0, 'length': 808}],\n", "    'barcodes': [],\n", "    'formulas': []}],\n", "  'paragraphs': [],\n", "  'tables': [{'row_count': 2,\n", "    'column_count': 6,\n", "    'cells': [{'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 0,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Item',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 0.1598, 'y': 3.1322},\n", "         {'x': 2.8536, 'y': 3.1322},\n", "         {'x': 2.8536, 'y': 3.3742},\n", "         {'x': 0.1598, 'y': 3.3742}]}],\n", "      'spans': [{'offset': 268, 'length': 4}]},\n", "     {'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 1,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Options',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 2.8536, 'y': 3.1322},\n", "         {'x': 4.136, 'y': 3.1322},\n", "         {'x': 4.136, 'y': 3.3742},\n", "         {'x': 2.8536, 'y': 3.3742}]}],\n", "      'spans': [{'offset': 273, 'length': 7}]},\n", "     {'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 2,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Qty',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 4.136, 'y': 3.1322},\n", "         {'x': 5.249, 'y': 3.1322},\n", "         {'x': 5.249, 'y': 3.3742},\n", "         {'x': 4.136, 'y': 3.3742}]}],\n", "      'spans': [{'offset': 281, 'length': 3}]},\n", "     {'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 3,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Unit Price',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 5.249, 'y': 3.1322},\n", "         {'x': 6.2814, 'y': 3.1322},\n", "         {'x': 6.2814, 'y': 3.3742},\n", "         {'x': 5.249, 'y': 3.3742}]}],\n", "      'spans': [{'offset': 285, 'length': 10}]},\n", "     {'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 4,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Discount',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 6.2814, 'y': 3.1322},\n", "         {'x': 7.2895, 'y': 3.1322},\n", "         {'x': 7.2895, 'y': 3.3742},\n", "         {'x': 6.2814, 'y': 3.3742}]}],\n", "      'spans': [{'offset': 296, 'length': 8}]},\n", "     {'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 5,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Subtotal',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 7.2895, 'y': 3.1322},\n", "         {'x': 8.088, 'y': 3.1322},\n", "         {'x': 8.088, 'y': 3.3742},\n", "         {'x': 7.2895, 'y': 3.3742}]}],\n", "      'spans': [{'offset': 305, 'length': 8}]},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 0,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Uricosal Potassium Citrate Mixture (200ml)',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 0.1598, 'y': 3.3742},\n", "         {'x': 2.8536, 'y': 3.3742},\n", "         {'x': 2.8536, 'y': 3.5759},\n", "         {'x': 0.1598, 'y': 3.5759}]}],\n", "      'spans': [{'offset': 314, 'length': 42}]},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 1,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': '',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 2.8536, 'y': 3.3742},\n", "         {'x': 4.136, 'y': 3.3742},\n", "         {'x': 4.1441, 'y': 3.584},\n", "         {'x': 2.8536, 'y': 3.5759}]}],\n", "      'spans': []},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 2,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': '1',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 4.136, 'y': 3.3742},\n", "         {'x': 5.249, 'y': 3.3742},\n", "         {'x': 5.249, 'y': 3.584},\n", "         {'x': 4.1441, 'y': 3.584}]}],\n", "      'spans': [{'offset': 357, 'length': 1}]},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 3,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': '$39.68',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 5.249, 'y': 3.3742},\n", "         {'x': 6.2814, 'y': 3.3742},\n", "         {'x': 6.2814, 'y': 3.584},\n", "         {'x': 5.249, 'y': 3.584}]}],\n", "      'spans': [{'offset': 359, 'length': 6}]},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 4,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': '',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 6.2814, 'y': 3.3742},\n", "         {'x': 7.2895, 'y': 3.3742},\n", "         {'x': 7.2895, 'y': 3.584},\n", "         {'x': 6.2814, 'y': 3.584}]}],\n", "      'spans': []},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 5,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': '$39.68',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 7.2895, 'y': 3.3742},\n", "         {'x': 8.088, 'y': 3.3742},\n", "         {'x': 8.088, 'y': 3.584},\n", "         {'x': 7.2895, 'y': 3.584}]}],\n", "      'spans': [{'offset': 366, 'length': 6}]}],\n", "    'bounding_regions': [{'page_number': 1,\n", "      'polygon': [{'x': 0.1918, 'y': 3.1264},\n", "       {'x': 8.0574, 'y': 3.1259},\n", "       {'x': 8.0586, 'y': 3.565},\n", "       {'x': 0.1915, 'y': 3.5655}]}],\n", "    'spans': [{'offset': 268, 'length': 104}]},\n", "   {'row_count': 2,\n", "    'column_count': 4,\n", "    'cells': [{'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 0,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Payments',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 0.401, 'y': 4.3854},\n", "         {'x': 1.5178, 'y': 4.3854},\n", "         {'x': 1.5178, 'y': 4.624},\n", "         {'x': 0.3959, 'y': 4.6291}]}],\n", "      'spans': [{'offset': 419, 'length': 8}]},\n", "     {'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 1,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Method',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 1.5178, 'y': 4.3854},\n", "         {'x': 2.7005, 'y': 4.3854},\n", "         {'x': 2.6954, 'y': 4.624},\n", "         {'x': 1.5178, 'y': 4.624}]}],\n", "      'spans': [{'offset': 428, 'length': 6}]},\n", "     {'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 2,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Ref',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 2.7005, 'y': 4.3854},\n", "         {'x': 3.3451, 'y': 4.3854},\n", "         {'x': 3.3451, 'y': 4.624},\n", "         {'x': 2.6954, 'y': 4.624}]}],\n", "      'spans': [{'offset': 435, 'length': 3}]},\n", "     {'kind': 'columnHeader',\n", "      'row_index': 0,\n", "      'column_index': 3,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Amount',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 3.3451, 'y': 4.3854},\n", "         {'x': 4.0761, 'y': 4.3854},\n", "         {'x': 4.0761, 'y': 4.624},\n", "         {'x': 3.3451, 'y': 4.624}]}],\n", "      'spans': [{'offset': 439, 'length': 6}]},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 0,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': '03 Oct 2024',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 0.3959, 'y': 4.6291},\n", "         {'x': 1.5178, 'y': 4.624},\n", "         {'x': 1.5178, 'y': 4.8473},\n", "         {'x': 0.3959, 'y': 4.8524}]}],\n", "      'spans': [{'offset': 477, 'length': 11}]},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 1,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': 'Credit Card',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 1.5178, 'y': 4.624},\n", "         {'x': 2.6954, 'y': 4.624},\n", "         {'x': 2.6954, 'y': 4.8473},\n", "         {'x': 1.5178, 'y': 4.8473}]}],\n", "      'spans': [{'offset': 489, 'length': 11}]},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 2,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': '',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 2.6954, 'y': 4.624},\n", "         {'x': 3.3451, 'y': 4.624},\n", "         {'x': 3.3451, 'y': 4.8473},\n", "         {'x': 2.6954, 'y': 4.8473}]}],\n", "      'spans': []},\n", "     {'kind': 'content',\n", "      'row_index': 1,\n", "      'column_index': 3,\n", "      'row_span': 1,\n", "      'column_span': 1,\n", "      'content': '$52.39',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 3.3451, 'y': 4.624},\n", "         {'x': 4.0761, 'y': 4.624},\n", "         {'x': 4.0761, 'y': 4.8473},\n", "         {'x': 3.3451, 'y': 4.8473}]}],\n", "      'spans': [{'offset': 501, 'length': 6}]}],\n", "    'bounding_regions': [{'page_number': 1,\n", "      'polygon': [{'x': 0.3051, 'y': 4.3737},\n", "       {'x': 4.0621, 'y': 4.374},\n", "       {'x': 4.063, 'y': 4.8517},\n", "       {'x': 0.3053, 'y': 4.8515}]}],\n", "    'spans': [{'offset': 419, 'length': 26}, {'offset': 477, 'length': 30}]}],\n", "  'key_value_pairs': [],\n", "  'styles': [],\n", "  'documents': [{'doc_type': 'invoice',\n", "    'bounding_regions': [{'page_number': 1,\n", "      'polygon': [{'x': 0.0, 'y': 0.0},\n", "       {'x': 8.2639, 'y': 0.0},\n", "       {'x': 8.2639, 'y': 11.6944},\n", "       {'x': 0.0, 'y': 11.6944}]}],\n", "    'spans': [{'offset': 0, 'length': 808}],\n", "    'fields': {'AmountDue': {'value_type': 'currency',\n", "      'value': {'amount': 0.0, 'symbol': '$', 'code': 'AUD'},\n", "      'content': '$0.00',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 7.5431, 'y': 6.3142},\n", "         {'x': 7.8578, 'y': 6.3142},\n", "         {'x': 7.8578, 'y': 6.4462},\n", "         {'x': 7.5431, 'y': 6.4462}]}],\n", "      'spans': [{'offset': 623, 'length': 5}],\n", "      'confidence': 0.416},\n", "     'CustomerAddress': {'value_type': 'address',\n", "      'value': {'house_number': None,\n", "       'po_box': 'PO Box 6026',\n", "       'road': None,\n", "       'city': None,\n", "       'state': 'Queensland',\n", "       'postal_code': '4179',\n", "       'country_region': 'Australia',\n", "       'street_address': 'PO Box 6026',\n", "       'unit': None,\n", "       'city_district': None,\n", "       'state_district': None,\n", "       'suburb': 'Manly',\n", "       'house': None,\n", "       'level': None},\n", "      'content': 'PO Box 6026\\nManly, Queensland 4179\\nAustralia',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 0.335, 'y': 1.9592},\n", "         {'x': 1.7005, 'y': 1.9592},\n", "         {'x': 1.7005, 'y': 2.3907},\n", "         {'x': 0.335, 'y': 2.3907}]}],\n", "      'spans': [{'offset': 139, 'length': 11},\n", "       {'offset': 163, 'length': 22},\n", "       {'offset': 209, 'length': 9}],\n", "      'confidence': 0.887},\n", "     'CustomerName': {'value_type': 'string',\n", "      'value': '<PERSON>',\n", "      'content': '<PERSON>',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 0.335, 'y': 1.812},\n", "         {'x': 1.0964, 'y': 1.812},\n", "         {'x': 1.0964, 'y': 1.944},\n", "         {'x': 0.335, 'y': 1.944}]}],\n", "      'spans': [{'offset': 113, 'length': 12}],\n", "      'confidence': 0.811},\n", "     'InvoiceDate': {'value_type': 'date',\n", "      'value': datetime.date(2024, 10, 3),\n", "      'content': '03 Oct 2024',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 4.2132, 'y': 1.1979},\n", "         {'x': 4.8781, 'y': 1.1979},\n", "         {'x': 4.8781, 'y': 1.3248},\n", "         {'x': 4.2132, 'y': 1.3248}]}],\n", "      'spans': [{'offset': 82, 'length': 11}],\n", "      'confidence': 0.923},\n", "     'InvoiceId': {'value_type': 'string',\n", "      'value': '838901',\n", "      'content': '838901',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 2.2589, 'y': 1.208},\n", "         {'x': 2.67, 'y': 1.2029},\n", "         {'x': 2.67, 'y': 1.3197},\n", "         {'x': 2.2589, 'y': 1.3197}]}],\n", "      'spans': [{'offset': 75, 'length': 6}],\n", "      'confidence': 0.956},\n", "     'InvoiceTotal': {'value_type': 'currency',\n", "      'value': {'amount': 52.39, 'symbol': '$', 'code': 'AUD'},\n", "      'content': '$52.39',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 7.472, 'y': 5.8218},\n", "         {'x': 7.8578, 'y': 5.832},\n", "         {'x': 7.8578, 'y': 5.9487},\n", "         {'x': 7.4771, 'y': 5.9487}]}],\n", "      'spans': [{'offset': 584, 'length': 6}],\n", "      'confidence': 0.437},\n", "     'Items': {'value_type': 'list',\n", "      'value': [{'value_type': 'dictionary',\n", "        'value': {'Amount': {'value_type': 'currency',\n", "          'value': {'amount': 39.68, 'symbol': '$', 'code': 'AUD'},\n", "          'content': '$39.68',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 7.5735, 'y': 3.421},\n", "             {'x': 7.9593, 'y': 3.416},\n", "             {'x': 7.9593, 'y': 3.5428},\n", "             {'x': 7.5786, 'y': 3.5378}]}],\n", "          'spans': [{'offset': 366, 'length': 6}],\n", "          'confidence': 0.916},\n", "         'Date': {'value_type': 'date',\n", "          'value': None,\n", "          'content': '03',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.6142, 'y': 4.6747},\n", "             {'x': 0.7614, 'y': 4.6747},\n", "             {'x': 0.7614, 'y': 4.7965},\n", "             {'x': 0.6193, 'y': 4.7965}]}],\n", "          'spans': [{'offset': 477, 'length': 2}],\n", "          'confidence': 0.247},\n", "         'Description': {'value_type': 'string',\n", "          'value': 'Uricosal Potassium Citrate Mixture (200ml)',\n", "          'content': 'Uricosal Potassium Citrate Mixture (200ml)',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.2893, 'y': 3.4109},\n", "             {'x': 2.6446, 'y': 3.4109},\n", "             {'x': 2.6446, 'y': 3.5632},\n", "             {'x': 0.2893, 'y': 3.5632}]}],\n", "          'spans': [{'offset': 314, 'length': 42}],\n", "          'confidence': 0.916},\n", "         'Quantity': {'value_type': 'float',\n", "          'value': 1.0,\n", "          'content': '1',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 4.8629, 'y': 3.416},\n", "             {'x': 4.9187, 'y': 3.416},\n", "             {'x': 4.9238, 'y': 3.5276},\n", "             {'x': 4.868, 'y': 3.5327}]}],\n", "          'spans': [{'offset': 357, 'length': 1}],\n", "          'confidence': 0.917},\n", "         'UnitPrice': {'value_type': 'currency',\n", "          'value': {'amount': 39.68, 'symbol': '$', 'code': 'AUD'},\n", "          'content': '$39.68',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 5.5431, 'y': 3.416},\n", "             {'x': 5.9441, 'y': 3.416},\n", "             {'x': 5.9441, 'y': 3.5428},\n", "             {'x': 5.5482, 'y': 3.5428}]}],\n", "          'spans': [{'offset': 359, 'length': 6}],\n", "          'confidence': 0.917}},\n", "        'content': 'Uricosal Potassium Citrate Mixture (200ml)\\n1\\n$39.68\\n$39.68\\nPayments\\nProduct Cost:\\n$39.68\\nAusPost eParcel\\nPayments\\nMethod\\nRef\\nAmount\\n03 Oct 2024\\nCredit Card\\n$52.39',\n", "        'bounding_regions': [{'page_number': 1,\n", "          'polygon': [{'x': 0.2893, 'y': 3.4109},\n", "           {'x': 7.9593, 'y': 3.4109},\n", "           {'x': 7.9593, 'y': 4.8016},\n", "           {'x': 0.2893, 'y': 4.8016}]}],\n", "        'spans': [{'offset': 314, 'length': 131},\n", "         {'offset': 477, 'length': 30}],\n", "        'confidence': 0.615}],\n", "      'content': None,\n", "      'bounding_regions': [],\n", "      'spans': [],\n", "      'confidence': None},\n", "     'ShippingAddress': {'value_type': 'address',\n", "      'value': {'house_number': None,\n", "       'po_box': 'PO Box 6026',\n", "       'road': None,\n", "       'city': None,\n", "       'state': 'Queensland',\n", "       'postal_code': '4179',\n", "       'country_region': 'Australia',\n", "       'street_address': 'PO Box 6026',\n", "       'unit': None,\n", "       'city_district': None,\n", "       'state_district': None,\n", "       'suburb': 'Manly',\n", "       'house': None,\n", "       'level': None},\n", "      'content': 'PO Box 6026\\nManly, Queensland 4179\\nAustralia',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 4.2284, 'y': 1.9592},\n", "         {'x': 5.5939, 'y': 1.9592},\n", "         {'x': 5.5939, 'y': 2.3856},\n", "         {'x': 4.2284, 'y': 2.3856}]}],\n", "      'spans': [{'offset': 151, 'length': 11},\n", "       {'offset': 186, 'length': 22},\n", "       {'offset': 219, 'length': 9}],\n", "      'confidence': 0.887},\n", "     'ShippingAddressRecipient': {'value_type': 'string',\n", "      'value': '<PERSON>',\n", "      'content': '<PERSON>',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 4.2336, 'y': 1.8041},\n", "         {'x': 4.9807, 'y': 1.8095},\n", "         {'x': 4.9797, 'y': 1.9467},\n", "         {'x': 4.2326, 'y': 1.9413}]}],\n", "      'spans': [{'offset': 126, 'length': 12}],\n", "      'confidence': 0.918},\n", "     'SubTotal': {'value_type': 'currency',\n", "      'value': {'amount': 47.63, 'symbol': '$', 'code': 'AUD'},\n", "      'content': '$47.63',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 7.4771, 'y': 5.0808},\n", "         {'x': 7.8578, 'y': 5.0757},\n", "         {'x': 7.8578, 'y': 5.2077},\n", "         {'x': 7.4771, 'y': 5.2077}]}],\n", "      'spans': [{'offset': 541, 'length': 6}],\n", "      'confidence': 0.95},\n", "     'TotalDiscount': {'value_type': 'currency',\n", "      'value': {'amount': 0.0, 'symbol': '$', 'code': 'AUD'},\n", "      'content': '$0.00',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 7.5532, 'y': 4.7813},\n", "         {'x': 7.8578, 'y': 4.7813},\n", "         {'x': 7.8578, 'y': 4.964},\n", "         {'x': 7.5532, 'y': 4.964}]}],\n", "      'spans': [{'offset': 524, 'length': 5}],\n", "      'confidence': 0.647},\n", "     'TotalTax': {'value_type': 'currency',\n", "      'value': {'amount': 4.76, 'symbol': '$', 'code': 'AUD'},\n", "      'content': '$4.76',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 7.5431, 'y': 5.3193},\n", "         {'x': 7.8629, 'y': 5.3193},\n", "         {'x': 7.8629, 'y': 5.4462},\n", "         {'x': 7.5431, 'y': 5.4462}]}],\n", "      'spans': [{'offset': 559, 'length': 5}],\n", "      'confidence': 0.95},\n", "     'VendorAddress': {'value_type': 'address',\n", "      'value': {'house_number': '29-31',\n", "       'po_box': None,\n", "       'road': 'Corporation Circuit',\n", "       'city': None,\n", "       'state': 'NSW',\n", "       'postal_code': '2486',\n", "       'country_region': None,\n", "       'street_address': '29-31 Corporation Circuit',\n", "       'unit': None,\n", "       'city_district': None,\n", "       'state_district': None,\n", "       'suburb': 'Tweed Heads South',\n", "       'house': None,\n", "       'level': None},\n", "      'content': '29-31 Corporation Circuit\\nTweed Heads South\\nNSW 2486',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 2.6824, 'y': 10.7865},\n", "         {'x': 4.0822, 'y': 10.796},\n", "         {'x': 4.0792, 'y': 11.2404},\n", "         {'x': 2.6793, 'y': 11.2309}]}],\n", "      'spans': [{'offset': 648, 'length': 25},\n", "       {'offset': 713, 'length': 17},\n", "       {'offset': 776, 'length': 8}],\n", "      'confidence': 0.886},\n", "     'VendorAddressRecipient': {'value_type': 'string',\n", "      'value': 'Pet Chemist Online\\nAussie Pet Meds Pty Ltd',\n", "      'content': 'Pet Chemist Online\\nAussie Pet Meds Pty Ltd',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 0.4264, 'y': 10.8011},\n", "         {'x': 1.7462, 'y': 10.8011},\n", "         {'x': 1.7462, 'y': 11.0955},\n", "         {'x': 0.4264, 'y': 11.0955}]}],\n", "      'spans': [{'offset': 629, 'length': 18}, {'offset': 689, 'length': 23}],\n", "      'confidence': 0.717},\n", "     'VendorName': {'value_type': 'string',\n", "      'value': 'PET CHEMIST',\n", "      'content': 'PET CHEMIST',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 0.9386, 'y': 0.4021},\n", "         {'x': 2.8426, 'y': 0.3983},\n", "         {'x': 2.8431, 'y': 0.6725},\n", "         {'x': 0.9391, 'y': 0.6763}]}],\n", "      'spans': [{'offset': 0, 'length': 11}],\n", "      'confidence': 0.938},\n", "     'VendorTaxId': {'value_type': 'string',\n", "      'value': '**************',\n", "      'content': '**************',\n", "      'bounding_regions': [{'page_number': 1,\n", "        'polygon': [{'x': 0.7563, 'y': 11.1057},\n", "         {'x': 1.599, 'y': 11.1057},\n", "         {'x': 1.599, 'y': 11.2326},\n", "         {'x': 0.7563, 'y': 11.2326}]}],\n", "      'spans': [{'offset': 761, 'length': 14}],\n", "      'confidence': 0.68}},\n", "    'confidence': 1.0}]}}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["document_intelligence_res[0]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# CHANGELOG: 04 NOV 2024 add function to verify whether the content contains numbers and puncs only\n", "import string\n", "def is_numbers_and_punctuation(s):\n", "    allowed_chars = set(string.digits + string.punctuation + \" \")\n", "    return all(char in allowed_chars for char in s)\n", "\n", "# parse Azure Document Intelligence results from ans\n", "def parse_document_intelligence_res(data_info: List[Dict] | Dict) -> Dict:\n", "    if isinstance(data_info, Dict):\n", "        data_info = [data_info]\n", "\n", "    assert isinstance(data_info, List) and all(isinstance(data, Dict) for data in data_info)\n", "\n", "    ans = {}\n", "    for data in tqdm(data_info):\n", "        file_path = data[\"file_path\"]\n", "        invoice = data[\"invoice\"]\n", "        content = invoice[\"content\"]\n", "        invoice_info = []\n", "        for document in invoice[\"documents\"]:\n", "            service_provider = document[\"fields\"].get(\"VendorName\", {}).get(\"value\", \"\") + \" \" + document[\"fields\"].get(\"VendorAddressRecipient\", {}).get(\"value\", \"\")\n", "            service_provider = service_provider.strip()\n", "            service_provider_conf = 0.\n", "            service_provider_count = int(document[\"fields\"].get(\"VendorName\", {}).get(\"value\", \"\") != \"\") + int(document[\"fields\"].get(\"VendorAddressRecipient\", {}).get(\"value\", \"\") != \"\")\n", "            service_provider_conf = (document[\"fields\"].get(\"VendorName\", {}).get(\"confidence\", 0.) or 0.) + (document[\"fields\"].get(\"VendorAddressRecipient\", {}).get(\"confidence\", 0.) or 0.)\n", "            if service_provider_conf > 0.:\n", "                service_provider_conf /= service_provider_count\n", "            # CHANGELOG: 04 NOV 2024 add service provider address extraction for service provider field fuzzy matching preparation\n", "            service_provider_address = \"\"\n", "            service_provider_streetname_name = \"\"\n", "            service_provider_suburb_name = \"\"\n", "\n", "            service_provider_address_value = document[\"fields\"].get(\"VendorAddress\", {}).get(\"value\", {})\n", "            service_provider_address_exist = service_provider_address_value.get(\"street_address\", \"\") and (service_provider_address_value.get(\"postal_code\", \"\") or service_provider_address_value.get(\"suburb\", \"\") or service_provider_address_value.get(\"city\", \"\")) \n", "            service_provider_address_content = document[\"fields\"].get(\"VendorAddress\", {}).get(\"content\", \"\").replace(\"\\t\", \" \").replace(\"\\n\", \" \")\n", "            if service_provider_address_exist:\n", "                service_provider_address = service_provider_address_content\n", "                service_provider_streetname_name = service_provider_address_value.get(\"street_address\", \"\")\n", "                service_provider_suburb_name = service_provider_address_value.get(\"suburb\", \"\") or service_provider_address_value.get(\"city\", \"\") or \"\"\n", "\n", "\n", "            invoice_no = document[\"fields\"].get(\"InvoiceId\", {}).get(\"value\", \"\")\n", "            invoice_date = document[\"fields\"].get(\"InvoiceDate\", {}).get(\"value\", \"\") or \"\"\n", "            if not isinstance(invoice_date, str):\n", "                invoice_date = invoice_date.isoformat()\n", "            invoice_total_dict = document[\"fields\"].get(\"InvoiceTotal\", {}).get(\"value\", {}) or {}\n", "            invoice_total = invoice_total_dict.get(\"amount\", -1)\n", "\n", "            invoice_no_conf = document[\"fields\"].get(\"InvoiceId\", {}).get(\"confidence\", 0.) or 0.\n", "            invoice_date_conf = document[\"fields\"].get(\"InvoiceDate\", {}).get(\"confidence\", 0.) or 0.\n", "            invoice_total_conf = document[\"fields\"].get(\"InvoiceTotal\", {}).get(\"confidence\", 0.) or 0.\n", "\n", "\n", "            treatments = []\n", "            cur_treatment_date = invoice_date\n", "            cur_treatment_date_conf = invoice_date_conf\n", "\n", "            for item in document[\"fields\"].get(\"Items\", {}).get(\"value\", []):\n", "                item_conf = item.get(\"confidence\", 0.) or 0.\n", "\n", "                treatment_date = item.get(\"value\", {}).get(\"Date\", {}).get(\"value\", cur_treatment_date) or \"\"\n", "                if not isinstance(treatment_date, str):\n", "                    treatment_date = treatment_date.isoformat()\n", "                treatment_date_conf = item.get(\"value\", {}).get(\"Date\", {}).get(\"confidence\", cur_treatment_date_conf)\n", "                if treatment_date_conf is None:\n", "                    treatment_date_conf = item_conf\n", "\n", "                if not treatment_date:\n", "                    treatment_date = cur_treatment_date\n", "                    treatment_date_conf = cur_treatment_date_conf\n", "                cur_treatment_date = treatment_date\n", "                cur_treatment_date_conf = treatment_date_conf\n", "\n", "                desc = item.get(\"value\", {}).get(\"Description\", {}).get(\"content\", \"\")\n", "                product = item.get(\"value\", {}).get(\"ProductCode\", {}).get(\"content\", \"\")\n", "                # CHANGELOG: 04 NOV 2024 ignore product if it only contains numbers and puncs.\n", "                if is_numbers_and_punctuation(product.strip()):\n", "                     product = \"\"\n", "                desc_conf = item.get(\"value\", {}).get(\"Description\", {}).get(\"confidence\", item_conf) or item_conf\n", "                product_conf = item.get(\"value\", {}).get(\"ProductCode\", {}).get(\"confidence\", item_conf) or item_conf\n", "                desc_conf = (desc_conf * int(desc!=\"\") + product_conf * int(product!=\"\"))/(int(desc!=\"\") + int(product!=\"\")+1e-7)\n", "                desc = product + \" \" + desc\n", "                desc = desc.strip()\n", "\n", "                # CHANGELOG: 01 NOV 2024 default amount change from -1 to 0. This treatment line would be removed later during post process as amount  == 0\n", "                amount_dict = item.get(\"value\", {}).get(\"Amount\", {}).get(\"value\", {}) or {}\n", "                amount = amount_dict.get(\"amount\", 0)\n", "                amount_conf = item.get(\"value\", {}).get(\"Amount\", {}).get(\"confidence\", 0.)\n", "                if amount_conf is None:\n", "                    amount_conf = item_conf\n", "\n", "\n", "                treatments.append({\"treatment_date\": treatment_date, \n", "                                   \"treatment\": desc, \n", "                                   \"amount\": amount, \n", "                                   \"treatment_date_conf\": treatment_date_conf,\n", "                                   \"treatment_conf\": desc_conf, \n", "                                   \"amount_conf\": amount_conf, \n", "                                   \"treatmentline_conf\": item_conf})\n", "\n", "            if not invoice_date:\n", "                invoice_date = cur_treatment_date\n", "                invoice_date_conf = cur_treatment_date_conf\n", "\n", "            if not isinstance(invoice_date, str):\n", "                invoice_date = invoice_date.isoformat()\n", "\n", "            invoice_info.append(\n", "                {\n", "                    \"service_provider\": service_provider.strip(),\n", "                    \"service_provider_address\": service_provider_address.strip(),\n", "                    \"service_provider_streetname_name\": service_provider_streetname_name.strip(),\n", "                    \"service_provider_suburb_name\": service_provider_suburb_name.strip(),\n", "                    \"content\": content.strip(),\n", "                    \"invoice_no\": invoice_no,\n", "                    \"invoice_date\": invoice_date,\n", "                    \"invoice_total\": invoice_total,\n", "                    \"service_provider_conf\": service_provider_conf,\n", "                    \"invoice_no_conf\": invoice_no_conf,\n", "                    \"invoice_date_conf\": invoice_date_conf,\n", "                    \"invoice_total_conf\": invoice_total_conf,\n", "                    \"treatments\": treatments,\n", "                    \"raw\": invoice\n", "                }\n", "            )\n", "            \n", "        ans[file_path] = invoice_info\n", "\n", "\n", "    return ans"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1039/1039 [00:00<00:00, 13587.09it/s]\n"]}], "source": ["document_intelligence_parsed_res = parse_document_intelligence_res(document_intelligence_res)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# group split di results\n", "def gather_document_intelligence_res(info: Dict) -> Dict:\n", "    ans = {}\n", "    for k, v in info.items():\n", "        if k.lower().endswith(\"pdf\"):\n", "            if len(v) == 0 or len(v[0][\"treatments\"]) ==0:\n", "                continue\n", "            stem = Path(k).stem\n", "            stem = \"-\".join(stem.split(\"-\")[:-1])\n", "            # if str(stem[-1]).isdigit() and str(stem[-2] == \"-\"):\n", "            # stem = str(stem)[:-2]\n", "            if f\"{stem}.{k[-3:]}\" not in ans:\n", "                ans[f\"{stem}.{k[-3:]}\"] = [v[0]]\n", "            else:\n", "                ans[f\"{stem}.{k[-3:]}\"].append(v[0])\n", "        else:\n", "            ans[k] = v\n", "    return ans"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["1223"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["document_intelligence_parsed_res = gather_document_intelligence_res(document_intelligence_parsed_res)\n", "len(document_intelligence_parsed_res)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/plain": ["(9694, 21)"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["# load MP label\n", "# ANNOTATION_PATH = f\"/workspaces/OCR_in_house/data/OCR_in_house/res/1000_2024_sample_evaluation/1000_samples_DI_rule_res.xlsx\"\n", "# ANNOTATION_PATH = (ROOTDIR / \"data/res/1000_2024_sample_evaluation/1000_samples_DI_rule_res.xlsx\")\n", "ANNOTATION_PATH = ('/home/<USER>/repos/OCR_in_house/data/result/nz_csp_samples_DI_rule_res.xlsx')\n", "anno_df = pd.read_excel(ANNOTATION_PATH)\n", "anno_df.shape"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["anno_df['docfile'] = anno_df['file_path']"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["anno_df['claimno']= anno_df['file_path']"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["def get_claim_mp_label(anno_df=anno_df) -> Dict:\n", "    anno_df[\"NOTE\"] = anno_df[\"NOTE\"].fillna(\"\")\n", "    ans = {}\n", "    for info in anno_df.to_dict(orient=\"records\"):\n", "        claimno = info[\"claimno\"]\n", "        docfile = info[\"docfile\"]\n", "        note = info[\"NOTE\"]\n", "        if claimno not in ans:\n", "            ans[claimno] = {}\n", "        if docfile not in ans[claimno]:\n", "            ans[claimno][docfile] = {\"is_mp\": 0, \"notes\": []}\n", "        ans[claimno][docfile][\"notes\"].append(note)\n", "    for claimno, cinfo in ans.items():\n", "        for docfile, finfo in cinfo.items():\n", "            if any(\"MP\" in note for note in finfo[\"notes\"]):\n", "                finfo[\"is_mp\"] = 1\n", "\n", "    return ans"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["mp_labels = get_claim_mp_label(anno_df)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'service_provider': 'PET CHEMIST Pet Chemist Online\\nAussie Pet Meds Pty Ltd',\n", "  'service_provider_address': '29-31 Corporation Circuit Tweed Heads South NSW 2486',\n", "  'service_provider_streetname_name': '29-31 Corporation Circuit',\n", "  'service_provider_suburb_name': 'Tweed Heads South',\n", "  'content': 'PET CHEMIST\\nTax Invoice\\nInvoice Date\\nInvoice No\\nCreated Date\\nTracking Code\\n838901\\n03 Oct 2024\\nCustomer:\\nShip To:\\n<PERSON><PERSON><PERSON>\\<PERSON><PERSON><PERSON><PERSON>\\nPO Box 6026\\nPO Box 6026\\nManly, Queensland 4179\\nManly, Queensland 4179\\nAustralia\\nAustralia\\nPhone: **********\\<EMAIL>\\nItem\\nOptions\\nQty\\nUnit Price\\nDiscount\\nSubtotal\\nUricosal Potassium Citrate Mixture (200ml)\\n1\\n$39.68\\n$39.68\\nPayments\\nProduct Cost:\\n$39.68\\nAusPost eParcel\\nPayments\\nMethod\\nRef\\nAmount\\nDelivery Method:\\n(Parcel Post)\\n03 Oct 2024\\nCredit Card\\n$52.39\\n$7.95\\nDiscount:\\n$0.00\\nSub Total:\\n$47.63\\nGST (10%):\\n$4.76\\nTax Invoice Total:\\n$52.39\\nTotal Paid:\\n$52.39\\nOutstanding:\\n$0.00\\nPet Chemist Online\\n29-31 Corporation Circuit\\nP\\n1300 000 738\\nAussie Pet Meds Pty Ltd\\nTweed Heads South\\nE\\<EMAIL>\\nABN: **************\\nNSW 2486\\nW\\nwww.petchemist.com.au',\n", "  'invoice_no': '838901',\n", "  'invoice_date': '2024-10-03',\n", "  'invoice_total': 52.39,\n", "  'service_provider_conf': 0.8274999999999999,\n", "  'invoice_no_conf': 0.956,\n", "  'invoice_date_conf': 0.923,\n", "  'invoice_total_conf': 0.437,\n", "  'treatments': [{'treatment_date': '2024-10-03',\n", "    'treatment': 'Uricosal Potassium Citrate Mixture (200ml)',\n", "    'amount': 39.68,\n", "    'treatment_date_conf': 0.923,\n", "    'treatment_conf': 0.9159999084000091,\n", "    'amount_conf': 0.916,\n", "    'treatmentline_conf': 0.615}],\n", "  'raw': {'api_version': '2023-07-31',\n", "   'model_id': 'prebuilt-invoice',\n", "   'content': 'PET CHEMIST\\nTax Invoice\\nInvoice Date\\nInvoice No\\nCreated Date\\nTracking Code\\n838901\\n03 Oct 2024\\nCustomer:\\nShip To:\\n<PERSON><PERSON><PERSON>\\<PERSON><PERSON><PERSON><PERSON>\\nPO Box 6026\\nPO Box 6026\\nManly, Queensland 4179\\nManly, Queensland 4179\\nAustralia\\nAustralia\\nPhone: **********\\<EMAIL>\\nItem\\nOptions\\nQty\\nUnit Price\\nDiscount\\nSubtotal\\nUricosal Potassium Citrate Mixture (200ml)\\n1\\n$39.68\\n$39.68\\nPayments\\nProduct Cost:\\n$39.68\\nAusPost eParcel\\nPayments\\nMethod\\nRef\\nAmount\\nDelivery Method:\\n(Parcel Post)\\n03 Oct 2024\\nCredit Card\\n$52.39\\n$7.95\\nDiscount:\\n$0.00\\nSub Total:\\n$47.63\\nGST (10%):\\n$4.76\\nTax Invoice Total:\\n$52.39\\nTotal Paid:\\n$52.39\\nOutstanding:\\n$0.00\\nPet Chemist Online\\n29-31 Corporation Circuit\\nP\\n1300 000 738\\nAussie Pet Meds Pty Ltd\\nTweed Heads South\\nE\\<EMAIL>\\nABN: **************\\nNSW 2486\\nW\\nwww.petchemist.com.au',\n", "   'languages': [],\n", "   'pages': [{'page_number': 1,\n", "     'angle': None,\n", "     'width': 8.2639,\n", "     'height': 11.6944,\n", "     'unit': 'inch',\n", "     'lines': [{'content': 'PET CHEMIST',\n", "       'polygon': [{'x': 0.9391, 'y': 0.401},\n", "        {'x': 2.8528, 'y': 0.3959},\n", "        {'x': 2.8528, 'y': 0.6649},\n", "        {'x': 0.9391, 'y': 0.6751}],\n", "       'spans': [{'offset': 0, 'length': 11}]},\n", "      {'content': 'Tax Invoice',\n", "       'polygon': [{'x': 6.8832, 'y': 0.4264},\n", "        {'x': 7.9644, 'y': 0.4264},\n", "        {'x': 7.9644, 'y': 0.6142},\n", "        {'x': 6.8832, 'y': 0.6142}],\n", "       'spans': [{'offset': 12, 'length': 11}]},\n", "      {'content': 'Invoice Date',\n", "       'polygon': [{'x': 0.2893, 'y': 1.0507},\n", "        {'x': 1.0, 'y': 1.0507},\n", "        {'x': 1.0, 'y': 1.1674},\n", "        {'x': 0.2893, 'y': 1.1725}],\n", "       'spans': [{'offset': 24, 'length': 12}]},\n", "      {'content': 'Invoice No',\n", "       'polygon': [{'x': 2.2386, 'y': 1.0456},\n", "        {'x': 2.8477, 'y': 1.0456},\n", "        {'x': 2.8477, 'y': 1.1725},\n", "        {'x': 2.2386, 'y': 1.1725}],\n", "       'spans': [{'offset': 37, 'length': 10}]},\n", "      {'content': 'Created Date',\n", "       'polygon': [{'x': 4.203, 'y': 1.0456},\n", "        {'x': 4.9746, 'y': 1.0456},\n", "        {'x': 4.9746, 'y': 1.1776},\n", "        {'x': 4.203, 'y': 1.1776}],\n", "       'spans': [{'offset': 48, 'length': 12}]},\n", "      {'content': 'Tracking Code',\n", "       'polygon': [{'x': 6.1573, 'y': 1.0405},\n", "        {'x': 7.0101, 'y': 1.0405},\n", "        {'x': 7.0101, 'y': 1.1877},\n", "        {'x': 6.1573, 'y': 1.1877}],\n", "       'spans': [{'offset': 61, 'length': 13}]},\n", "      {'content': '838901',\n", "       'polygon': [{'x': 2.2487, 'y': 1.2029},\n", "        {'x': 2.67, 'y': 1.1979},\n", "        {'x': 2.67, 'y': 1.3146},\n", "        {'x': 2.2487, 'y': 1.3146}],\n", "       'spans': [{'offset': 75, 'length': 6}]},\n", "      {'content': '03 Oct 2024',\n", "       'polygon': [{'x': 4.203, 'y': 1.1979},\n", "        {'x': 4.8883, 'y': 1.1979},\n", "        {'x': 4.8883, 'y': 1.3197},\n", "        {'x': 4.203, 'y': 1.3197}],\n", "       'spans': [{'offset': 82, 'length': 11}]},\n", "      {'content': 'Customer:',\n", "       'polygon': [{'x': 0.335, 'y': 1.6598},\n", "        {'x': 0.9492, 'y': 1.6598},\n", "        {'x': 0.9492, 'y': 1.7867},\n", "        {'x': 0.335, 'y': 1.7816}],\n", "       'spans': [{'offset': 94, 'length': 9}]},\n", "      {'content': 'Ship To:',\n", "       'polygon': [{'x': 4.2284, 'y': 1.6598},\n", "        {'x': 4.6903, 'y': 1.6598},\n", "        {'x': 4.6903, 'y': 1.7968},\n", "        {'x': 4.2284, 'y': 1.7968}],\n", "       'spans': [{'offset': 104, 'length': 8}]},\n", "      {'content': '<PERSON>',\n", "       'polygon': [{'x': 0.3299, 'y': 1.807},\n", "        {'x': 1.0964, 'y': 1.807},\n", "        {'x': 1.0964, 'y': 1.9389},\n", "        {'x': 0.3299, 'y': 1.9389}],\n", "       'spans': [{'offset': 113, 'length': 12}]},\n", "      {'content': '<PERSON>',\n", "       'polygon': [{'x': 4.2182, 'y': 1.807},\n", "        {'x': 4.9898, 'y': 1.807},\n", "        {'x': 4.9898, 'y': 1.9389},\n", "        {'x': 4.2182, 'y': 1.9389}],\n", "       'spans': [{'offset': 126, 'length': 12}]},\n", "      {'content': 'PO Box 6026',\n", "       'polygon': [{'x': 0.335, 'y': 1.9541},\n", "        {'x': 1.0609, 'y': 1.9592},\n", "        {'x': 1.0609, 'y': 2.081},\n", "        {'x': 0.335, 'y': 2.076}],\n", "       'spans': [{'offset': 139, 'length': 11}]},\n", "      {'content': 'PO Box 6026',\n", "       'polygon': [{'x': 4.2233, 'y': 1.9541},\n", "        {'x': 4.9543, 'y': 1.9541},\n", "        {'x': 4.9543, 'y': 2.081},\n", "        {'x': 4.2233, 'y': 2.081}],\n", "       'spans': [{'offset': 151, 'length': 11}]},\n", "      {'content': 'Manly, Queensland 4179',\n", "       'polygon': [{'x': 0.335, 'y': 2.1013},\n", "        {'x': 1.7005, 'y': 2.0912},\n", "        {'x': 1.7005, 'y': 2.2384},\n", "        {'x': 0.335, 'y': 2.2435}],\n", "       'spans': [{'offset': 163, 'length': 22}]},\n", "      {'content': 'Manly, Queensland 4179',\n", "       'polygon': [{'x': 4.2284, 'y': 2.1013},\n", "        {'x': 5.5939, 'y': 2.0963},\n", "        {'x': 5.5939, 'y': 2.2333},\n", "        {'x': 4.2284, 'y': 2.2384}],\n", "       'spans': [{'offset': 186, 'length': 22}]},\n", "      {'content': 'Australia',\n", "       'polygon': [{'x': 0.3299, 'y': 2.2587},\n", "        {'x': 0.8325, 'y': 2.2587},\n", "        {'x': 0.8325, 'y': 2.3856},\n", "        {'x': 0.3299, 'y': 2.3856}],\n", "       'spans': [{'offset': 209, 'length': 9}]},\n", "      {'content': 'Australia',\n", "       'polygon': [{'x': 4.2182, 'y': 2.2587},\n", "        {'x': 4.7309, 'y': 2.2536},\n", "        {'x': 4.736, 'y': 2.3805},\n", "        {'x': 4.2182, 'y': 2.3856}],\n", "       'spans': [{'offset': 219, 'length': 9}]},\n", "      {'content': 'Phone: **********',\n", "       'polygon': [{'x': 0.335, 'y': 2.5226},\n", "        {'x': 1.467, 'y': 2.5226},\n", "        {'x': 1.467, 'y': 2.6495},\n", "        {'x': 0.335, 'y': 2.6546}],\n", "       'spans': [{'offset': 229, 'length': 17}]},\n", "      {'content': '<EMAIL>',\n", "       'polygon': [{'x': 0.3299, 'y': 2.6851},\n", "        {'x': 1.7513, 'y': 2.68},\n", "        {'x': 1.7513, 'y': 2.8119},\n", "        {'x': 0.3299, 'y': 2.817}],\n", "       'spans': [{'offset': 247, 'length': 20}]},\n", "      {'content': 'Item',\n", "       'polygon': [{'x': 0.2233, 'y': 3.2079},\n", "        {'x': 0.4924, 'y': 3.2079},\n", "        {'x': 0.4924, 'y': 3.3195},\n", "        {'x': 0.2233, 'y': 3.3094}],\n", "       'spans': [{'offset': 268, 'length': 4}]},\n", "      {'content': 'Options',\n", "       'polygon': [{'x': 3.0355, 'y': 3.1977},\n", "        {'x': 3.4974, 'y': 3.1977},\n", "        {'x': 3.4924, 'y': 3.3347},\n", "        {'x': 3.0355, 'y': 3.3297}],\n", "       'spans': [{'offset': 273, 'length': 7}]},\n", "      {'content': 'Qty',\n", "       'polygon': [{'x': 4.8071, 'y': 3.1977},\n", "        {'x': 5.0152, 'y': 3.2028},\n", "        {'x': 5.0152, 'y': 3.3347},\n", "        {'x': 4.8071, 'y': 3.3246}],\n", "       'spans': [{'offset': 281, 'length': 3}]},\n", "      {'content': 'Unit Price',\n", "       'polygon': [{'x': 5.4517, 'y': 3.1926},\n", "        {'x': 6.0203, 'y': 3.1977},\n", "        {'x': 6.0203, 'y': 3.3246},\n", "        {'x': 5.4517, 'y': 3.3195}],\n", "       'spans': [{'offset': 285, 'length': 10}]},\n", "      {'content': 'Discount',\n", "       'polygon': [{'x': 6.5025, 'y': 3.2028},\n", "        {'x': 7.0253, 'y': 3.2079},\n", "        {'x': 7.0202, 'y': 3.3195},\n", "        {'x': 6.5025, 'y': 3.3144}],\n", "       'spans': [{'offset': 296, 'length': 8}]},\n", "      {'content': 'Subtotal',\n", "       'polygon': [{'x': 7.538, 'y': 3.1977},\n", "        {'x': 8.0304, 'y': 3.1977},\n", "        {'x': 8.0304, 'y': 3.3195},\n", "        {'x': 7.538, 'y': 3.3144}],\n", "       'spans': [{'offset': 305, 'length': 8}]},\n", "      {'content': 'Uricosal Potassium Citrate Mixture (200ml)',\n", "       'polygon': [{'x': 0.2792, 'y': 3.4109},\n", "        {'x': 2.6446, 'y': 3.4109},\n", "        {'x': 2.6446, 'y': 3.5581},\n", "        {'x': 0.2792, 'y': 3.553}],\n", "       'spans': [{'offset': 314, 'length': 42}]},\n", "      {'content': '1',\n", "       'polygon': [{'x': 4.8629, 'y': 3.421},\n", "        {'x': 4.9238, 'y': 3.416},\n", "        {'x': 4.9289, 'y': 3.5327},\n", "        {'x': 4.868, 'y': 3.5327}],\n", "       'spans': [{'offset': 357, 'length': 1}]},\n", "      {'content': '$39.68',\n", "       'polygon': [{'x': 5.538, 'y': 3.4109},\n", "        {'x': 5.939, 'y': 3.4109},\n", "        {'x': 5.939, 'y': 3.5378},\n", "        {'x': 5.538, 'y': 3.5378}],\n", "       'spans': [{'offset': 359, 'length': 6}]},\n", "      {'content': '$39.68',\n", "       'polygon': [{'x': 7.5634, 'y': 3.416},\n", "        {'x': 7.9644, 'y': 3.4109},\n", "        {'x': 7.9644, 'y': 3.5378},\n", "        {'x': 7.5634, 'y': 3.5378}],\n", "       'spans': [{'offset': 366, 'length': 6}]},\n", "      {'content': 'Payments',\n", "       'polygon': [{'x': 0.3096, 'y': 4.0961},\n", "        {'x': 0.9898, 'y': 4.1012},\n", "        {'x': 0.9848, 'y': 4.2585},\n", "        {'x': 0.3096, 'y': 4.2534}],\n", "       'spans': [{'offset': 373, 'length': 8}]},\n", "      {'content': 'Product Cost:',\n", "       'polygon': [{'x': 5.9543, 'y': 4.0149},\n", "        {'x': 6.7005, 'y': 4.02},\n", "        {'x': 6.7005, 'y': 4.1367},\n", "        {'x': 5.9543, 'y': 4.1316}],\n", "       'spans': [{'offset': 382, 'length': 13}]},\n", "      {'content': '$39.68',\n", "       'polygon': [{'x': 7.472, 'y': 4.0149},\n", "        {'x': 7.8629, 'y': 4.0098},\n", "        {'x': 7.8629, 'y': 4.1367},\n", "        {'x': 7.472, 'y': 4.1418}],\n", "       'spans': [{'offset': 396, 'length': 6}]},\n", "      {'content': 'AusPost eParcel',\n", "       'polygon': [{'x': 6.9492, 'y': 4.2636},\n", "        {'x': 7.8883, 'y': 4.2585},\n", "        {'x': 7.8883, 'y': 4.3854},\n", "        {'x': 6.9492, 'y': 4.3905}],\n", "       'spans': [{'offset': 403, 'length': 15}]},\n", "      {'content': 'Payments',\n", "       'polygon': [{'x': 0.5533, 'y': 4.4565},\n", "        {'x': 1.1421, 'y': 4.4565},\n", "        {'x': 1.1421, 'y': 4.5935},\n", "        {'x': 0.5533, 'y': 4.5884}],\n", "       'spans': [{'offset': 419, 'length': 8}]},\n", "      {'content': 'Method',\n", "       'polygon': [{'x': 1.7563, 'y': 4.4565},\n", "        {'x': 2.2081, 'y': 4.4565},\n", "        {'x': 2.2081, 'y': 4.5783},\n", "        {'x': 1.7563, 'y': 4.5783}],\n", "       'spans': [{'offset': 428, 'length': 6}]},\n", "      {'content': 'Ref',\n", "       'polygon': [{'x': 2.9137, 'y': 4.4565},\n", "        {'x': 3.132, 'y': 4.4514},\n", "        {'x': 3.132, 'y': 4.5732},\n", "        {'x': 2.9188, 'y': 4.5732}],\n", "       'spans': [{'offset': 435, 'length': 3}]},\n", "      {'content': 'Amount',\n", "       'polygon': [{'x': 3.5634, 'y': 4.4565},\n", "        {'x': 4.0355, 'y': 4.4565},\n", "        {'x': 4.0304, 'y': 4.5884},\n", "        {'x': 3.5634, 'y': 4.5834}],\n", "       'spans': [{'offset': 439, 'length': 6}]},\n", "      {'content': 'Delivery Method:',\n", "       'polygon': [{'x': 5.8121, 'y': 4.4159},\n", "        {'x': 6.7055, 'y': 4.4159},\n", "        {'x': 6.7055, 'y': 4.5478},\n", "        {'x': 5.8121, 'y': 4.5428}],\n", "       'spans': [{'offset': 446, 'length': 16}]},\n", "      {'content': '(Parcel Post)',\n", "       'polygon': [{'x': 7.1573, 'y': 4.4006},\n", "        {'x': 7.868, 'y': 4.3956},\n", "        {'x': 7.868, 'y': 4.5681},\n", "        {'x': 7.1573, 'y': 4.5732}],\n", "       'spans': [{'offset': 463, 'length': 13}]},\n", "      {'content': '03 Oct 2024',\n", "       'polygon': [{'x': 0.6041, 'y': 4.6747},\n", "        {'x': 1.2995, 'y': 4.6747},\n", "        {'x': 1.2995, 'y': 4.7965},\n", "        {'x': 0.6041, 'y': 4.7915}],\n", "       'spans': [{'offset': 477, 'length': 11}]},\n", "      {'content': 'Credit Card',\n", "       'polygon': [{'x': 1.8172, 'y': 4.6747},\n", "        {'x': 2.4771, 'y': 4.6747},\n", "        {'x': 2.4721, 'y': 4.7965},\n", "        {'x': 1.8172, 'y': 4.7915}],\n", "       'spans': [{'offset': 489, 'length': 11}]},\n", "      {'content': '$52.39',\n", "       'polygon': [{'x': 3.5685, 'y': 4.6747},\n", "        {'x': 3.9644, 'y': 4.6747},\n", "        {'x': 3.9644, 'y': 4.7915},\n", "        {'x': 3.5685, 'y': 4.7965}],\n", "       'spans': [{'offset': 501, 'length': 6}]},\n", "      {'content': '$7.95',\n", "       'polygon': [{'x': 7.5329, 'y': 4.5681},\n", "        {'x': 7.8629, 'y': 4.5681},\n", "        {'x': 7.8629, 'y': 4.69},\n", "        {'x': 7.538, 'y': 4.69}],\n", "       'spans': [{'offset': 508, 'length': 5}]},\n", "      {'content': 'Discount:',\n", "       'polygon': [{'x': 6.1827, 'y': 4.8219},\n", "        {'x': 6.7005, 'y': 4.8219},\n", "        {'x': 6.7005, 'y': 4.9437},\n", "        {'x': 6.1827, 'y': 4.9437}],\n", "       'spans': [{'offset': 514, 'length': 9}]},\n", "      {'content': '$0.00',\n", "       'polygon': [{'x': 7.5532, 'y': 4.7864},\n", "        {'x': 7.8629, 'y': 4.7813},\n", "        {'x': 7.868, 'y': 4.964},\n", "        {'x': 7.5532, 'y': 4.964}],\n", "       'spans': [{'offset': 524, 'length': 5}]},\n", "      {'content': 'Sub Total:',\n", "       'polygon': [{'x': 6.1421, 'y': 5.0757},\n", "        {'x': 6.7106, 'y': 5.0757},\n", "        {'x': 6.7106, 'y': 5.2026},\n", "        {'x': 6.1421, 'y': 5.1975}],\n", "       'spans': [{'offset': 530, 'length': 10}]},\n", "      {'content': '$47.63',\n", "       'polygon': [{'x': 7.472, 'y': 5.0706},\n", "        {'x': 7.8629, 'y': 5.0706},\n", "        {'x': 7.8629, 'y': 5.2026},\n", "        {'x': 7.472, 'y': 5.2026}],\n", "       'spans': [{'offset': 541, 'length': 6}]},\n", "      {'content': 'GST (10%):',\n", "       'polygon': [{'x': 6.0659, 'y': 5.3193},\n", "        {'x': 6.7055, 'y': 5.3193},\n", "        {'x': 6.7055, 'y': 5.4615},\n", "        {'x': 6.0659, 'y': 5.4564}],\n", "       'spans': [{'offset': 548, 'length': 10}]},\n", "      {'content': '$4.76',\n", "       'polygon': [{'x': 7.5431, 'y': 5.3244},\n", "        {'x': 7.873, 'y': 5.3193},\n", "        {'x': 7.8781, 'y': 5.4462},\n", "        {'x': 7.5431, 'y': 5.4462}],\n", "       'spans': [{'offset': 559, 'length': 5}]},\n", "      {'content': 'Tax Invoice Total:',\n", "       'polygon': [{'x': 5.7055, 'y': 5.8218},\n", "        {'x': 6.7106, 'y': 5.8218},\n", "        {'x': 6.7106, 'y': 5.9538},\n", "        {'x': 5.7055, 'y': 5.9487}],\n", "       'spans': [{'offset': 565, 'length': 18}]},\n", "      {'content': '$52.39',\n", "       'polygon': [{'x': 7.472, 'y': 5.8218},\n", "        {'x': 7.8629, 'y': 5.8269},\n", "        {'x': 7.8629, 'y': 5.9538},\n", "        {'x': 7.472, 'y': 5.9437}],\n", "       'spans': [{'offset': 584, 'length': 6}]},\n", "      {'content': 'Total Paid:',\n", "       'polygon': [{'x': 6.1015, 'y': 6.0655},\n", "        {'x': 6.7106, 'y': 6.0655},\n", "        {'x': 6.7106, 'y': 6.1924},\n", "        {'x': 6.1015, 'y': 6.1924}],\n", "       'spans': [{'offset': 591, 'length': 11}]},\n", "      {'content': '$52.39',\n", "       'polygon': [{'x': 7.472, 'y': 6.0706},\n", "        {'x': 7.8629, 'y': 6.0706},\n", "        {'x': 7.8629, 'y': 6.1974},\n", "        {'x': 7.472, 'y': 6.1924}],\n", "       'spans': [{'offset': 603, 'length': 6}]},\n", "      {'content': 'Outstanding:',\n", "       'polygon': [{'x': 5.9796, 'y': 6.299},\n", "        {'x': 6.7055, 'y': 6.2939},\n", "        {'x': 6.7055, 'y': 6.4715},\n", "        {'x': 5.9796, 'y': 6.4715}],\n", "       'spans': [{'offset': 610, 'length': 12}]},\n", "      {'content': '$0.00',\n", "       'polygon': [{'x': 7.5431, 'y': 6.3142},\n", "        {'x': 7.868, 'y': 6.3142},\n", "        {'x': 7.868, 'y': 6.4462},\n", "        {'x': 7.5431, 'y': 6.4462}],\n", "       'spans': [{'offset': 623, 'length': 5}]},\n", "      {'content': 'Pet Chemist Online',\n", "       'polygon': [{'x': 0.4162, 'y': 10.8011},\n", "        {'x': 1.5279, 'y': 10.8011},\n", "        {'x': 1.5279, 'y': 10.9331},\n", "        {'x': 0.4162, 'y': 10.9331}],\n", "       'spans': [{'offset': 629, 'length': 18}]},\n", "      {'content': '29-31 Corporation Circuit',\n", "       'polygon': [{'x': 2.67, 'y': 10.796},\n", "        {'x': 4.0761, 'y': 10.796},\n", "        {'x': 4.0761, 'y': 10.9432},\n", "        {'x': 2.67, 'y': 10.9432}],\n", "       'spans': [{'offset': 648, 'length': 25}]},\n", "      {'content': 'P',\n", "       'polygon': [{'x': 5.0203, 'y': 10.8113},\n", "        {'x': 5.1116, 'y': 10.8062},\n", "        {'x': 5.1167, 'y': 10.9229},\n", "        {'x': 5.0253, 'y': 10.928}],\n", "       'spans': [{'offset': 674, 'length': 1}]},\n", "      {'content': '1300 000 738',\n", "       'polygon': [{'x': 5.4873, 'y': 10.8011},\n", "        {'x': 6.2487, 'y': 10.8011},\n", "        {'x': 6.2487, 'y': 10.928},\n", "        {'x': 5.4873, 'y': 10.928}],\n", "       'spans': [{'offset': 676, 'length': 12}]},\n", "      {'content': 'Aussie Pet Meds Pty Ltd',\n", "       'polygon': [{'x': 0.4213, 'y': 10.9483},\n", "        {'x': 1.7513, 'y': 10.9483},\n", "        {'x': 1.7513, 'y': 11.0904},\n", "        {'x': 0.4213, 'y': 11.0854}],\n", "       'spans': [{'offset': 689, 'length': 23}]},\n", "      {'content': 'Tweed Heads South',\n", "       'polygon': [{'x': 2.6802, 'y': 10.9432},\n", "        {'x': 3.8122, 'y': 10.9432},\n", "        {'x': 3.8122, 'y': 11.0803},\n", "        {'x': 2.6802, 'y': 11.0803}],\n", "       'spans': [{'offset': 713, 'length': 17}]},\n", "      {'content': 'E',\n", "       'polygon': [{'x': 5.0203, 'y': 10.9483},\n", "        {'x': 5.1167, 'y': 10.9483},\n", "        {'x': 5.1167, 'y': 11.0701},\n", "        {'x': 5.0203, 'y': 11.0651}],\n", "       'spans': [{'offset': 731, 'length': 1}]},\n", "      {'content': '<EMAIL>',\n", "       'polygon': [{'x': 5.4771, 'y': 10.9483},\n", "        {'x': 6.8629, 'y': 10.9585},\n", "        {'x': 6.8578, 'y': 11.0854},\n", "        {'x': 5.4771, 'y': 11.0803}],\n", "       'spans': [{'offset': 733, 'length': 22}]},\n", "      {'content': 'ABN: **************',\n", "       'polygon': [{'x': 0.4213, 'y': 11.1006},\n", "        {'x': 1.599, 'y': 11.1006},\n", "        {'x': 1.599, 'y': 11.2275},\n", "        {'x': 0.4213, 'y': 11.2326}],\n", "       'spans': [{'offset': 756, 'length': 19}]},\n", "      {'content': 'NSW 2486',\n", "       'polygon': [{'x': 2.6802, 'y': 11.1057},\n", "        {'x': 3.2893, 'y': 11.1057},\n", "        {'x': 3.2893, 'y': 11.2275},\n", "        {'x': 2.6802, 'y': 11.2275}],\n", "       'spans': [{'offset': 776, 'length': 8}]},\n", "      {'content': 'W',\n", "       'polygon': [{'x': 5.0304, 'y': 11.1107},\n", "        {'x': 5.137, 'y': 11.1057},\n", "        {'x': 5.1421, 'y': 11.2173},\n", "        {'x': 5.0355, 'y': 11.2173}],\n", "       'spans': [{'offset': 785, 'length': 1}]},\n", "      {'content': 'www.petchemist.com.au',\n", "       'polygon': [{'x': 5.472, 'y': 11.1057},\n", "        {'x': 6.8426, 'y': 11.1057},\n", "        {'x': 6.8426, 'y': 11.2427},\n", "        {'x': 5.472, 'y': 11.2427}],\n", "       'spans': [{'offset': 787, 'length': 21}]}],\n", "     'words': [{'content': 'PET',\n", "       'polygon': [{'x': 0.9492, 'y': 0.4111},\n", "        {'x': 1.4771, 'y': 0.401},\n", "        {'x': 1.4721, 'y': 0.6751},\n", "        {'x': 0.9391, 'y': 0.6751}],\n", "       'span': {'offset': 0, 'length': 3},\n", "       'confidence': 0.996},\n", "      {'content': 'CHEMIST',\n", "       'polygon': [{'x': 1.5685, 'y': 0.401},\n", "        {'x': 2.8426, 'y': 0.4061},\n", "        {'x': 2.8426, 'y': 0.6649},\n", "        {'x': 1.5584, 'y': 0.6751}],\n", "       'span': {'offset': 4, 'length': 7},\n", "       'confidence': 0.992},\n", "      {'content': 'Tax',\n", "       'polygon': [{'x': 6.9086, 'y': 0.4264},\n", "        {'x': 7.2334, 'y': 0.4264},\n", "        {'x': 7.2334, 'y': 0.6192},\n", "        {'x': 6.9136, 'y': 0.6192}],\n", "       'span': {'offset': 12, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': 'Invoice',\n", "       'polygon': [{'x': 7.269, 'y': 0.4264},\n", "        {'x': 7.9542, 'y': 0.4314},\n", "        {'x': 7.9542, 'y': 0.6192},\n", "        {'x': 7.269, 'y': 0.6192}],\n", "       'span': {'offset': 16, 'length': 7},\n", "       'confidence': 0.982},\n", "      {'content': 'Invoice',\n", "       'polygon': [{'x': 0.2893, 'y': 1.0557},\n", "        {'x': 0.7005, 'y': 1.0557},\n", "        {'x': 0.7056, 'y': 1.1725},\n", "        {'x': 0.2944, 'y': 1.1776}],\n", "       'span': {'offset': 24, 'length': 7},\n", "       'confidence': 0.962},\n", "      {'content': 'Date',\n", "       'polygon': [{'x': 0.7411, 'y': 1.0557},\n", "        {'x': 0.9898, 'y': 1.0507},\n", "        {'x': 0.9898, 'y': 1.1725},\n", "        {'x': 0.7411, 'y': 1.1725}],\n", "       'span': {'offset': 32, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'Invoice',\n", "       'polygon': [{'x': 2.2487, 'y': 1.0507},\n", "        {'x': 2.6548, 'y': 1.0557},\n", "        {'x': 2.6548, 'y': 1.1725},\n", "        {'x': 2.2538, 'y': 1.1776}],\n", "       'span': {'offset': 37, 'length': 7},\n", "       'confidence': 0.961},\n", "      {'content': 'No',\n", "       'polygon': [{'x': 2.6751, 'y': 1.0557},\n", "        {'x': 2.8375, 'y': 1.0507},\n", "        {'x': 2.8375, 'y': 1.1725},\n", "        {'x': 2.6802, 'y': 1.1725}],\n", "       'span': {'offset': 45, 'length': 2},\n", "       'confidence': 0.993},\n", "      {'content': 'Created',\n", "       'polygon': [{'x': 4.2132, 'y': 1.0507},\n", "        {'x': 4.6548, 'y': 1.0507},\n", "        {'x': 4.6599, 'y': 1.1826},\n", "        {'x': 4.2132, 'y': 1.1776}],\n", "       'span': {'offset': 48, 'length': 7},\n", "       'confidence': 0.994},\n", "      {'content': 'Date',\n", "       'polygon': [{'x': 4.6903, 'y': 1.0507},\n", "        {'x': 4.9492, 'y': 1.0507},\n", "        {'x': 4.9492, 'y': 1.1826},\n", "        {'x': 4.6954, 'y': 1.1826}],\n", "       'span': {'offset': 56, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'Tracking',\n", "       'polygon': [{'x': 6.1725, 'y': 1.0507},\n", "        {'x': 6.67, 'y': 1.0456},\n", "        {'x': 6.67, 'y': 1.1928},\n", "        {'x': 6.1725, 'y': 1.1877}],\n", "       'span': {'offset': 61, 'length': 8},\n", "       'confidence': 0.994},\n", "      {'content': 'Code',\n", "       'polygon': [{'x': 6.7005, 'y': 1.0456},\n", "        {'x': 6.9999, 'y': 1.0456},\n", "        {'x': 6.9999, 'y': 1.1928},\n", "        {'x': 6.7005, 'y': 1.1928}],\n", "       'span': {'offset': 70, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': '838901',\n", "       'polygon': [{'x': 2.2589, 'y': 1.208},\n", "        {'x': 2.67, 'y': 1.2029},\n", "        {'x': 2.67, 'y': 1.3197},\n", "        {'x': 2.2589, 'y': 1.3197}],\n", "       'span': {'offset': 75, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': '03',\n", "       'polygon': [{'x': 4.2132, 'y': 1.2029},\n", "        {'x': 4.3502, 'y': 1.2029},\n", "        {'x': 4.3502, 'y': 1.3248},\n", "        {'x': 4.2132, 'y': 1.3197}],\n", "       'span': {'offset': 82, 'length': 2},\n", "       'confidence': 0.996},\n", "      {'content': 'Oct',\n", "       'polygon': [{'x': 4.3807, 'y': 1.2029},\n", "        {'x': 4.5888, 'y': 1.2029},\n", "        {'x': 4.5939, 'y': 1.3248},\n", "        {'x': 4.3807, 'y': 1.3248}],\n", "       'span': {'offset': 85, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': '2024',\n", "       'polygon': [{'x': 4.6142, 'y': 1.2029},\n", "        {'x': 4.8781, 'y': 1.1979},\n", "        {'x': 4.8781, 'y': 1.3248},\n", "        {'x': 4.6142, 'y': 1.3248}],\n", "       'span': {'offset': 89, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'Customer:',\n", "       'polygon': [{'x': 0.3452, 'y': 1.6648},\n", "        {'x': 0.9391, 'y': 1.6598},\n", "        {'x': 0.9442, 'y': 1.7867},\n", "        {'x': 0.3452, 'y': 1.7816}],\n", "       'span': {'offset': 94, 'length': 9},\n", "       'confidence': 0.995},\n", "      {'content': 'Ship',\n", "       'polygon': [{'x': 4.2335, 'y': 1.6598},\n", "        {'x': 4.4771, 'y': 1.6598},\n", "        {'x': 4.4822, 'y': 1.7968},\n", "        {'x': 4.2385, 'y': 1.8019}],\n", "       'span': {'offset': 104, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'To:',\n", "       'polygon': [{'x': 4.5127, 'y': 1.6598},\n", "        {'x': 4.6903, 'y': 1.6598},\n", "        {'x': 4.6903, 'y': 1.7968},\n", "        {'x': 4.5177, 'y': 1.7968}],\n", "       'span': {'offset': 109, 'length': 3},\n", "       'confidence': 0.993},\n", "      {'content': '<PERSON>',\n", "       'polygon': [{'x': 0.335, 'y': 1.812},\n", "        {'x': 0.7157, 'y': 1.812},\n", "        {'x': 0.7157, 'y': 1.944},\n", "        {'x': 0.3401, 'y': 1.944}],\n", "       'span': {'offset': 113, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': '<PERSON>',\n", "       'polygon': [{'x': 0.7411, 'y': 1.812},\n", "        {'x': 1.0964, 'y': 1.812},\n", "        {'x': 1.0964, 'y': 1.9389},\n", "        {'x': 0.7411, 'y': 1.944}],\n", "       'span': {'offset': 120, 'length': 5},\n", "       'confidence': 0.995},\n", "      {'content': '<PERSON>',\n", "       'polygon': [{'x': 4.2335, 'y': 1.812},\n", "        {'x': 4.6091, 'y': 1.807},\n", "        {'x': 4.6091, 'y': 1.944},\n", "        {'x': 4.2385, 'y': 1.9389}],\n", "       'span': {'offset': 126, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': '<PERSON>',\n", "       'polygon': [{'x': 4.6345, 'y': 1.807},\n", "        {'x': 4.9797, 'y': 1.812},\n", "        {'x': 4.9797, 'y': 1.944},\n", "        {'x': 4.6345, 'y': 1.944}],\n", "       'span': {'offset': 133, 'length': 5},\n", "       'confidence': 0.995},\n", "      {'content': 'PO',\n", "       'polygon': [{'x': 0.335, 'y': 1.9592},\n", "        {'x': 0.4873, 'y': 1.9592},\n", "        {'x': 0.4873, 'y': 2.081},\n", "        {'x': 0.335, 'y': 2.081}],\n", "       'span': {'offset': 139, 'length': 2},\n", "       'confidence': 0.995},\n", "      {'content': 'Box',\n", "       'polygon': [{'x': 0.5431, 'y': 1.9592},\n", "        {'x': 0.7563, 'y': 1.9592},\n", "        {'x': 0.7563, 'y': 2.081},\n", "        {'x': 0.5431, 'y': 2.081}],\n", "       'span': {'offset': 142, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': '6026',\n", "       'polygon': [{'x': 0.7817, 'y': 1.9592},\n", "        {'x': 1.0508, 'y': 1.9643},\n", "        {'x': 1.0558, 'y': 2.081},\n", "        {'x': 0.7817, 'y': 2.081}],\n", "       'span': {'offset': 146, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'PO',\n", "       'polygon': [{'x': 4.2335, 'y': 1.9592},\n", "        {'x': 4.3807, 'y': 1.9592},\n", "        {'x': 4.3807, 'y': 2.0861},\n", "        {'x': 4.2335, 'y': 2.0861}],\n", "       'span': {'offset': 151, 'length': 2},\n", "       'confidence': 0.995},\n", "      {'content': 'Box',\n", "       'polygon': [{'x': 4.4365, 'y': 1.9592},\n", "        {'x': 4.6548, 'y': 1.9592},\n", "        {'x': 4.6548, 'y': 2.081},\n", "        {'x': 4.4365, 'y': 2.0861}],\n", "       'span': {'offset': 154, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': '6026',\n", "       'polygon': [{'x': 4.6802, 'y': 1.9592},\n", "        {'x': 4.9492, 'y': 1.9592},\n", "        {'x': 4.9492, 'y': 2.081},\n", "        {'x': 4.6802, 'y': 2.081}],\n", "       'span': {'offset': 158, 'length': 4},\n", "       'confidence': 0.986},\n", "      {'content': 'Man<PERSON>,',\n", "       'polygon': [{'x': 0.335, 'y': 2.1013},\n", "        {'x': 0.7056, 'y': 2.1064},\n", "        {'x': 0.7056, 'y': 2.2435},\n", "        {'x': 0.3401, 'y': 2.2435}],\n", "       'span': {'offset': 163, 'length': 6},\n", "       'confidence': 0.994},\n", "      {'content': 'Queensland',\n", "       'polygon': [{'x': 0.731, 'y': 2.1064},\n", "        {'x': 1.401, 'y': 2.1013},\n", "        {'x': 1.401, 'y': 2.2435},\n", "        {'x': 0.736, 'y': 2.2435}],\n", "       'span': {'offset': 170, 'length': 10},\n", "       'confidence': 0.994},\n", "      {'content': '4179',\n", "       'polygon': [{'x': 1.4315, 'y': 2.0963},\n", "        {'x': 1.7005, 'y': 2.0963},\n", "        {'x': 1.7005, 'y': 2.2384},\n", "        {'x': 1.4315, 'y': 2.2435}],\n", "       'span': {'offset': 181, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'Man<PERSON>,',\n", "       'polygon': [{'x': 4.2284, 'y': 2.1064},\n", "        {'x': 4.5989, 'y': 2.1064},\n", "        {'x': 4.604, 'y': 2.2435},\n", "        {'x': 4.2335, 'y': 2.2435}],\n", "       'span': {'offset': 186, 'length': 6},\n", "       'confidence': 0.994},\n", "      {'content': 'Queensland',\n", "       'polygon': [{'x': 4.6294, 'y': 2.1064},\n", "        {'x': 5.2893, 'y': 2.1013},\n", "        {'x': 5.2944, 'y': 2.2384},\n", "        {'x': 4.6294, 'y': 2.2435}],\n", "       'span': {'offset': 193, 'length': 10},\n", "       'confidence': 0.993},\n", "      {'content': '4179',\n", "       'polygon': [{'x': 5.3198, 'y': 2.1013},\n", "        {'x': 5.5939, 'y': 2.1013},\n", "        {'x': 5.5939, 'y': 2.2384},\n", "        {'x': 5.3198, 'y': 2.2384}],\n", "       'span': {'offset': 204, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'Australia',\n", "       'polygon': [{'x': 0.3452, 'y': 2.2638},\n", "        {'x': 0.8173, 'y': 2.2587},\n", "        {'x': 0.8223, 'y': 2.3907},\n", "        {'x': 0.3503, 'y': 2.3856}],\n", "       'span': {'offset': 209, 'length': 9},\n", "       'confidence': 0.992},\n", "      {'content': 'Australia',\n", "       'polygon': [{'x': 4.2385, 'y': 2.2638},\n", "        {'x': 4.7157, 'y': 2.2587},\n", "        {'x': 4.7208, 'y': 2.3856},\n", "        {'x': 4.2385, 'y': 2.3805}],\n", "       'span': {'offset': 219, 'length': 9},\n", "       'confidence': 0.994},\n", "      {'content': 'Phone:',\n", "       'polygon': [{'x': 0.335, 'y': 2.5226},\n", "        {'x': 0.7411, 'y': 2.5277},\n", "        {'x': 0.7411, 'y': 2.6546},\n", "        {'x': 0.3401, 'y': 2.6597}],\n", "       'span': {'offset': 229, 'length': 6},\n", "       'confidence': 0.994},\n", "      {'content': '**********',\n", "       'polygon': [{'x': 0.7665, 'y': 2.5277},\n", "        {'x': 1.4619, 'y': 2.5277},\n", "        {'x': 1.4619, 'y': 2.6546},\n", "        {'x': 0.7665, 'y': 2.6546}],\n", "       'span': {'offset': 236, 'length': 10},\n", "       'confidence': 0.991},\n", "      {'content': '<EMAIL>',\n", "       'polygon': [{'x': 0.335, 'y': 2.6901},\n", "        {'x': 1.7056, 'y': 2.68},\n", "        {'x': 1.7106, 'y': 2.817},\n", "        {'x': 0.335, 'y': 2.8221}],\n", "       'span': {'offset': 247, 'length': 20},\n", "       'confidence': 0.95},\n", "      {'content': 'Item',\n", "       'polygon': [{'x': 0.2284, 'y': 3.2079},\n", "        {'x': 0.4365, 'y': 3.2079},\n", "        {'x': 0.4365, 'y': 3.3195},\n", "        {'x': 0.2284, 'y': 3.3144}],\n", "       'span': {'offset': 268, 'length': 4},\n", "       'confidence': 0.914},\n", "      {'content': 'Options',\n", "       'polygon': [{'x': 3.0406, 'y': 3.2028},\n", "        {'x': 3.4924, 'y': 3.2028},\n", "        {'x': 3.4924, 'y': 3.3347},\n", "        {'x': 3.0507, 'y': 3.3347}],\n", "       'span': {'offset': 273, 'length': 7},\n", "       'confidence': 0.994},\n", "      {'content': 'Qty',\n", "       'polygon': [{'x': 4.8121, 'y': 3.1977},\n", "        {'x': 5.0101, 'y': 3.2028},\n", "        {'x': 5.005, 'y': 3.3347},\n", "        {'x': 4.8071, 'y': 3.3246}],\n", "       'span': {'offset': 281, 'length': 3},\n", "       'confidence': 0.992},\n", "      {'content': 'Unit',\n", "       'polygon': [{'x': 5.4619, 'y': 3.1977},\n", "        {'x': 5.7005, 'y': 3.1977},\n", "        {'x': 5.7005, 'y': 3.3246},\n", "        {'x': 5.4619, 'y': 3.3195}],\n", "       'span': {'offset': 285, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'Price',\n", "       'polygon': [{'x': 5.7258, 'y': 3.1977},\n", "        {'x': 6.0101, 'y': 3.1977},\n", "        {'x': 6.0101, 'y': 3.3297},\n", "        {'x': 5.7258, 'y': 3.3246}],\n", "       'span': {'offset': 290, 'length': 5},\n", "       'confidence': 0.995},\n", "      {'content': 'Discount',\n", "       'polygon': [{'x': 6.5126, 'y': 3.2028},\n", "        {'x': 7.0202, 'y': 3.2079},\n", "        {'x': 7.0202, 'y': 3.3246},\n", "        {'x': 6.5076, 'y': 3.3144}],\n", "       'span': {'offset': 296, 'length': 8},\n", "       'confidence': 0.982},\n", "      {'content': 'Subtotal',\n", "       'polygon': [{'x': 7.5532, 'y': 3.2028},\n", "        {'x': 8.0304, 'y': 3.1977},\n", "        {'x': 8.0304, 'y': 3.3195},\n", "        {'x': 7.5532, 'y': 3.3144}],\n", "       'span': {'offset': 305, 'length': 8},\n", "       'confidence': 0.993},\n", "      {'content': 'Uricosal',\n", "       'polygon': [{'x': 0.2893, 'y': 3.416},\n", "        {'x': 0.7411, 'y': 3.416},\n", "        {'x': 0.7462, 'y': 3.5479},\n", "        {'x': 0.2893, 'y': 3.5479}],\n", "       'span': {'offset': 314, 'length': 8},\n", "       'confidence': 0.994},\n", "      {'content': 'Potassium',\n", "       'polygon': [{'x': 0.7665, 'y': 3.416},\n", "        {'x': 1.3096, 'y': 3.416},\n", "        {'x': 1.3147, 'y': 3.5479},\n", "        {'x': 0.7716, 'y': 3.5479}],\n", "       'span': {'offset': 323, 'length': 9},\n", "       'confidence': 0.993},\n", "      {'content': 'Citrate',\n", "       'polygon': [{'x': 1.3807, 'y': 3.416},\n", "        {'x': 1.7462, 'y': 3.416},\n", "        {'x': 1.7462, 'y': 3.5479},\n", "        {'x': 1.3858, 'y': 3.5479}],\n", "       'span': {'offset': 333, 'length': 7},\n", "       'confidence': 0.993},\n", "      {'content': 'Mixture',\n", "       'polygon': [{'x': 1.7716, 'y': 3.416},\n", "        {'x': 2.1878, 'y': 3.416},\n", "        {'x': 2.1929, 'y': 3.553},\n", "        {'x': 1.7766, 'y': 3.5479}],\n", "       'span': {'offset': 341, 'length': 7},\n", "       'confidence': 0.993},\n", "      {'content': '(200ml)',\n", "       'polygon': [{'x': 2.2132, 'y': 3.416},\n", "        {'x': 2.6446, 'y': 3.4109},\n", "        {'x': 2.6446, 'y': 3.5632},\n", "        {'x': 2.2183, 'y': 3.5581}],\n", "       'span': {'offset': 349, 'length': 7},\n", "       'confidence': 0.993},\n", "      {'content': '1',\n", "       'polygon': [{'x': 4.8629, 'y': 3.416},\n", "        {'x': 4.9187, 'y': 3.416},\n", "        {'x': 4.9238, 'y': 3.5276},\n", "        {'x': 4.868, 'y': 3.5327}],\n", "       'span': {'offset': 357, 'length': 1},\n", "       'confidence': 0.995},\n", "      {'content': '$39.68',\n", "       'polygon': [{'x': 5.5431, 'y': 3.416},\n", "        {'x': 5.9441, 'y': 3.416},\n", "        {'x': 5.9441, 'y': 3.5428},\n", "        {'x': 5.5482, 'y': 3.5428}],\n", "       'span': {'offset': 359, 'length': 6},\n", "       'confidence': 0.994},\n", "      {'content': '$39.68',\n", "       'polygon': [{'x': 7.5735, 'y': 3.421},\n", "        {'x': 7.9593, 'y': 3.416},\n", "        {'x': 7.9593, 'y': 3.5428},\n", "        {'x': 7.5786, 'y': 3.5378}],\n", "       'span': {'offset': 366, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': 'Payments',\n", "       'polygon': [{'x': 0.3096, 'y': 4.1012},\n", "        {'x': 0.9848, 'y': 4.1113},\n", "        {'x': 0.9848, 'y': 4.2534},\n", "        {'x': 0.3096, 'y': 4.2534}],\n", "       'span': {'offset': 373, 'length': 8},\n", "       'confidence': 0.993},\n", "      {'content': 'Product',\n", "       'polygon': [{'x': 5.9543, 'y': 4.02},\n", "        {'x': 6.3908, 'y': 4.02},\n", "        {'x': 6.3908, 'y': 4.1367},\n", "        {'x': 5.9593, 'y': 4.1367}],\n", "       'span': {'offset': 382, 'length': 7},\n", "       'confidence': 0.971},\n", "      {'content': 'Cost:',\n", "       'polygon': [{'x': 6.4111, 'y': 4.02},\n", "        {'x': 6.6954, 'y': 4.025},\n", "        {'x': 6.7005, 'y': 4.1367},\n", "        {'x': 6.4162, 'y': 4.1367}],\n", "       'span': {'offset': 390, 'length': 5},\n", "       'confidence': 0.994},\n", "      {'content': '$39.68',\n", "       'polygon': [{'x': 7.4771, 'y': 4.02},\n", "        {'x': 7.8629, 'y': 4.0149},\n", "        {'x': 7.8629, 'y': 4.1418},\n", "        {'x': 7.4771, 'y': 4.1418}],\n", "       'span': {'offset': 396, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': 'AusPost',\n", "       'polygon': [{'x': 6.9644, 'y': 4.2636},\n", "        {'x': 7.4213, 'y': 4.2636},\n", "        {'x': 7.4213, 'y': 4.3905},\n", "        {'x': 6.9644, 'y': 4.3956}],\n", "       'span': {'offset': 403, 'length': 7},\n", "       'confidence': 0.983},\n", "      {'content': 'eParcel',\n", "       'polygon': [{'x': 7.4466, 'y': 4.2636},\n", "        {'x': 7.8832, 'y': 4.2585},\n", "        {'x': 7.8832, 'y': 4.3905},\n", "        {'x': 7.4466, 'y': 4.3905}],\n", "       'span': {'offset': 411, 'length': 7},\n", "       'confidence': 0.965},\n", "      {'content': 'Payments',\n", "       'polygon': [{'x': 0.5584, 'y': 4.4616},\n", "        {'x': 1.1421, 'y': 4.4616},\n", "        {'x': 1.1421, 'y': 4.5935},\n", "        {'x': 0.5634, 'y': 4.5884}],\n", "       'span': {'offset': 419, 'length': 8},\n", "       'confidence': 0.994},\n", "      {'content': 'Method',\n", "       'polygon': [{'x': 1.7614, 'y': 4.4565},\n", "        {'x': 2.1776, 'y': 4.4616},\n", "        {'x': 2.1776, 'y': 4.5834},\n", "        {'x': 1.7614, 'y': 4.5783}],\n", "       'span': {'offset': 428, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': 'Ref',\n", "       'polygon': [{'x': 2.9289, 'y': 4.4514},\n", "        {'x': 3.1218, 'y': 4.4514},\n", "        {'x': 3.1269, 'y': 4.5732},\n", "        {'x': 2.9289, 'y': 4.5732}],\n", "       'span': {'offset': 435, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': 'Amount',\n", "       'polygon': [{'x': 3.5786, 'y': 4.4616},\n", "        {'x': 4.0355, 'y': 4.4565},\n", "        {'x': 4.0304, 'y': 4.5884},\n", "        {'x': 3.5786, 'y': 4.5783}],\n", "       'span': {'offset': 439, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': 'Delivery',\n", "       'polygon': [{'x': 5.8121, 'y': 4.4209},\n", "        {'x': 6.2334, 'y': 4.4159},\n", "        {'x': 6.2334, 'y': 4.5478},\n", "        {'x': 5.8121, 'y': 4.5478}],\n", "       'span': {'offset': 446, 'length': 8},\n", "       'confidence': 0.995},\n", "      {'content': 'Method:',\n", "       'polygon': [{'x': 6.2588, 'y': 4.4159},\n", "        {'x': 6.7005, 'y': 4.4209},\n", "        {'x': 6.7055, 'y': 4.5529},\n", "        {'x': 6.2639, 'y': 4.5529}],\n", "       'span': {'offset': 455, 'length': 7},\n", "       'confidence': 0.987},\n", "      {'content': '(<PERSON><PERSON><PERSON>',\n", "       'polygon': [{'x': 7.1624, 'y': 4.4057},\n", "        {'x': 7.5431, 'y': 4.4057},\n", "        {'x': 7.538, 'y': 4.5732},\n", "        {'x': 7.1624, 'y': 4.5681}],\n", "       'span': {'offset': 463, 'length': 7},\n", "       'confidence': 0.99},\n", "      {'content': 'Post)',\n", "       'polygon': [{'x': 7.5735, 'y': 4.4006},\n", "        {'x': 7.868, 'y': 4.4006},\n", "        {'x': 7.868, 'y': 4.5732},\n", "        {'x': 7.5735, 'y': 4.5732}],\n", "       'span': {'offset': 471, 'length': 5},\n", "       'confidence': 0.994},\n", "      {'content': '03',\n", "       'polygon': [{'x': 0.6142, 'y': 4.6747},\n", "        {'x': 0.7614, 'y': 4.6747},\n", "        {'x': 0.7614, 'y': 4.7965},\n", "        {'x': 0.6193, 'y': 4.7965}],\n", "       'span': {'offset': 477, 'length': 2},\n", "       'confidence': 0.994},\n", "      {'content': 'Oct',\n", "       'polygon': [{'x': 0.7817, 'y': 4.6747},\n", "        {'x': 0.9949, 'y': 4.6747},\n", "        {'x': 0.9949, 'y': 4.7965},\n", "        {'x': 0.7868, 'y': 4.7965}],\n", "       'span': {'offset': 480, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': '2024',\n", "       'polygon': [{'x': 1.0152, 'y': 4.6747},\n", "        {'x': 1.2843, 'y': 4.6747},\n", "        {'x': 1.2843, 'y': 4.7965},\n", "        {'x': 1.0203, 'y': 4.7965}],\n", "       'span': {'offset': 484, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'Credit',\n", "       'polygon': [{'x': 1.8274, 'y': 4.6747},\n", "        {'x': 2.1726, 'y': 4.6747},\n", "        {'x': 2.1726, 'y': 4.7965},\n", "        {'x': 1.8325, 'y': 4.7965}],\n", "       'span': {'offset': 489, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': 'Card',\n", "       'polygon': [{'x': 2.1929, 'y': 4.6747},\n", "        {'x': 2.4568, 'y': 4.6747},\n", "        {'x': 2.4619, 'y': 4.8016},\n", "        {'x': 2.198, 'y': 4.7965}],\n", "       'span': {'offset': 496, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': '$52.39',\n", "       'polygon': [{'x': 3.5685, 'y': 4.6798},\n", "        {'x': 3.9594, 'y': 4.6747},\n", "        {'x': 3.9594, 'y': 4.7965},\n", "        {'x': 3.5736, 'y': 4.8016}],\n", "       'span': {'offset': 501, 'length': 6},\n", "       'confidence': 0.994},\n", "      {'content': '$7.95',\n", "       'polygon': [{'x': 7.5482, 'y': 4.5681},\n", "        {'x': 7.8578, 'y': 4.5681},\n", "        {'x': 7.8578, 'y': 4.69},\n", "        {'x': 7.5482, 'y': 4.69}],\n", "       'span': {'offset': 508, 'length': 5},\n", "       'confidence': 0.994},\n", "      {'content': 'Discount:',\n", "       'polygon': [{'x': 6.1878, 'y': 4.8372},\n", "        {'x': 6.7005, 'y': 4.827},\n", "        {'x': 6.7005, 'y': 4.9488},\n", "        {'x': 6.1878, 'y': 4.9387}],\n", "       'span': {'offset': 514, 'length': 9},\n", "       'confidence': 0.993},\n", "      {'content': '$0.00',\n", "       'polygon': [{'x': 7.5532, 'y': 4.7813},\n", "        {'x': 7.8578, 'y': 4.7813},\n", "        {'x': 7.8578, 'y': 4.964},\n", "        {'x': 7.5532, 'y': 4.964}],\n", "       'span': {'offset': 524, 'length': 5},\n", "       'confidence': 0.986},\n", "      {'content': 'Sub',\n", "       'polygon': [{'x': 6.1522, 'y': 5.0808},\n", "        {'x': 6.3502, 'y': 5.0757},\n", "        {'x': 6.3553, 'y': 5.1975},\n", "        {'x': 6.1573, 'y': 5.1975}],\n", "       'span': {'offset': 530, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': 'Total:',\n", "       'polygon': [{'x': 6.4162, 'y': 5.0757},\n", "        {'x': 6.7157, 'y': 5.0757},\n", "        {'x': 6.7157, 'y': 5.2026},\n", "        {'x': 6.4162, 'y': 5.1975}],\n", "       'span': {'offset': 534, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': '$47.63',\n", "       'polygon': [{'x': 7.4771, 'y': 5.0808},\n", "        {'x': 7.8578, 'y': 5.0757},\n", "        {'x': 7.8578, 'y': 5.2077},\n", "        {'x': 7.4771, 'y': 5.2077}],\n", "       'span': {'offset': 541, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': 'GST',\n", "       'polygon': [{'x': 6.071, 'y': 5.3244},\n", "        {'x': 6.3147, 'y': 5.3193},\n", "        {'x': 6.3147, 'y': 5.4615},\n", "        {'x': 6.071, 'y': 5.4564}],\n", "       'span': {'offset': 548, 'length': 3},\n", "       'confidence': 0.994},\n", "      {'content': '(10%):',\n", "       'polygon': [{'x': 6.34, 'y': 5.3193},\n", "        {'x': 6.7106, 'y': 5.3244},\n", "        {'x': 6.7106, 'y': 5.4665},\n", "        {'x': 6.34, 'y': 5.4615}],\n", "       'span': {'offset': 552, 'length': 6},\n", "       'confidence': 0.775},\n", "      {'content': '$4.76',\n", "       'polygon': [{'x': 7.5431, 'y': 5.3193},\n", "        {'x': 7.8629, 'y': 5.3193},\n", "        {'x': 7.8629, 'y': 5.4462},\n", "        {'x': 7.5431, 'y': 5.4462}],\n", "       'span': {'offset': 559, 'length': 5},\n", "       'confidence': 0.995},\n", "      {'content': 'Tax',\n", "       'polygon': [{'x': 5.7258, 'y': 5.8218},\n", "        {'x': 5.9187, 'y': 5.8218},\n", "        {'x': 5.9187, 'y': 5.9538},\n", "        {'x': 5.7258, 'y': 5.9538}],\n", "       'span': {'offset': 565, 'length': 3},\n", "       'confidence': 0.997},\n", "      {'content': 'Invoice',\n", "       'polygon': [{'x': 5.9441, 'y': 5.8218},\n", "        {'x': 6.3553, 'y': 5.8269},\n", "        {'x': 6.3553, 'y': 5.9487},\n", "        {'x': 5.9441, 'y': 5.9538}],\n", "       'span': {'offset': 569, 'length': 7},\n", "       'confidence': 0.992},\n", "      {'content': 'Total:',\n", "       'polygon': [{'x': 6.3857, 'y': 5.8269},\n", "        {'x': 6.7106, 'y': 5.8269},\n", "        {'x': 6.7106, 'y': 5.9538},\n", "        {'x': 6.3908, 'y': 5.9487}],\n", "       'span': {'offset': 577, 'length': 6},\n", "       'confidence': 0.994},\n", "      {'content': '$52.39',\n", "       'polygon': [{'x': 7.472, 'y': 5.8218},\n", "        {'x': 7.8578, 'y': 5.832},\n", "        {'x': 7.8578, 'y': 5.9487},\n", "        {'x': 7.4771, 'y': 5.9487}],\n", "       'span': {'offset': 584, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': 'Total',\n", "       'polygon': [{'x': 6.1218, 'y': 6.0706},\n", "        {'x': 6.3959, 'y': 6.0706},\n", "        {'x': 6.3959, 'y': 6.1974},\n", "        {'x': 6.1218, 'y': 6.1924}],\n", "       'span': {'offset': 591, 'length': 5},\n", "       'confidence': 0.995},\n", "      {'content': 'Paid:',\n", "       'polygon': [{'x': 6.4213, 'y': 6.0706},\n", "        {'x': 6.7055, 'y': 6.0706},\n", "        {'x': 6.7055, 'y': 6.1924},\n", "        {'x': 6.4213, 'y': 6.1974}],\n", "       'span': {'offset': 597, 'length': 5},\n", "       'confidence': 0.995},\n", "      {'content': '$52.39',\n", "       'polygon': [{'x': 7.4771, 'y': 6.0756},\n", "        {'x': 7.8578, 'y': 6.0756},\n", "        {'x': 7.8629, 'y': 6.1974},\n", "        {'x': 7.4822, 'y': 6.1974}],\n", "       'span': {'offset': 603, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': 'Outstanding:',\n", "       'polygon': [{'x': 5.9847, 'y': 6.304},\n", "        {'x': 6.7005, 'y': 6.299},\n", "        {'x': 6.7005, 'y': 6.4766},\n", "        {'x': 5.9847, 'y': 6.4715}],\n", "       'span': {'offset': 610, 'length': 12},\n", "       'confidence': 0.991},\n", "      {'content': '$0.00',\n", "       'polygon': [{'x': 7.5431, 'y': 6.3142},\n", "        {'x': 7.8578, 'y': 6.3142},\n", "        {'x': 7.8578, 'y': 6.4462},\n", "        {'x': 7.5431, 'y': 6.4462}],\n", "       'span': {'offset': 623, 'length': 5},\n", "       'confidence': 0.993},\n", "      {'content': 'Pet',\n", "       'polygon': [{'x': 0.4264, 'y': 10.8011},\n", "        {'x': 0.6244, 'y': 10.8011},\n", "        {'x': 0.6244, 'y': 10.9382},\n", "        {'x': 0.4315, 'y': 10.9382}],\n", "       'span': {'offset': 629, 'length': 3},\n", "       'confidence': 0.996},\n", "      {'content': 'Chemist',\n", "       'polygon': [{'x': 0.6497, 'y': 10.8011},\n", "        {'x': 1.1218, 'y': 10.8011},\n", "        {'x': 1.1218, 'y': 10.9382},\n", "        {'x': 0.6497, 'y': 10.9382}],\n", "       'span': {'offset': 633, 'length': 7},\n", "       'confidence': 0.995},\n", "      {'content': 'Online',\n", "       'polygon': [{'x': 1.1472, 'y': 10.8011},\n", "        {'x': 1.5178, 'y': 10.8062},\n", "        {'x': 1.5178, 'y': 10.9382},\n", "        {'x': 1.1472, 'y': 10.9382}],\n", "       'span': {'offset': 641, 'length': 6},\n", "       'confidence': 0.995},\n", "      {'content': '29-31',\n", "       'polygon': [{'x': 2.6903, 'y': 10.8011},\n", "        {'x': 3.0051, 'y': 10.8011},\n", "        {'x': 3.0051, 'y': 10.9432},\n", "        {'x': 2.6954, 'y': 10.9432}],\n", "       'span': {'offset': 648, 'length': 5},\n", "       'confidence': 0.993},\n", "      {'content': 'Corporation',\n", "       'polygon': [{'x': 3.0304, 'y': 10.8011},\n", "        {'x': 3.6751, 'y': 10.8011},\n", "        {'x': 3.6802, 'y': 10.9483},\n", "        {'x': 3.0355, 'y': 10.9432}],\n", "       'span': {'offset': 654, 'length': 11},\n", "       'confidence': 0.991},\n", "      {'content': 'Circuit',\n", "       'polygon': [{'x': 3.7157, 'y': 10.8011},\n", "        {'x': 4.0761, 'y': 10.796},\n", "        {'x': 4.0812, 'y': 10.9432},\n", "        {'x': 3.7157, 'y': 10.9483}],\n", "       'span': {'offset': 666, 'length': 7},\n", "       'confidence': 0.993},\n", "      {'content': 'P',\n", "       'polygon': [{'x': 5.0203, 'y': 10.8113},\n", "        {'x': 5.0812, 'y': 10.8062},\n", "        {'x': 5.0863, 'y': 10.9229},\n", "        {'x': 5.0253, 'y': 10.928}],\n", "       'span': {'offset': 674, 'length': 1},\n", "       'confidence': 0.995},\n", "      {'content': '1300',\n", "       'polygon': [{'x': 5.4923, 'y': 10.8062},\n", "        {'x': 5.7563, 'y': 10.8062},\n", "        {'x': 5.7614, 'y': 10.9331},\n", "        {'x': 5.4974, 'y': 10.928}],\n", "       'span': {'offset': 676, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': '000',\n", "       'polygon': [{'x': 5.7918, 'y': 10.8062},\n", "        {'x': 5.9949, 'y': 10.8062},\n", "        {'x': 5.9999, 'y': 10.9331},\n", "        {'x': 5.7918, 'y': 10.9331}],\n", "       'span': {'offset': 681, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': '738',\n", "       'polygon': [{'x': 6.0406, 'y': 10.8062},\n", "        {'x': 6.2436, 'y': 10.8062},\n", "        {'x': 6.2436, 'y': 10.9331},\n", "        {'x': 6.0406, 'y': 10.9331}],\n", "       'span': {'offset': 685, 'length': 3},\n", "       'confidence': 0.996},\n", "      {'content': 'Aussie',\n", "       'polygon': [{'x': 0.4315, 'y': 10.9534},\n", "        {'x': 0.7969, 'y': 10.9534},\n", "        {'x': 0.802, 'y': 11.0854},\n", "        {'x': 0.4315, 'y': 11.0854}],\n", "       'span': {'offset': 689, 'length': 6},\n", "       'confidence': 0.993},\n", "      {'content': 'Pet',\n", "       'polygon': [{'x': 0.8223, 'y': 10.9534},\n", "        {'x': 1.0051, 'y': 10.9534},\n", "        {'x': 1.0051, 'y': 11.0854},\n", "        {'x': 0.8274, 'y': 11.0854}],\n", "       'span': {'offset': 696, 'length': 3},\n", "       'confidence': 0.992},\n", "      {'content': 'Meds',\n", "       'polygon': [{'x': 1.0304, 'y': 10.9534},\n", "        {'x': 1.335, 'y': 10.9534},\n", "        {'x': 1.335, 'y': 11.0904},\n", "        {'x': 1.0304, 'y': 11.0854}],\n", "       'span': {'offset': 700, 'length': 4},\n", "       'confidence': 0.992},\n", "      {'content': 'Pty',\n", "       'polygon': [{'x': 1.3604, 'y': 10.9534},\n", "        {'x': 1.5381, 'y': 10.9483},\n", "        {'x': 1.5431, 'y': 11.0955},\n", "        {'x': 1.3655, 'y': 11.0904}],\n", "       'span': {'offset': 705, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': 'Ltd',\n", "       'polygon': [{'x': 1.5634, 'y': 10.9483},\n", "        {'x': 1.7462, 'y': 10.9483},\n", "        {'x': 1.7462, 'y': 11.0955},\n", "        {'x': 1.5685, 'y': 11.0955}],\n", "       'span': {'offset': 709, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': 'Tweed',\n", "       'polygon': [{'x': 2.6954, 'y': 10.9483},\n", "        {'x': 3.0507, 'y': 10.9534},\n", "        {'x': 3.0558, 'y': 11.0854},\n", "        {'x': 2.7005, 'y': 11.0854}],\n", "       'span': {'offset': 713, 'length': 5},\n", "       'confidence': 0.995},\n", "      {'content': 'Heads',\n", "       'polygon': [{'x': 3.0761, 'y': 10.9534},\n", "        {'x': 3.4517, 'y': 10.9534},\n", "        {'x': 3.4568, 'y': 11.0854},\n", "        {'x': 3.0812, 'y': 11.0803}],\n", "       'span': {'offset': 719, 'length': 5},\n", "       'confidence': 0.995},\n", "      {'content': 'South',\n", "       'polygon': [{'x': 3.4873, 'y': 10.9534},\n", "        {'x': 3.802, 'y': 10.9483},\n", "        {'x': 3.802, 'y': 11.0854},\n", "        {'x': 3.4924, 'y': 11.0854}],\n", "       'span': {'offset': 725, 'length': 5},\n", "       'confidence': 0.995},\n", "      {'content': 'E',\n", "       'polygon': [{'x': 5.0253, 'y': 10.9483},\n", "        {'x': 5.0913, 'y': 10.9483},\n", "        {'x': 5.0863, 'y': 11.0701},\n", "        {'x': 5.0253, 'y': 11.0651}],\n", "       'span': {'offset': 731, 'length': 1},\n", "       'confidence': 0.995},\n", "      {'content': '<EMAIL>',\n", "       'polygon': [{'x': 5.4822, 'y': 10.9534},\n", "        {'x': 6.8527, 'y': 10.9585},\n", "        {'x': 6.8527, 'y': 11.0904},\n", "        {'x': 5.4822, 'y': 11.0854}],\n", "       'span': {'offset': 733, 'length': 22},\n", "       'confidence': 0.96},\n", "      {'content': 'ABN:',\n", "       'polygon': [{'x': 0.4315, 'y': 11.1057},\n", "        {'x': 0.731, 'y': 11.1057},\n", "        {'x': 0.736, 'y': 11.2326},\n", "        {'x': 0.4315, 'y': 11.2326}],\n", "       'span': {'offset': 756, 'length': 4},\n", "       'confidence': 0.992},\n", "      {'content': '13',\n", "       'polygon': [{'x': 0.7563, 'y': 11.1057},\n", "        {'x': 0.8832, 'y': 11.1057},\n", "        {'x': 0.8832, 'y': 11.2326},\n", "        {'x': 0.7614, 'y': 11.2326}],\n", "       'span': {'offset': 761, 'length': 2},\n", "       'confidence': 0.992},\n", "      {'content': '614',\n", "       'polygon': [{'x': 0.9086, 'y': 11.1057},\n", "        {'x': 1.1167, 'y': 11.1057},\n", "        {'x': 1.1218, 'y': 11.2326},\n", "        {'x': 0.9086, 'y': 11.2326}],\n", "       'span': {'offset': 764, 'length': 3},\n", "       'confidence': 0.993},\n", "      {'content': '035',\n", "       'polygon': [{'x': 1.1523, 'y': 11.1057},\n", "        {'x': 1.3502, 'y': 11.1057},\n", "        {'x': 1.3553, 'y': 11.2326},\n", "        {'x': 1.1523, 'y': 11.2326}],\n", "       'span': {'offset': 768, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': '282',\n", "       'polygon': [{'x': 1.3909, 'y': 11.1057},\n", "        {'x': 1.5939, 'y': 11.1057},\n", "        {'x': 1.599, 'y': 11.2326},\n", "        {'x': 1.3959, 'y': 11.2326}],\n", "       'span': {'offset': 772, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': 'NSW',\n", "       'polygon': [{'x': 2.6802, 'y': 11.1057},\n", "        {'x': 2.9188, 'y': 11.1057},\n", "        {'x': 2.9238, 'y': 11.2326},\n", "        {'x': 2.6853, 'y': 11.2275}],\n", "       'span': {'offset': 776, 'length': 3},\n", "       'confidence': 0.995},\n", "      {'content': '2486',\n", "       'polygon': [{'x': 3.0101, 'y': 11.1057},\n", "        {'x': 3.2741, 'y': 11.1107},\n", "        {'x': 3.2741, 'y': 11.2326},\n", "        {'x': 3.0101, 'y': 11.2326}],\n", "       'span': {'offset': 780, 'length': 4},\n", "       'confidence': 0.993},\n", "      {'content': 'W',\n", "       'polygon': [{'x': 5.0304, 'y': 11.1057},\n", "        {'x': 5.0863, 'y': 11.1057},\n", "        {'x': 5.0863, 'y': 11.2173},\n", "        {'x': 5.0304, 'y': 11.2173}],\n", "       'span': {'offset': 785, 'length': 1},\n", "       'confidence': 0.946},\n", "      {'content': 'www.petchemist.com.au',\n", "       'polygon': [{'x': 5.4771, 'y': 11.1158},\n", "        {'x': 6.8324, 'y': 11.1057},\n", "        {'x': 6.8324, 'y': 11.2478},\n", "        {'x': 5.4771, 'y': 11.2427}],\n", "       'span': {'offset': 787, 'length': 21},\n", "       'confidence': 0.971}],\n", "     'selection_marks': [],\n", "     'spans': [{'offset': 0, 'length': 808}],\n", "     'barcodes': [],\n", "     'formulas': []}],\n", "   'paragraphs': [],\n", "   'tables': [{'row_count': 2,\n", "     'column_count': 6,\n", "     'cells': [{'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 0,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Item',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 0.1598, 'y': 3.1322},\n", "          {'x': 2.8536, 'y': 3.1322},\n", "          {'x': 2.8536, 'y': 3.3742},\n", "          {'x': 0.1598, 'y': 3.3742}]}],\n", "       'spans': [{'offset': 268, 'length': 4}]},\n", "      {'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 1,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Options',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 2.8536, 'y': 3.1322},\n", "          {'x': 4.136, 'y': 3.1322},\n", "          {'x': 4.136, 'y': 3.3742},\n", "          {'x': 2.8536, 'y': 3.3742}]}],\n", "       'spans': [{'offset': 273, 'length': 7}]},\n", "      {'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 2,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Qty',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 4.136, 'y': 3.1322},\n", "          {'x': 5.249, 'y': 3.1322},\n", "          {'x': 5.249, 'y': 3.3742},\n", "          {'x': 4.136, 'y': 3.3742}]}],\n", "       'spans': [{'offset': 281, 'length': 3}]},\n", "      {'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 3,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Unit Price',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 5.249, 'y': 3.1322},\n", "          {'x': 6.2814, 'y': 3.1322},\n", "          {'x': 6.2814, 'y': 3.3742},\n", "          {'x': 5.249, 'y': 3.3742}]}],\n", "       'spans': [{'offset': 285, 'length': 10}]},\n", "      {'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 4,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Discount',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 6.2814, 'y': 3.1322},\n", "          {'x': 7.2895, 'y': 3.1322},\n", "          {'x': 7.2895, 'y': 3.3742},\n", "          {'x': 6.2814, 'y': 3.3742}]}],\n", "       'spans': [{'offset': 296, 'length': 8}]},\n", "      {'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 5,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Subtotal',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 7.2895, 'y': 3.1322},\n", "          {'x': 8.088, 'y': 3.1322},\n", "          {'x': 8.088, 'y': 3.3742},\n", "          {'x': 7.2895, 'y': 3.3742}]}],\n", "       'spans': [{'offset': 305, 'length': 8}]},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 0,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Uricosal Potassium Citrate Mixture (200ml)',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 0.1598, 'y': 3.3742},\n", "          {'x': 2.8536, 'y': 3.3742},\n", "          {'x': 2.8536, 'y': 3.5759},\n", "          {'x': 0.1598, 'y': 3.5759}]}],\n", "       'spans': [{'offset': 314, 'length': 42}]},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 1,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': '',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 2.8536, 'y': 3.3742},\n", "          {'x': 4.136, 'y': 3.3742},\n", "          {'x': 4.1441, 'y': 3.584},\n", "          {'x': 2.8536, 'y': 3.5759}]}],\n", "       'spans': []},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 2,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': '1',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 4.136, 'y': 3.3742},\n", "          {'x': 5.249, 'y': 3.3742},\n", "          {'x': 5.249, 'y': 3.584},\n", "          {'x': 4.1441, 'y': 3.584}]}],\n", "       'spans': [{'offset': 357, 'length': 1}]},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 3,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': '$39.68',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 5.249, 'y': 3.3742},\n", "          {'x': 6.2814, 'y': 3.3742},\n", "          {'x': 6.2814, 'y': 3.584},\n", "          {'x': 5.249, 'y': 3.584}]}],\n", "       'spans': [{'offset': 359, 'length': 6}]},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 4,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': '',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 6.2814, 'y': 3.3742},\n", "          {'x': 7.2895, 'y': 3.3742},\n", "          {'x': 7.2895, 'y': 3.584},\n", "          {'x': 6.2814, 'y': 3.584}]}],\n", "       'spans': []},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 5,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': '$39.68',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 7.2895, 'y': 3.3742},\n", "          {'x': 8.088, 'y': 3.3742},\n", "          {'x': 8.088, 'y': 3.584},\n", "          {'x': 7.2895, 'y': 3.584}]}],\n", "       'spans': [{'offset': 366, 'length': 6}]}],\n", "     'bounding_regions': [{'page_number': 1,\n", "       'polygon': [{'x': 0.1918, 'y': 3.1264},\n", "        {'x': 8.0574, 'y': 3.1259},\n", "        {'x': 8.0586, 'y': 3.565},\n", "        {'x': 0.1915, 'y': 3.5655}]}],\n", "     'spans': [{'offset': 268, 'length': 104}]},\n", "    {'row_count': 2,\n", "     'column_count': 4,\n", "     'cells': [{'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 0,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Payments',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 0.401, 'y': 4.3854},\n", "          {'x': 1.5178, 'y': 4.3854},\n", "          {'x': 1.5178, 'y': 4.624},\n", "          {'x': 0.3959, 'y': 4.6291}]}],\n", "       'spans': [{'offset': 419, 'length': 8}]},\n", "      {'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 1,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Method',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 1.5178, 'y': 4.3854},\n", "          {'x': 2.7005, 'y': 4.3854},\n", "          {'x': 2.6954, 'y': 4.624},\n", "          {'x': 1.5178, 'y': 4.624}]}],\n", "       'spans': [{'offset': 428, 'length': 6}]},\n", "      {'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 2,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Ref',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 2.7005, 'y': 4.3854},\n", "          {'x': 3.3451, 'y': 4.3854},\n", "          {'x': 3.3451, 'y': 4.624},\n", "          {'x': 2.6954, 'y': 4.624}]}],\n", "       'spans': [{'offset': 435, 'length': 3}]},\n", "      {'kind': 'columnHeader',\n", "       'row_index': 0,\n", "       'column_index': 3,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Amount',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 3.3451, 'y': 4.3854},\n", "          {'x': 4.0761, 'y': 4.3854},\n", "          {'x': 4.0761, 'y': 4.624},\n", "          {'x': 3.3451, 'y': 4.624}]}],\n", "       'spans': [{'offset': 439, 'length': 6}]},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 0,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': '03 Oct 2024',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 0.3959, 'y': 4.6291},\n", "          {'x': 1.5178, 'y': 4.624},\n", "          {'x': 1.5178, 'y': 4.8473},\n", "          {'x': 0.3959, 'y': 4.8524}]}],\n", "       'spans': [{'offset': 477, 'length': 11}]},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 1,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': 'Credit Card',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 1.5178, 'y': 4.624},\n", "          {'x': 2.6954, 'y': 4.624},\n", "          {'x': 2.6954, 'y': 4.8473},\n", "          {'x': 1.5178, 'y': 4.8473}]}],\n", "       'spans': [{'offset': 489, 'length': 11}]},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 2,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': '',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 2.6954, 'y': 4.624},\n", "          {'x': 3.3451, 'y': 4.624},\n", "          {'x': 3.3451, 'y': 4.8473},\n", "          {'x': 2.6954, 'y': 4.8473}]}],\n", "       'spans': []},\n", "      {'kind': 'content',\n", "       'row_index': 1,\n", "       'column_index': 3,\n", "       'row_span': 1,\n", "       'column_span': 1,\n", "       'content': '$52.39',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 3.3451, 'y': 4.624},\n", "          {'x': 4.0761, 'y': 4.624},\n", "          {'x': 4.0761, 'y': 4.8473},\n", "          {'x': 3.3451, 'y': 4.8473}]}],\n", "       'spans': [{'offset': 501, 'length': 6}]}],\n", "     'bounding_regions': [{'page_number': 1,\n", "       'polygon': [{'x': 0.3051, 'y': 4.3737},\n", "        {'x': 4.0621, 'y': 4.374},\n", "        {'x': 4.063, 'y': 4.8517},\n", "        {'x': 0.3053, 'y': 4.8515}]}],\n", "     'spans': [{'offset': 419, 'length': 26}, {'offset': 477, 'length': 30}]}],\n", "   'key_value_pairs': [],\n", "   'styles': [],\n", "   'documents': [{'doc_type': 'invoice',\n", "     'bounding_regions': [{'page_number': 1,\n", "       'polygon': [{'x': 0.0, 'y': 0.0},\n", "        {'x': 8.2639, 'y': 0.0},\n", "        {'x': 8.2639, 'y': 11.6944},\n", "        {'x': 0.0, 'y': 11.6944}]}],\n", "     'spans': [{'offset': 0, 'length': 808}],\n", "     'fields': {'AmountDue': {'value_type': 'currency',\n", "       'value': {'amount': 0.0, 'symbol': '$', 'code': 'AUD'},\n", "       'content': '$0.00',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 7.5431, 'y': 6.3142},\n", "          {'x': 7.8578, 'y': 6.3142},\n", "          {'x': 7.8578, 'y': 6.4462},\n", "          {'x': 7.5431, 'y': 6.4462}]}],\n", "       'spans': [{'offset': 623, 'length': 5}],\n", "       'confidence': 0.416},\n", "      'CustomerAddress': {'value_type': 'address',\n", "       'value': {'house_number': None,\n", "        'po_box': 'PO Box 6026',\n", "        'road': None,\n", "        'city': None,\n", "        'state': 'Queensland',\n", "        'postal_code': '4179',\n", "        'country_region': 'Australia',\n", "        'street_address': 'PO Box 6026',\n", "        'unit': None,\n", "        'city_district': None,\n", "        'state_district': None,\n", "        'suburb': 'Manly',\n", "        'house': None,\n", "        'level': None},\n", "       'content': 'PO Box 6026\\nManly, Queensland 4179\\nAustralia',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 0.335, 'y': 1.9592},\n", "          {'x': 1.7005, 'y': 1.9592},\n", "          {'x': 1.7005, 'y': 2.3907},\n", "          {'x': 0.335, 'y': 2.3907}]}],\n", "       'spans': [{'offset': 139, 'length': 11},\n", "        {'offset': 163, 'length': 22},\n", "        {'offset': 209, 'length': 9}],\n", "       'confidence': 0.887},\n", "      'CustomerName': {'value_type': 'string',\n", "       'value': '<PERSON>',\n", "       'content': '<PERSON>',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 0.335, 'y': 1.812},\n", "          {'x': 1.0964, 'y': 1.812},\n", "          {'x': 1.0964, 'y': 1.944},\n", "          {'x': 0.335, 'y': 1.944}]}],\n", "       'spans': [{'offset': 113, 'length': 12}],\n", "       'confidence': 0.811},\n", "      'InvoiceDate': {'value_type': 'date',\n", "       'value': datetime.date(2024, 10, 3),\n", "       'content': '03 Oct 2024',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 4.2132, 'y': 1.1979},\n", "          {'x': 4.8781, 'y': 1.1979},\n", "          {'x': 4.8781, 'y': 1.3248},\n", "          {'x': 4.2132, 'y': 1.3248}]}],\n", "       'spans': [{'offset': 82, 'length': 11}],\n", "       'confidence': 0.923},\n", "      'InvoiceId': {'value_type': 'string',\n", "       'value': '838901',\n", "       'content': '838901',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 2.2589, 'y': 1.208},\n", "          {'x': 2.67, 'y': 1.2029},\n", "          {'x': 2.67, 'y': 1.3197},\n", "          {'x': 2.2589, 'y': 1.3197}]}],\n", "       'spans': [{'offset': 75, 'length': 6}],\n", "       'confidence': 0.956},\n", "      'InvoiceTotal': {'value_type': 'currency',\n", "       'value': {'amount': 52.39, 'symbol': '$', 'code': 'AUD'},\n", "       'content': '$52.39',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 7.472, 'y': 5.8218},\n", "          {'x': 7.8578, 'y': 5.832},\n", "          {'x': 7.8578, 'y': 5.9487},\n", "          {'x': 7.4771, 'y': 5.9487}]}],\n", "       'spans': [{'offset': 584, 'length': 6}],\n", "       'confidence': 0.437},\n", "      'Items': {'value_type': 'list',\n", "       'value': [{'value_type': 'dictionary',\n", "         'value': {'Amount': {'value_type': 'currency',\n", "           'value': {'amount': 39.68, 'symbol': '$', 'code': 'AUD'},\n", "           'content': '$39.68',\n", "           'bounding_regions': [{'page_number': 1,\n", "             'polygon': [{'x': 7.5735, 'y': 3.421},\n", "              {'x': 7.9593, 'y': 3.416},\n", "              {'x': 7.9593, 'y': 3.5428},\n", "              {'x': 7.5786, 'y': 3.5378}]}],\n", "           'spans': [{'offset': 366, 'length': 6}],\n", "           'confidence': 0.916},\n", "          'Date': {'value_type': 'date',\n", "           'value': None,\n", "           'content': '03',\n", "           'bounding_regions': [{'page_number': 1,\n", "             'polygon': [{'x': 0.6142, 'y': 4.6747},\n", "              {'x': 0.7614, 'y': 4.6747},\n", "              {'x': 0.7614, 'y': 4.7965},\n", "              {'x': 0.6193, 'y': 4.7965}]}],\n", "           'spans': [{'offset': 477, 'length': 2}],\n", "           'confidence': 0.247},\n", "          'Description': {'value_type': 'string',\n", "           'value': 'Uricosal Potassium Citrate Mixture (200ml)',\n", "           'content': 'Uricosal Potassium Citrate Mixture (200ml)',\n", "           'bounding_regions': [{'page_number': 1,\n", "             'polygon': [{'x': 0.2893, 'y': 3.4109},\n", "              {'x': 2.6446, 'y': 3.4109},\n", "              {'x': 2.6446, 'y': 3.5632},\n", "              {'x': 0.2893, 'y': 3.5632}]}],\n", "           'spans': [{'offset': 314, 'length': 42}],\n", "           'confidence': 0.916},\n", "          'Quantity': {'value_type': 'float',\n", "           'value': 1.0,\n", "           'content': '1',\n", "           'bounding_regions': [{'page_number': 1,\n", "             'polygon': [{'x': 4.8629, 'y': 3.416},\n", "              {'x': 4.9187, 'y': 3.416},\n", "              {'x': 4.9238, 'y': 3.5276},\n", "              {'x': 4.868, 'y': 3.5327}]}],\n", "           'spans': [{'offset': 357, 'length': 1}],\n", "           'confidence': 0.917},\n", "          'UnitPrice': {'value_type': 'currency',\n", "           'value': {'amount': 39.68, 'symbol': '$', 'code': 'AUD'},\n", "           'content': '$39.68',\n", "           'bounding_regions': [{'page_number': 1,\n", "             'polygon': [{'x': 5.5431, 'y': 3.416},\n", "              {'x': 5.9441, 'y': 3.416},\n", "              {'x': 5.9441, 'y': 3.5428},\n", "              {'x': 5.5482, 'y': 3.5428}]}],\n", "           'spans': [{'offset': 359, 'length': 6}],\n", "           'confidence': 0.917}},\n", "         'content': 'Uricosal Potassium Citrate Mixture (200ml)\\n1\\n$39.68\\n$39.68\\nPayments\\nProduct Cost:\\n$39.68\\nAusPost eParcel\\nPayments\\nMethod\\nRef\\nAmount\\n03 Oct 2024\\nCredit Card\\n$52.39',\n", "         'bounding_regions': [{'page_number': 1,\n", "           'polygon': [{'x': 0.2893, 'y': 3.4109},\n", "            {'x': 7.9593, 'y': 3.4109},\n", "            {'x': 7.9593, 'y': 4.8016},\n", "            {'x': 0.2893, 'y': 4.8016}]}],\n", "         'spans': [{'offset': 314, 'length': 131},\n", "          {'offset': 477, 'length': 30}],\n", "         'confidence': 0.615}],\n", "       'content': None,\n", "       'bounding_regions': [],\n", "       'spans': [],\n", "       'confidence': None},\n", "      'ShippingAddress': {'value_type': 'address',\n", "       'value': {'house_number': None,\n", "        'po_box': 'PO Box 6026',\n", "        'road': None,\n", "        'city': None,\n", "        'state': 'Queensland',\n", "        'postal_code': '4179',\n", "        'country_region': 'Australia',\n", "        'street_address': 'PO Box 6026',\n", "        'unit': None,\n", "        'city_district': None,\n", "        'state_district': None,\n", "        'suburb': 'Manly',\n", "        'house': None,\n", "        'level': None},\n", "       'content': 'PO Box 6026\\nManly, Queensland 4179\\nAustralia',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 4.2284, 'y': 1.9592},\n", "          {'x': 5.5939, 'y': 1.9592},\n", "          {'x': 5.5939, 'y': 2.3856},\n", "          {'x': 4.2284, 'y': 2.3856}]}],\n", "       'spans': [{'offset': 151, 'length': 11},\n", "        {'offset': 186, 'length': 22},\n", "        {'offset': 219, 'length': 9}],\n", "       'confidence': 0.887},\n", "      'ShippingAddressRecipient': {'value_type': 'string',\n", "       'value': '<PERSON>',\n", "       'content': '<PERSON>',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 4.2336, 'y': 1.8041},\n", "          {'x': 4.9807, 'y': 1.8095},\n", "          {'x': 4.9797, 'y': 1.9467},\n", "          {'x': 4.2326, 'y': 1.9413}]}],\n", "       'spans': [{'offset': 126, 'length': 12}],\n", "       'confidence': 0.918},\n", "      'SubTotal': {'value_type': 'currency',\n", "       'value': {'amount': 47.63, 'symbol': '$', 'code': 'AUD'},\n", "       'content': '$47.63',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 7.4771, 'y': 5.0808},\n", "          {'x': 7.8578, 'y': 5.0757},\n", "          {'x': 7.8578, 'y': 5.2077},\n", "          {'x': 7.4771, 'y': 5.2077}]}],\n", "       'spans': [{'offset': 541, 'length': 6}],\n", "       'confidence': 0.95},\n", "      'TotalDiscount': {'value_type': 'currency',\n", "       'value': {'amount': 0.0, 'symbol': '$', 'code': 'AUD'},\n", "       'content': '$0.00',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 7.5532, 'y': 4.7813},\n", "          {'x': 7.8578, 'y': 4.7813},\n", "          {'x': 7.8578, 'y': 4.964},\n", "          {'x': 7.5532, 'y': 4.964}]}],\n", "       'spans': [{'offset': 524, 'length': 5}],\n", "       'confidence': 0.647},\n", "      'TotalTax': {'value_type': 'currency',\n", "       'value': {'amount': 4.76, 'symbol': '$', 'code': 'AUD'},\n", "       'content': '$4.76',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 7.5431, 'y': 5.3193},\n", "          {'x': 7.8629, 'y': 5.3193},\n", "          {'x': 7.8629, 'y': 5.4462},\n", "          {'x': 7.5431, 'y': 5.4462}]}],\n", "       'spans': [{'offset': 559, 'length': 5}],\n", "       'confidence': 0.95},\n", "      'VendorAddress': {'value_type': 'address',\n", "       'value': {'house_number': '29-31',\n", "        'po_box': None,\n", "        'road': 'Corporation Circuit',\n", "        'city': None,\n", "        'state': 'NSW',\n", "        'postal_code': '2486',\n", "        'country_region': None,\n", "        'street_address': '29-31 Corporation Circuit',\n", "        'unit': None,\n", "        'city_district': None,\n", "        'state_district': None,\n", "        'suburb': 'Tweed Heads South',\n", "        'house': None,\n", "        'level': None},\n", "       'content': '29-31 Corporation Circuit\\nTweed Heads South\\nNSW 2486',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 2.6824, 'y': 10.7865},\n", "          {'x': 4.0822, 'y': 10.796},\n", "          {'x': 4.0792, 'y': 11.2404},\n", "          {'x': 2.6793, 'y': 11.2309}]}],\n", "       'spans': [{'offset': 648, 'length': 25},\n", "        {'offset': 713, 'length': 17},\n", "        {'offset': 776, 'length': 8}],\n", "       'confidence': 0.886},\n", "      'VendorAddressRecipient': {'value_type': 'string',\n", "       'value': 'Pet Chemist Online\\nAussie Pet Meds Pty Ltd',\n", "       'content': 'Pet Chemist Online\\nAussie Pet Meds Pty Ltd',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 0.4264, 'y': 10.8011},\n", "          {'x': 1.7462, 'y': 10.8011},\n", "          {'x': 1.7462, 'y': 11.0955},\n", "          {'x': 0.4264, 'y': 11.0955}]}],\n", "       'spans': [{'offset': 629, 'length': 18}, {'offset': 689, 'length': 23}],\n", "       'confidence': 0.717},\n", "      'VendorName': {'value_type': 'string',\n", "       'value': 'PET CHEMIST',\n", "       'content': 'PET CHEMIST',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 0.9386, 'y': 0.4021},\n", "          {'x': 2.8426, 'y': 0.3983},\n", "          {'x': 2.8431, 'y': 0.6725},\n", "          {'x': 0.9391, 'y': 0.6763}]}],\n", "       'spans': [{'offset': 0, 'length': 11}],\n", "       'confidence': 0.938},\n", "      'VendorTaxId': {'value_type': 'string',\n", "       'value': '**************',\n", "       'content': '**************',\n", "       'bounding_regions': [{'page_number': 1,\n", "         'polygon': [{'x': 0.7563, 'y': 11.1057},\n", "          {'x': 1.599, 'y': 11.1057},\n", "          {'x': 1.599, 'y': 11.2326},\n", "          {'x': 0.7563, 'y': 11.2326}]}],\n", "       'spans': [{'offset': 761, 'length': 14}],\n", "       'confidence': 0.68}},\n", "     'confidence': 1.0}]}}]"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["document_intelligence_parsed_res['0058d496-8395-402f-8d2d-2d4a63201dfe.pdf']"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['../../data/samples/nz_csp_samples_DI/NZC0022108.pdf', 'NZC0022109.pdf', 'NZC0022110.pdf', 'NZC0022111.pdf', 'NZC0022113.pdf', 'NZC0022115.pdf', 'NZC0022116.pdf', 'NZC0022117.pdf', 'NZC0022118.pdf', 'NZC0022120.pdf', 'NZC0022121.pdf', 'NZC0022125.pdf', 'NZC0022128.pdf', 'NZC0022131.pdf', 'NZC0022133.pdf', 'NZC0022136.pdf', 'NZC0022138.pdf', 'NZC0022140.pdf', 'NZC0022145.pdf', 'NZC0022146.pdf', 'NZC0022150.pdf', 'NZC0022151.pdf', 'NZC0022152.pdf', 'NZC0022153.pdf', 'NZC0022154.pdf', 'NZC0022156.pdf', 'NZC0022160.pdf', 'NZC0022162.pdf', 'NZC0022164.pdf', 'NZC0022168.pdf', 'NZC0022170.pdf', 'NZC0022171.pdf', 'NZC0022176.pdf', 'NZC0022177.pdf', 'NZC0022178.pdf', 'NZC0022179.pdf', 'NZC0022185.pdf', 'NZC0022187.pdf', 'NZC0022188.pdf', 'NZC0022189.pdf', 'NZC0022190.pdf', 'NZC0022192.pdf', 'NZC0022195.pdf', 'NZC0022196.pdf', 'NZC0022198.pdf', 'NZC0022200.pdf', 'NZC0022202.pdf', 'NZC0022211.pdf', 'NZC0022212.pdf', 'NZC0022213.pdf', 'NZC0022214.pdf', 'NZC0022215.pdf', 'NZC0022217.pdf', 'NZC0022218.pdf', 'NZC0022221.pdf', 'NZC0022234.pdf', 'NZC0022235.pdf', 'NZC0022237.pdf', 'NZC0022238.pdf', 'NZC0022239.pdf', 'NZC0022243.pdf', 'NZC0022244.pdf', 'NZC0022247.pdf', 'NZC0022248.pdf', 'NZC0022253.pdf', 'NZC0022254.pdf', 'NZC0022255.pdf', 'NZC0022256.pdf', 'NZC0022257.pdf', 'NZC0022258.pdf', 'NZC0022259.pdf', 'NZC0022261.pdf', 'NZC0022265.pdf', 'NZC0022268.pdf', 'NZC0022269.pdf', 'NZC0022273.pdf', 'NZC0022278.pdf', 'NZC0022279.pdf', 'NZC0022280.pdf', 'NZC0022282.pdf', 'NZC0022283.pdf', 'NZC0022284.pdf', 'NZC0022290.pdf', 'NZC0022292.pdf', 'NZC0022293.pdf', 'NZC0022294.pdf', 'NZC0022296.pdf', 'NZC0022297.pdf', 'NZC0022300.pdf', 'NZC0022301.pdf', 'NZC0022303.pdf', 'NZC0022306.pdf', 'NZC0022307.pdf', 'NZC0022312.pdf', 'NZC0022315.pdf', 'NZC0022318.pdf', 'NZC0022319.pdf', 'NZC0022320.pdf', 'NZC0022321.pdf', 'NZC0022326.pdf', 'NZC0022327.pdf', 'NZC0022329.pdf', 'NZC0022332.pdf', 'NZC0022333.pdf', 'NZC0022334.pdf', 'NZC0022335.pdf', 'NZC0022336.pdf', 'NZC0022337.pdf', 'NZC0022338.pdf', 'NZC0022339.pdf', 'NZC0022341.pdf', 'NZC0022342.pdf', 'NZC0022343.pdf', 'NZC0022345.pdf', 'NZC0022347.pdf', 'NZC0022348.pdf', 'NZC0022349.pdf', 'NZC0022350.pdf', 'NZC0022351.pdf', 'NZC0022352.pdf', 'NZC0022353.pdf', 'NZC0022354.pdf', 'NZC0022355.pdf', 'NZC0022357.pdf', 'NZC0022359.pdf', 'NZC0022360.pdf', 'NZC0022361.pdf', 'NZC0022363.pdf', 'NZC0022365.pdf', 'NZC0022368.pdf', 'NZC0022370.pdf', 'NZC0022376.pdf', 'NZC0022378.pdf', 'NZC0022380.pdf', 'NZC0022382.pdf', 'NZC0022385.pdf', 'NZC0022386.pdf', 'NZC0022387.pdf', 'NZC0022388.pdf', 'NZC0022389.pdf', 'NZC0022391.pdf', 'NZC0022393.pdf', 'NZC0022394.pdf', 'NZC0022395.pdf', 'NZC0022398.pdf', 'NZC0022400.pdf', 'NZC0022405.pdf', 'NZC0022406.pdf', 'NZC0022407.pdf', 'NZC0022408.pdf', 'NZC0022409.pdf', 'NZC0022414.pdf', 'NZC0022417.pdf', 'NZC0022418.pdf', 'NZC0022420.pdf', 'NZC0022421.pdf', 'NZC0022423.pdf', 'NZC0022424.pdf', 'NZC0022431.pdf', 'NZC0022432.pdf', 'NZC0022433.pdf', 'NZC0022434.pdf', 'NZC0022437.pdf', 'NZC0022438.pdf', 'NZC0022439.pdf', 'NZC0022442.pdf', 'NZC0022443.pdf', 'NZC0022446.pdf', 'NZC0022447.pdf', 'NZC0022451.pdf', 'NZC0022456.pdf', 'NZC0022459.pdf', 'NZC0022460.pdf', 'NZC0022463.pdf', 'NZC0022465.pdf', 'NZC0022466.pdf', 'NZC0022467.pdf', 'NZC0022472.pdf', 'NZC0022474.pdf', 'NZC0022476.pdf', 'NZC0022477.pdf', 'NZC0022481.pdf', 'NZC0022482.pdf', 'NZC0022483.pdf', 'NZC0022489.pdf', 'NZC0022491.pdf', 'NZC0022492.pdf', 'NZC0022493.pdf', 'NZC0022495.pdf', 'NZC0022496.pdf', 'NZC0022497.pdf', 'NZC0022499.pdf', 'NZC0022501.pdf', 'NZC0022504.pdf', 'NZC0022505.pdf', 'NZC0022506.pdf', 'NZC0022507.pdf', 'NZC0022509.pdf', 'NZC0022511.pdf', 'NZC0022513.pdf', 'NZC0022514.pdf', 'NZC0022515.pdf', 'NZC0022516.pdf', 'NZC0022517.pdf', 'NZC0022518.pdf', 'NZC0022519.pdf', 'NZC0022520.pdf', 'NZC0022522.pdf', 'NZC0022524.pdf', 'NZC0022527.pdf', 'NZC0022528.pdf', 'NZC0022529.pdf', 'NZC0022530.pdf', 'NZC0022533.pdf', 'NZC0022534.pdf', 'NZC0022539.pdf', 'NZC0022541.pdf', 'NZC0022542.pdf', 'NZC0022545.pdf', 'NZC0022547.pdf', 'NZC0022548.pdf', 'NZC0022550.pdf', 'NZC0022553.pdf', 'NZC0022557.pdf', 'NZC0022559.pdf', 'NZC0022562.pdf', 'NZC0022563.pdf', 'NZC0022567.pdf', 'NZC0022569.pdf', 'NZC0022572.pdf', 'NZC0022575.pdf', 'NZC0022578.pdf', 'NZC0022581.pdf', 'NZC0022583.pdf', 'NZC0022584.pdf', 'NZC0022588.pdf', 'NZC0022589.pdf', 'NZC0022590.pdf', 'NZC0022591.pdf', 'NZC0022592.pdf', 'NZC0022593.pdf', 'NZC0022598.pdf', 'NZC0022599.pdf', 'NZC0022606.pdf', 'NZC0022608.pdf', 'NZC0022609.pdf', 'NZC0022610.pdf', 'NZC0022611.pdf', 'NZC0022612.pdf', 'NZC0022613.pdf', 'NZC0022614.pdf', 'NZC0022621.pdf', 'NZC0022623.pdf', 'NZC0022629.pdf', 'NZC0022630.pdf', 'NZC0022631.pdf', 'NZC0022632.pdf', 'NZC0022634.pdf', 'NZC0022635.pdf', 'NZC0022636.pdf', 'NZC0022640.pdf', 'NZC0022641.pdf', 'NZC0022644.pdf', 'NZC0022645.pdf', 'NZC0022646.pdf', 'NZC0022648.pdf', 'NZC0022650.pdf', 'NZC0022651.pdf', 'NZC0022652.pdf', 'NZC0022654.pdf', 'NZC0022656.pdf', 'NZC0022657.pdf', 'NZC0022658.pdf', 'NZC0022659.pdf', 'NZC0022661.pdf', 'NZC0022662.pdf', 'NZC0022663.pdf', 'NZC0022664.pdf', 'NZC0022666.pdf', 'NZC0022667.pdf', 'NZC0022668.pdf', 'NZC0022669.pdf', 'NZC0022671.pdf', 'NZC0022672.pdf', 'NZC0022673.pdf', 'NZC0022676.pdf', 'NZC0022677.pdf', 'NZC0022679.pdf', 'NZC0022683.pdf', 'NZC0022685.pdf', 'NZC0022687.pdf', 'NZC0022689.pdf', 'NZC0022692.pdf', 'NZC0022693.pdf', 'NZC0022694.pdf', 'NZC0022695.pdf', 'NZC0022697.pdf', 'NZC0022701.pdf', 'NZC0022705.pdf', 'NZC0022707.pdf', 'NZC0022708.pdf', 'NZC0022711.pdf', 'NZC0022712.pdf', 'NZC0022714.pdf', 'NZC0022719.pdf', 'NZC0022720.pdf', 'NZC0022723.pdf', 'NZC0022725.pdf', 'NZC0022728.pdf', 'NZC0022732.pdf', 'NZC0022736.pdf', 'NZC0022741.pdf', 'NZC0022743.pdf', 'NZC0022745.pdf', 'NZC0022751.pdf', 'NZC0022753.pdf', 'NZC0022754.pdf', 'NZC0022758.pdf', 'NZC0022759.pdf', 'NZC0022760.pdf', 'NZC0022762.pdf', 'NZC0022766.pdf', 'NZC0022767.pdf', 'NZC0022768.pdf', 'NZC0022775.pdf', 'NZC0022778.pdf', 'NZC0022782.pdf', 'NZC0022784.pdf', 'NZC0022786.pdf', 'NZC0022790.pdf', 'NZC0022791.pdf', 'NZC0022792.pdf', 'NZC0022794.pdf', 'NZC0022797.pdf', 'NZC0022800.pdf', 'NZC0022801.pdf', 'NZC0022804.pdf', 'NZC0022805.pdf', 'NZC0022806.pdf', 'NZC0022807.pdf', 'NZC0022808.pdf', 'NZC0022810.pdf', 'NZC0022812.pdf', 'NZC0022815.pdf', 'NZC0022817.pdf', 'NZC0022818.pdf', 'NZC0022821.pdf', 'NZC0022823.pdf', 'NZC0022830.pdf', 'NZC0022833.pdf', 'NZC0022835.pdf', 'NZC0022840.pdf', 'NZC0022841.pdf', 'NZC0022843.pdf', 'NZC0022847.pdf', 'NZC0022853.pdf', 'NZC0022857.pdf', 'NZC0022859.pdf', 'NZC0022860.pdf', 'NZC0022861.pdf', 'NZC0022863.pdf', 'NZC0022864.pdf', 'NZC0022872.pdf', 'NZC0022873.pdf', 'NZC0022874.pdf', 'NZC0022879.pdf', 'NZC0022880.pdf', 'NZC0022881.pdf', 'NZC0022882.pdf', 'NZC0022883.pdf', 'NZC0022884.pdf', 'NZC0022885.pdf', 'NZC0022886.pdf', 'NZC0022889.pdf', 'NZC0022891.pdf', 'NZC0022892.pdf', 'NZC0022894.pdf', 'NZC0022897.pdf', 'NZC0022898.pdf', 'NZC0022901.pdf', 'NZC0022904.pdf', 'NZC0022905.pdf', 'NZC0022906.pdf', 'NZC0022909.pdf', 'NZC0022911.pdf', 'NZC0022913.pdf', 'NZC0022914.pdf', 'NZC0022915.pdf', 'NZC0022916.pdf', 'NZC0022917.pdf', 'NZC0022918.pdf', 'NZC0022919.pdf', 'NZC0022920.pdf', 'NZC0022921.pdf', 'NZC0022922.pdf', 'NZC0022926.pdf', 'NZC0022928.pdf', 'NZC0022932.pdf', 'NZC0022942.pdf', 'NZC0022944.pdf', 'NZC0022945.pdf', 'NZC0022947.pdf', 'NZC0022948.pdf', 'NZC0022950.pdf', 'NZC0022951.pdf', 'NZC0022952.pdf', 'NZC0022953.pdf', 'NZC0022955.pdf', 'NZC0022956.pdf', 'NZC0022957.pdf', 'NZC0022959.pdf', 'NZC0022960.pdf', 'NZC0022961.pdf', 'NZC0022964.pdf', 'NZC0022965.pdf', 'NZC0022966.pdf', 'NZC0022967.pdf', 'NZC0022970.pdf', 'NZC0022971.pdf', 'NZC0022972.pdf', 'NZC0022974.pdf', 'NZC0022978.pdf', 'NZC0022979.pdf', 'NZC0022981.pdf', 'NZC0022991.pdf', 'NZC0022993.pdf', 'NZC0022994.pdf', 'NZC0022996.pdf', 'NZC0022999.pdf', 'NZC0023002.pdf', 'NZC0023003.pdf', 'NZC0023012.pdf', 'NZC0023013.pdf', 'NZC0023014.pdf', 'NZC0023015.pdf', 'NZC0023016.pdf', 'NZC0023019.pdf', 'NZC0023023.pdf', 'NZC0023024.pdf', 'NZC0023026.pdf', 'NZC0023027.pdf', 'NZC0023028.pdf', 'NZC0023029.pdf', 'NZC0023030.pdf', 'NZC0023031.pdf', 'NZC0023037.pdf', 'NZC0023039.pdf', 'NZC0023040.pdf', 'NZC0023042.pdf', 'NZC0023044.pdf', 'NZC0023045.pdf', 'NZC0023046.pdf', 'NZC0023048.pdf', 'NZC0023049.pdf', 'NZC0023051.pdf', 'NZC0023052.pdf', 'NZC0023054.pdf', 'NZC0023057.pdf', 'NZC0023059.pdf', 'NZC0023060.pdf', 'NZC0023061.pdf', 'NZC0023062.pdf', 'NZC0023063.pdf', 'NZC0023065.pdf', 'NZC0023066.pdf', 'NZC0023067.pdf', 'NZC0023071.pdf', 'NZC0023072.pdf', 'NZC0023073.pdf', 'NZC0023074.pdf', 'NZC0023075.pdf', 'NZC0023076.pdf', 'NZC0023077.pdf', 'NZC0023079.pdf', 'NZC0023081.pdf', 'NZC0023082.pdf', 'NZC0023083.pdf', 'NZC0023084.pdf', 'NZC0023085.pdf', 'NZC0023091.pdf', 'NZC0023092.pdf', 'NZC0023093.pdf', 'NZC0023094.pdf', 'NZC0023095.pdf', 'NZC0023096.pdf', 'NZC0023100.pdf', 'NZC0023104.pdf', 'NZC0023105.pdf', 'NZC0023109.pdf', 'NZC0023110.pdf', 'NZC0023111.pdf', 'NZC0023113.pdf', 'NZC0023116.pdf', 'NZC0023117.pdf', 'NZC0023120.pdf', 'NZC0023124.pdf', 'NZC0023125.pdf', 'NZC0023126.pdf', 'NZC0023127.pdf', 'NZC0023129.pdf', 'NZC0023131.pdf', 'NZC0023136.pdf', 'NZC0023138.pdf', 'NZC0023139.pdf', 'NZC0023140.pdf', 'NZC0023143.pdf', 'NZC0023144.pdf', 'NZC0023146.pdf', 'NZC0023149.pdf', 'NZC0023152.pdf', 'NZC0023153.pdf', 'NZC0023158.pdf', 'NZC0023160.pdf', 'NZC0023161.pdf', 'NZC0023162.pdf', 'NZC0023164.pdf', 'NZC0023169.pdf', 'NZC0023170.pdf', 'NZC0023171.pdf', 'NZC0023173.pdf', 'NZC0023174.pdf', 'NZC0023176.pdf', 'NZC0023178.pdf', 'NZC0023179.pdf', 'NZC0023183.pdf', 'NZC0023186.pdf', 'NZC0023188.pdf', 'NZC0023189.pdf', 'NZC0023191.pdf', 'NZC0023192.pdf', 'NZC0023193.pdf', 'NZC0023194.pdf', 'NZC0023199.pdf', 'NZC0023200.pdf', 'NZC0023201.pdf', 'NZC0023202.pdf', 'NZC0023203.pdf', 'NZC0023204.pdf', 'NZC0023205.pdf', 'NZC0023207.pdf', 'NZC0023208.pdf', 'NZC0023210.pdf', 'NZC0023211.pdf', 'NZC0023213.pdf', 'NZC0023214.pdf', 'NZC0023215.pdf', 'NZC0023216.pdf', 'NZC0023218.pdf', 'NZC0023219.pdf', 'NZC0023220.pdf', 'NZC0023221.pdf', 'NZC0023222.pdf', 'NZC0023224.pdf', 'NZC0023225.pdf', 'NZC0023231.pdf', 'NZC0023232.pdf', 'NZC0023233.pdf', 'NZC0023235.pdf', 'NZC0023236.pdf', 'NZC0023238.pdf', 'NZC0023239.pdf', 'NZC0023240.pdf', 'NZC0023242.pdf', 'NZC0023243.pdf', 'NZC0023244.pdf', 'NZC0023245.pdf', 'NZC0023246.pdf', 'NZC0023248.pdf', 'NZC0023249.pdf', 'NZC0023250.pdf', 'NZC0023251.pdf', 'NZC0023252.pdf', 'NZC0023254.pdf', 'NZC0023256.pdf', 'NZC0023261.pdf', 'NZC0023265.pdf', 'NZC0023271.pdf', 'NZC0023274.pdf', 'NZC0023276.pdf', 'NZC0023278.pdf', 'NZC0023280.pdf', 'NZC0023281.pdf', 'NZC0023282.pdf', 'NZC0023283.pdf', 'NZC0023284.pdf', 'NZC0023285.pdf', 'NZC0023287.pdf', 'NZC0023290.pdf', 'NZC0023291.pdf', 'NZC0023293.pdf', 'NZC0023294.pdf', 'NZC0023298.pdf', 'NZC0023301.pdf', 'NZC0023302.pdf', 'NZC0023304.pdf', 'NZC0023305.pdf', 'NZC0023309.pdf', 'NZC0023313.pdf', 'NZC0023314.pdf', 'NZC0023316.pdf', 'NZC0023318.pdf', 'NZC0023319.pdf', 'NZC0023322.pdf', 'NZC0023323.pdf', 'NZC0023325.pdf', 'NZC0023326.pdf', 'NZC0023327.pdf', 'NZC0023328.pdf', 'NZC0023329.pdf', 'NZC0023330.pdf', 'NZC0023331.pdf', 'NZC0023333.pdf', 'NZC0023334.pdf', 'NZC0023336.pdf', 'NZC0023337.pdf', 'NZC0023338.pdf', 'NZC0023339.pdf', 'NZC0023340.pdf', 'NZC0023341.pdf', 'NZC0023342.pdf', 'NZC0023346.pdf', 'NZC0023348.pdf', 'NZC0023349.pdf', 'NZC0023353.pdf', 'NZC0023356.pdf', 'NZC0023361.pdf', 'NZC0023362.pdf', 'NZC0023363.pdf', 'NZC0023364.pdf', 'NZC0023365.pdf', 'NZC0023367.pdf', 'NZC0023368.pdf', 'NZC0023373.pdf', 'NZC0023375.pdf', 'NZC0023377.pdf', 'NZC0023378.pdf', 'NZC0023380.pdf', 'NZC0023381.pdf', 'NZC0023383.pdf', 'NZC0023384.pdf', 'NZC0023387.pdf', 'NZC0023388.pdf', 'NZC0023389.pdf', 'NZC0023393.pdf', 'NZC0023394.pdf', 'NZC0023395.pdf', 'NZC0023396.pdf', 'NZC0023399.pdf', 'NZC0023400.pdf', 'NZC0023401.pdf', 'NZC0023404.pdf', 'NZC0023405.pdf', 'NZC0023406.pdf', 'NZC0023407.pdf', 'NZC0023408.pdf', 'NZC0023409.pdf', 'NZC0023411.pdf', 'NZC0023415.pdf', 'NZC0023418.pdf', 'NZC0023419.pdf', 'NZC0023421.pdf', 'NZC0023422.pdf', 'NZC0023425.pdf', 'NZC0023426.pdf', 'NZC0023427.pdf', 'NZC0023429.pdf', 'NZC0023432.pdf', 'NZC0023434.pdf', 'NZC0023435.pdf', 'NZC0023437.pdf', 'NZC0023438.pdf', 'NZC0023441.pdf', 'NZC0023442.pdf', 'NZC0023444.pdf', 'NZC0023445.pdf', 'NZC0023446.pdf', 'NZC0023447.pdf', 'NZC0023450.pdf', 'NZC0023451.pdf', 'NZC0023452.pdf', 'NZC0023454.pdf', 'NZC0023455.pdf', 'NZC0023456.pdf', 'NZC0023457.pdf', 'NZC0023460.pdf', 'NZC0023461.pdf', 'NZC0023462.pdf', 'NZC0023464.pdf', 'NZC0023466.pdf', 'NZC0023468.pdf', 'NZC0023469.pdf', 'NZC0023470.pdf', 'NZC0023472.pdf', 'NZC0023475.pdf', 'NZC0023476.pdf', 'NZC0023478.pdf', 'NZC0023479.pdf', 'NZC0023481.pdf', 'NZC0023483.pdf', 'NZC0023484.pdf', 'NZC0023485.pdf', 'NZC0023495.pdf', 'NZC0023500.pdf', 'NZC0023501.pdf', 'NZC0023502.pdf', 'NZC0023505.pdf', 'NZC0023509.pdf', 'NZC0023510.pdf', 'NZC0023512.pdf', 'NZC0023513.pdf', 'NZC0023514.pdf', 'NZC0023515.pdf', 'NZC0023516.pdf', 'NZC0023520.pdf', 'NZC0023521.pdf', 'NZC0023522.pdf', 'NZC0023523.pdf', 'NZC0023525.pdf', 'NZC0023526.pdf', 'NZC0023527.pdf', 'NZC0023529.pdf', 'NZC0023531.pdf', 'NZC0023532.pdf', 'NZC0023533.pdf', 'NZC0023536.pdf', 'NZC0023537.pdf', 'NZC0023541.pdf', 'NZC0023542.pdf', 'NZC0023543.pdf', 'NZC0023544.pdf', 'NZC0023548.pdf', 'NZC0023549.pdf', 'NZC0023550.pdf', 'NZC0023551.pdf', 'NZC0023552.pdf', 'NZC0023553.pdf', 'NZC0023561.pdf', 'NZC0023562.pdf', 'NZC0023564.pdf', 'NZC0023567.pdf', 'NZC0023568.pdf', 'NZC0023570.pdf', 'NZC0023571.pdf', 'NZC0023573.pdf', 'NZC0023574.pdf', 'NZC0023575.pdf', 'NZC0023576.pdf', 'NZC0023579.pdf', 'NZC0023581.pdf', 'NZC0023582.pdf', 'NZC0023583.pdf', 'NZC0023584.pdf', 'NZC0023585.pdf', 'NZC0023586.pdf', 'NZC0023588.pdf', 'NZC0023590.pdf', 'NZC0023595.pdf', 'NZC0023597.pdf', 'NZC0023598.pdf', 'NZC0023599.pdf', 'NZC0023600.pdf', 'NZC0023601.pdf', 'NZC0023604.pdf', 'NZC0023606.pdf', 'NZC0023607.pdf', 'NZC0023610.pdf', 'NZC0023611.pdf', 'NZC0023612.pdf', 'NZC0023613.pdf', 'NZC0023615.pdf', 'NZC0023618.pdf', 'NZC0023620.pdf', 'NZC0023622.pdf', 'NZC0023623.pdf', 'NZC0023624.pdf', 'NZC0023625.pdf', 'NZC0023627.pdf', 'NZC0023629.pdf', 'NZC0023630.pdf', 'NZC0023632.pdf', 'NZC0023633.pdf', 'NZC0023634.pdf', 'NZC0023636.pdf', 'NZC0023638.pdf', 'NZC0023639.pdf', 'NZC0023640.pdf', 'NZC0023641.pdf', 'NZC0023642.pdf', 'NZC0023643.pdf', 'NZC0023644.pdf', 'NZC0023645.pdf', 'NZC0023646.pdf', 'NZC0023648.pdf', 'NZC0023650.pdf', 'NZC0023651.pdf', 'NZC0023652.pdf', 'NZC0023653.pdf', 'NZC0023654.pdf', 'NZC0023655.pdf', 'NZC0023656.pdf', 'NZC0023657.pdf', 'NZC0023658.pdf', 'NZC0023659.pdf', 'NZC0023662.pdf', 'NZC0023663.pdf', 'NZC0023664.pdf', 'NZC0023666.pdf', 'NZC0023668.pdf', 'NZC0023670.pdf', 'NZC0023673.pdf', 'NZC0023675.pdf', 'NZC0023676.pdf', 'NZC0023679.pdf', 'NZC0023681.pdf', 'NZC0023685.pdf', 'NZC0023687.pdf', 'NZC0023688.pdf', 'NZC0023689.pdf', 'NZC0023691.pdf', 'NZC0023692.pdf', 'NZC0023696.pdf', 'NZC0023697.pdf', 'NZC0023698.pdf', 'NZC0023703.pdf', 'NZC0023704.pdf', 'NZC0023712.pdf', 'NZC0023714.pdf', 'NZC0023715.pdf', 'NZC0023717.pdf', 'NZC0023718.pdf', 'NZC0023719.pdf', 'NZC0023720.pdf', 'NZC0023722.pdf', 'NZC0023723.pdf', 'NZC0023724.pdf', 'NZC0023725.pdf', 'NZC0023726.pdf', 'NZC0023729.pdf', 'NZC0023730.pdf', 'NZC0023731.pdf', 'NZC0023732.pdf', 'NZC0023733.pdf', 'NZC0023736.pdf', 'NZC0023739.pdf', 'NZC0023741.pdf', 'NZC0023742.pdf', 'NZC0023745.pdf', 'NZC0023746.pdf', 'NZC0023750.pdf', 'NZC0023751.pdf', 'NZC0023752.pdf', 'NZC0023754.pdf', 'NZC0023766.pdf', 'NZC0023767.pdf', 'NZC0023769.pdf', 'NZC0023770.pdf', 'NZC0023771.pdf', 'NZC0023772.pdf', 'NZC0023774.pdf', 'NZC0023775.pdf', 'NZC0023777.pdf', 'NZC0023779.pdf', 'NZC0023781.pdf', 'NZC0023782.pdf', 'NZC0023795.pdf', 'NZC0023796.pdf', 'NZC0023797.pdf', 'NZC0023798.pdf', 'NZC0023801.pdf', 'NZC0023802.pdf', 'NZC0023803.pdf', 'NZC0023805.pdf', 'NZC0023808.pdf', 'NZC0023811.pdf', 'NZC0023812.pdf', 'NZC0023815.pdf', 'NZC0023816.pdf', 'NZC0023817.pdf', 'NZC0023820.pdf', 'NZC0023822.pdf', 'NZC0023825.pdf', 'NZC0023832.pdf', 'NZC0023834.pdf', 'NZC0023835.pdf', 'NZC0023836.pdf', 'NZC0023838.pdf', 'NZC0023843.pdf', 'NZC0023844.pdf', 'NZC0023845.pdf', 'NZC0023847.pdf', 'NZC0023848.pdf', 'NZC0023849.pdf', 'NZC0023851.pdf', 'NZC0023853.pdf', 'NZC0023854.pdf', 'NZC0023857.pdf', 'NZC0023859.pdf', 'NZC0023863.pdf', 'NZC0023866.pdf', 'NZC0023869.pdf', 'NZC0023870.pdf', 'NZC0023871.pdf', 'NZC0023872.pdf', 'NZC0023876.pdf', 'NZC0023877.pdf', 'NZC0023879.pdf', 'NZC0023881.pdf', 'NZC0023882.pdf', 'NZC0023884.pdf', 'NZC0023887.pdf', 'NZC0023888.pdf', 'NZC0023889.pdf', 'NZC0023890.pdf', 'NZC0023892.pdf', 'NZC0023893.pdf', 'NZC0023894.pdf', 'NZC0023899.pdf', 'NZC0023901.pdf', 'NZC0023902.pdf', 'NZC0023907.pdf', 'NZC0023908.pdf', 'NZC0023909.pdf', 'NZC0023910.pdf', 'NZC0023911.pdf', 'NZC0023912.pdf', 'NZC0023913.pdf', 'NZC0023916.pdf', 'NZC0023918.pdf', 'NZC0023921.pdf', 'NZC0023923.pdf', 'NZC0023924.pdf', 'NZC0023927.pdf', 'NZC0023928.pdf', 'NZC0023929.pdf', 'NZC0023931.pdf', 'NZC0023932.pdf', 'NZC0023934.pdf', 'NZC0023935.pdf', 'NZC0023942.pdf', 'NZC0023943.pdf', 'NZC0023948.pdf', 'NZC0023949.pdf', 'NZC0023950.pdf', 'NZC0023953.pdf', 'NZC0023957.pdf', 'NZC0023958.pdf', 'NZC0023960.pdf', 'NZC0023961.pdf', 'NZC0023962.pdf', 'NZC0023963.pdf', 'NZC0023964.pdf', 'NZC0023965.pdf', 'NZC0023966.pdf', 'NZC0023967.pdf', 'NZC0023968.pdf', 'NZC0023969.pdf', 'NZC0023970.pdf', 'NZC0023972.pdf', 'NZC0023973.pdf', 'NZC0023974.pdf', 'NZC0023975.pdf', 'NZC0023976.pdf', 'NZC0023978.pdf', 'NZC0023979.pdf', 'NZC0023980.pdf', 'NZC0023981.pdf', 'NZC0023984.pdf', 'NZC0023986.pdf', 'NZC0023994.pdf', 'NZC0023996.pdf', 'NZC0023997.pdf', 'NZC0023998.pdf', 'NZC0024001.pdf', 'NZC0024002.pdf', 'NZC0024006.pdf', 'NZC0024008.pdf', 'NZC0024010.pdf', 'NZC0024013.pdf', 'NZC0024015.pdf', 'NZC0024017.pdf', 'NZC0024018.pdf', 'NZC0024024.pdf', 'NZC0024029.pdf', 'NZC0024031.pdf', 'NZC0024032.pdf', 'NZC0024035.pdf', 'NZC0024037.pdf', 'NZC0024038.pdf', 'NZC0024040.pdf', 'NZC0024041.pdf', 'NZC0024042.pdf', 'NZC0024044.pdf', 'NZC0024049.pdf', 'NZC0024050.pdf', 'NZC0024053.pdf', 'NZC0024054.pdf', 'NZC0024055.pdf', 'NZC0024058.pdf', 'NZC0024060.pdf', 'NZC0024064.pdf', 'NZC0024067.pdf', 'NZC0024073.pdf', 'NZC0024075.pdf', 'NZC0024077.pdf', 'NZC0024079.pdf', 'NZC0024080.pdf', 'NZC0024081.pdf', 'NZC0024083.pdf', 'NZC0024084.pdf', 'NZC0024085.pdf', 'NZC0024086.pdf', 'NZC0024090.pdf', 'NZC0024091.pdf', 'NZC0024092.pdf', 'NZC0024093.pdf', 'NZC0024094.pdf', 'NZC0024096.pdf', 'NZC0024097.pdf', 'NZC0024098.pdf', 'NZC0024100.pdf', 'NZC0024103.pdf', 'NZC0024104.pdf', 'NZC0024105.pdf', 'NZC0024106.pdf', 'NZC0024107.pdf', 'NZC0024110.pdf', 'NZC0024111.pdf', 'NZC0024115.pdf', 'NZC0024116.pdf', 'NZC0024117.pdf', 'NZC0024118.pdf', 'NZC0024119.pdf', 'NZC0024120.pdf', 'NZC0024123.pdf', 'NZC0024126.pdf', 'NZC0024314.pdf', 'NZC0024393.pdf', 'NZC0024588.pdf', 'NZC0024689.pdf'])"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["document_intelligence_parsed_res.keys()"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["# gather all\n", "def prepare_dataset(raw_data=document_intelligence_parsed_res, labels=mp_labels):\n", "    # raw_data: docfile -> [{\"content\":\"\", ...}]\n", "    # claim_policy_info: claimno -> {\"ClaimAnimalName\": \"\", \"PolicyAnimalNames\": []}\n", "    # labels: claimno -> {docfile: {\"is_mp\"}}\n", "    ans = {}\n", "    for claimno, cinfo in labels.items():\n", "        ans[claimno] = {}\n", "        ans[claimno][\"docfiles\"] = {}\n", "        for docfile, dinfo in cinfo.items():\n", "            content_info = raw_data[docfile]\n", "            content = \"\\n\".join(x[\"content\"] for x in content_info)\n", "            label = dinfo[\"is_mp\"]\n", "            ans[claimno][\"docfiles\"][docfile] = {\"content\": content, \"is_mp\": label, \"parse_result\": content_info} #\"raw\": [x[\"raw\"] for x in content_info], \n", "    return ans"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["# gather all\n", "def prepare_dataset(raw_data=document_intelligence_parsed_res, labels=mp_labels):\n", "    # raw_data: docfile -> [{\"content\":\"\", ...}]\n", "    # claim_policy_info: claimno -> {\"ClaimAnimalName\": \"\", \"PolicyAnimalNames\": []}\n", "    # labels: claimno -> {docfile: {\"is_mp\"}}\n", "    ans = {}\n", "    for claimno, cinfo in labels.items():\n", "        ans[claimno] = {}\n", "        ans[claimno][\"docfiles\"] = {}\n", "        for docfile, dinfo in cinfo.items():\n", "            content_info = raw_data[docfile]\n", "            content = \"\\n\".join(x[\"content\"] for x in content_info)\n", "            label = dinfo[\"is_mp\"]\n", "            ans[claimno][\"docfiles\"][docfile] = {\"content\": content, \"is_mp\": label, \"parse_result\": content_info} #\"raw\": [x[\"raw\"] for x in content_info], \n", "    return ans"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["mp_dataset = prepare_dataset(raw_data=document_intelligence_parsed_res, labels=mp_labels)"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["1039"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["len(mp_dataset)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["del document_intelligence_res1, document_intelligence_res2, document_intelligence_res, document_intelligence_parsed_res, anno_df"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["1134"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["import gc   \n", "gc.collect()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## service provider fuzzy match"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import ast\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from gensim.models import Word2Vec\n", "from src.service_provider_matching.preprocessing.preprocess import preprocess, preprocess_numbers, preprocess_web\n"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["# config\n", "# Dictionary to link between OCR data and list of Service Provider columns\n", "sp_dict = {\n", "    \"Name\": \"ServiceProviderName\",\n", "    \"Address\": \"Address\",\n", "    \"streetname_name\": \"ServiceProviderName\",\n", "    \"suburb_name\": \"ServiceProviderName\",\n", "    \"abn\": \"ABN\",\n", "    \"email\": \"Email\",\n", "    \"web\": \"HomePage\",\n", "    \"phone_home\": \"PhoneNo_Home\",\n", "    \"phone_work\": \"PhoneNo_Work\",\n", "    \"phone_mobile\": \"PhoneNo_Mobile\",\n", "    \"fax\": \"FaxNo\",\n", "}\n", "\n", "# List of hyperparameters\n", "top_n = 10  # Top number of fuzzy matches to keep for Name, Address\n", "cos_l = 0.5  # Lower limit on cosine acceptance\n", "A_log = 1.0  # Logarithmic amplitude for scaling ABN multi-matches\n", "priority_dict = {  # Dictionary to link OCR fields to priority\n", "    \"Name\": 1.0,\n", "    \"Address\": 1.0,\n", "    \"streetname_name\": 0.5,\n", "    \"suburb_name\": 0.5,\n", "    \"abn\": 1.0,\n", "    \"email\": 1.0,\n", "    \"web\": 0.25,\n", "    \"phone_home\": 0.5,\n", "    \"phone_work\": 0.5,\n", "    \"phone_mobile\": 0.5,\n", "    \"fax\": 0.5,\n", "}"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Service Provider data loaded...\n"]}], "source": ["##########################\n", "# Load and preprocess data\n", "##########################\n", "\n", "# Read in Service Provider raw data\n", "# serv_prov_raw = pd.read_csv(\"/workspaces/OCR_in_house/data/OCR_in_house/data/ref_service_provider.csv\")\n", "# serv_prov_raw = pd.read_csv(ROOTDIR/ \"data/ref_service_provider_updated.csv\")\n", "serv_prov_raw = pd.read_csv(\"/home/<USER>/repos/OCR_in_house/data/ref_service_provider_updated.csv\")\n", "serv_prov_raw = serv_prov_raw[serv_prov_raw['Is_Blocked']==0].reset_index(drop=True)\n", "serv_prov = serv_prov_raw.copy()  # create copy of raw data\n", "serv_prov = serv_prov.fillna(\"\")\n", "serv_prov[\"PostCode\"] = serv_prov[\"PostCode\"].astype(str)\n", "print(\"Service Provider data loaded...\")\n", "\n", "# Load list of fields to check in iteration\n", "field_list = list(sp_dict.keys())\n", "priority_scores = [priority_dict[field] for field in field_list]\n", "\n", "# Preprocessing of Service Provider List\n", "serv_prov[\"Address\"] = (\n", "    serv_prov[\"Address\"]\n", "    + \" \"\n", "    + serv_prov[\"City\"]\n", "    + \" \"\n", "    + serv_prov[\"State\"]\n", "    + \" \"\n", "    + serv_prov[\"PostCode\"]\n", ")  # Concat fields to form full Address\n", "for field in [\n", "    \"ServiceProviderName\",\n", "    \"Address\",\n", "    \"ABN\",\n", "    \"Email\",\n", "    \"HomePage\",\n", "    \"PhoneNo_Home\",\n", "    \"PhoneNo_Work\",\n", "    \"PhoneNo_Mobile\",\n", "    \"FaxNo\",\n", "]:\n", "    if field in [\"ServiceProviderName\", \"Address\"]:\n", "        serv_prov[field] = serv_prov[field].apply(preprocess)\n", "    elif field in [\"Email\", \"HomePage\"]:\n", "        serv_prov[field] = serv_prov[field].apply(preprocess_web)\n", "    elif field in [\n", "        \"PhoneNo_Home\",\n", "        \"PhoneNo_Work\",\n", "        \"PhoneNo_Mobile\",\n", "        \"FaxNo\",\n", "        \"PhoneNo_Home\",\n", "    ]:\n", "        serv_prov[field] = serv_prov[field].apply(preprocess_numbers)\n", "    elif field in ['ABN']:\n", "        serv_prov[field] = serv_prov[field].apply(lambda x: int(x) if x != \"\" and pd.notnull(x) else x)\n", "\n", "# # Read in OCR data after parsing\n", "# ocr_data = pd.read_csv(\"preprocessing/dataaa1006_fixed.csv\")\n", "# print(\"OCR data loaded...\")\n", "\n", "# Load FT models into dictionary\n", "# FT_model = {}\n", "# for field in [\"Name\", \"Address\"]:\n", "#     # FT_model[field] = Word2Vec.load(\"/workspaces/OCR_in_house/data/OCR_in_house/data//FT_model_\" + sp_dict[field])\n", "#     FT_model[field] = Word2Vec.load(str(ROOTDIR/\"data//FT_model_updated_\") + sp_dict[field])\n", "#     print(\"FastText model loaded...\", field)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import re\n", "\n", "# abn_extract_regex = r\"(?:\\d *){11}\"\n", "# abn_extract_regex1 = r\"\\d{2}-\\d{3}-\\d{3}-\\d{3}\"\n", "\n", "# def validate_abn(nums: List[int]) -> bool:\n", "#     if len(nums) != 11:\n", "#         return False\n", "#     if not all(isinstance(x, int) for x in nums):\n", "#         return False\n", "#     if any(x>9 for x in nums):\n", "#         return False\n", "#     if any(x<0 for x in nums):\n", "#         return False\n", "\n", "#     s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))\n", "#     return s%89 == 0\n", "\n", "\n", "# def extract_abn(info: Dict) -> List[str]:\n", "#     content = info[\"content\"]\n", "#     matches = re.findall(abn_extract_regex, content)\n", "#     matches1 = re.findall(abn_extract_regex1, content)\n", "#     ans = []\n", "#     for match in matches + matches1:\n", "#         match_num = []\n", "#         for c in match:\n", "#             try:\n", "#                 int_c = int(c)\n", "#                 match_num.append(int_c)\n", "#             except ValueError:\n", "#                 continue\n", "#         if validate_abn(match_num):\n", "#             ans.append(match_num)\n", "\n", "#     ans = list({\"\".join([str(x) for x in abn]) for abn in ans})\n", "#     return ans\n", "#     # info[\"ABN\"] = ans\n", "#     # return info\n", "\n", "# # get phone number \n", "# def extract_fax_number(content: str) -> List[str]:\n", "#     # work landline extraction\n", "#     phone_number_regex = r\"\\(0\\d{1,2}\\) \\d{4} \\d{4}\"\n", "#     matches = re.findall(phone_number_regex, content)\n", "#     return matches\n", "\n", "# def extract_phone_number(content: str) -> List[str]:\n", "#     # work landline extraction\n", "#     phone_number_regex = r\"\\(0\\d{1,2}\\) \\d{4} \\d{4}\"\n", "#     matches = re.findall(phone_number_regex, content)\n", "#     return matches\n", "\n", "# def extract_mobile_number(content: str) -> List[str]:\n", "#     # phone number extraction\n", "#     phone_number_regex = r\"\\+?61[- ]?\\d{1,2}[- ]?\\d{4}[- ]?\\d{4}\"\n", "#     matches = re.findall(phone_number_regex, content) \n", "#     return matches\n", "\n", "# # get email \n", "# def extract_email(content: str) -> List[str]:\n", "#     try: \n", "#         # email extraction with sender name\n", "#         pattern = r\"([a-zA-Z\\s]+)?\\s*<([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})>\"\n", "#         matches = re.findall(pattern, content)\n", "#     except Exception:\n", "#         # Email regex only\n", "#         pattern = r\"([a-zA-Z\\s]+)?\\s*<([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})>\"\n", "#         matches = re.findall(pattern, content)\n", "#     return matches\n", "# # get weblink\n", "# def extract_weblink(content: str) -> List[str]:\n", "#     # weblink extraction\n", "#     weblink_regex = r\"(http|ftp|https)://([\\w_-]+(?:(?:\\.[\\w_-]+)+))([\\w.,@?^=%&:/~+#-]*[\\w@?^=%&/~+#-])?\"\n", "#     matches = re.findall(weblink_regex, content)\n", "#     return matches"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "abn_extract_regex = r\"(?:\\d *){11}\"\n", "abn_extract_regex1 = r\"\\d{2}-\\d{3}-\\d{3}-\\d{3}\"\n", "\n", "def validate_abn(nums: List[int]) -> bool:\n", "    if len(nums) != 11:\n", "        return False\n", "    if not all(isinstance(x, int) for x in nums):\n", "        return False\n", "    if any(x>9 for x in nums):\n", "        return False\n", "    if any(x<0 for x in nums):\n", "        return False\n", "\n", "    s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))\n", "    return s%89 == 0\n", "\n", "\n", "def extract_abn(info: Dict) -> List[str]:\n", "    content = info[\"content\"]\n", "    matches = re.findall(abn_extract_regex, content)\n", "    matches1 = re.findall(abn_extract_regex1, content)\n", "    ans = []\n", "    for match in matches + matches1:\n", "        match_num = []\n", "        for c in match:\n", "            try:\n", "                int_c = int(c)\n", "                match_num.append(int_c)\n", "            except ValueError:\n", "                continue\n", "        if validate_abn(match_num):\n", "            ans.append(match_num)\n", "\n", "    ans = list({\"\".join([str(x) for x in abn]) for abn in ans})\n", "    return ans\n", "    # info[\"ABN\"] = ans\n", "    # return info\n", "\n", "# get phone number \n", "def extract_fax_number(content: str) -> List[str]:\n", "    # work landline extraction\n", "    phone_number_regex = r\"\\(0\\d{1,2}\\) \\d{4} \\d{4}\"\n", "    matches = re.findall(phone_number_regex, content)\n", "    return matches\n", "\n", "def extract_fax_number(content: str) -> List[str]:\n", "    # Regex to capture Australian phone numbers including:\n", "    # 1. Landlines like (02) 9876 5432\n", "    # 2. Mobile numbers like 0412 345 678\n", "    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.\n", "    # Exclude international format like +61\n", "    \n", "    phone_number_regex = r\"(\\(0\\d{1,2}\\) \\d{4} \\d{4})|(\\(04\\d{2}\\) \\d{3} \\d{3})|(\\d{4} \\d{3} \\d{3})|13\\d{4}|1300 \\d{3} \\d{3}|1800 \\d{3} \\d{3}\"\n", "    \n", "    # Extract all matches\n", "    matches = re.findall(phone_number_regex, content)\n", "    \n", "    # Flatten the list and remove any empty strings\n", "    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]\n", "    \n", "    return flattened_matches\n", "\n", "def extract_phone_number(content: str) -> List[str]:\n", "    # Regex to capture Australian phone numbers including:\n", "    # 1. Landlines like (02) 9876 5432\n", "    # 2. Mobile numbers like 0412 345 678\n", "    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.\n", "    # Exclude international format like +61\n", "    \n", "    phone_number_regex = r\"(\\(0\\d{1,2}\\) \\d{4} \\d{4})|(\\(04\\d{2}\\) \\d{3} \\d{3})|(\\d{4} \\d{3} \\d{3})|13\\d{4}|1300 \\d{3} \\d{3}|1800 \\d{3} \\d{3}\"\n", "    \n", "    # Extract all matches\n", "    matches = re.findall(phone_number_regex, content)\n", "    \n", "    # Flatten the list and remove any empty strings\n", "    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]\n", "    \n", "    return flattened_matches\n", "\n", "def extract_mobile_number(content: str) -> List[str]:\n", "    # phone number extraction\n", "    phone_number_regex = r\"\\+?61[- ]?\\d{1,2}[- ]?\\d{4}[- ]?\\d{4}\"\n", "    matches = re.findall(phone_number_regex, content) \n", "    return matches\n", "\n", "def extract_email(content: str) -> List[str]:\n", "    # Replace newlines with spaces to normalize text\n", "    content = content.replace(\"\\n\", \" \")\n", "    \n", "    # Improved regex to capture emails after a colon or space\n", "    pattern = r\"(?<=[:\\s])([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})(?!\\S)\"\n", "    \n", "    matches = re.findall(pattern, content)\n", "    \n", "    return matches\n", "\n", "# get weblink\n", "def extract_weblink(content: str) -> List[str]:\n", "    # weblink extraction\n", "    weblink_regex = r\"(http|ftp|https)://([\\w_-]+(?:(?:\\.[\\w_-]+)+))([\\w.,@?^=%&:/~+#-]*[\\w@?^=%&/~+#-])?\"\n", "    matches = re.findall(weblink_regex, content)\n", "    return matches"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["# prepare service provider info\n", "def prepare_service_provider_info(info: Dict) -> Dict:\n", "    service_provider_info = {}\n", "    content = info[\"content\"]\n", "    service_provider_info[\"phone_home\"] = extract_phone_number(content)\n", "    service_provider_info[\"phone_work\"] = service_provider_info[\"phone_home\"]\n", "    service_provider_info[\"fax\"] = service_provider_info[\"phone_home\"]\n", "    service_provider_info[\"phone_mobile\"] = extract_mobile_number(content)\n", "    service_provider_info[\"email\"] = extract_email(content)\n", "    service_provider_info[\"web\"] = extract_weblink(content)\n", "    service_provider_info[\"abn\"] = extract_abn(info)\n", "    service_provider_info[\"Name\"] = info[\"service_provider\"]\n", "    service_provider_info[\"Address\"] = info[\"service_provider_address\"]\n", "    service_provider_info[\"streetname_name\"] = info[\"service_provider_streetname_name\"]\n", "    service_provider_info[\"suburb_name\"] = info[\"service_provider_suburb_name\"]\n", "    return service_provider_info"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [], "source": ["def fuzzy_match_service_provider(service_provider_info: Dict):\n", "    # Prepare list of match index and cosine values\n", "    match_index_list = []\n", "    match_cosine_list = []\n", "\n", "    # Begin iterating over field list\n", "    for field in field_list:\n", "        if (service_provider_info.get(field, None) is not None or service_provider_info[field] is not np.NaN) and service_provider_info.get(field, \"\"):\n", "            # fields stored in lists (numerical)\n", "            if field in [\"abn\", \"phone_home\", \"phone_work\", \"phone_mobile\", \"fax\"]:\n", "                query_list = [\n", "                    preprocess_numbers(query)\n", "                    for query in service_provider_info[field]\n", "                ]  # Convert string to list of integers\n", "                query_list = [query for query in query_list if query != 99999999]\n", "            # fields stored in lists (non-numerical)\n", "            elif field in [\"email\", \"web\"]:\n", "                query_list = [\n", "                    preprocess_web(query) for query in service_provider_info[field]\n", "                ]  # Convert string to list of strings\n", "            # fields stored in lists (requires Fuzzy Search)\n", "            elif field in [\"Name\", \"Address\"]:\n", "                query_list = [\n", "                    query for query in [service_provider_info[field]]\n", "                ]  # multiple strings in list\n", "                # flatten entire fuzzy match list\n", "                fuzzy_list = []\n", "                for query in query_list:\n", "                    fuzzy_list += FT_model[field].wv.most_similar(query, topn=top_n)\n", "                cosine_dict = {\n", "                    query: 1.0 for query in query_list if query\n", "                }  # Prepare dictionary of cosine value mappings\n", "                query_fuzzy_list = [\n", "                    item[0] for item in fuzzy_list if item[1] >= cos_l\n", "                ]  # only keep items above threshold\n", "                # Add mappings for cosing values\n", "                cosine_fuzzy_dict = {\n", "                    item[0]: item[1] for item in fuzzy_list if item[1] >= cos_l\n", "                }  # only keep items above threshold\n", "                # combine all mappings\n", "                cosine_dict = {**cosine_dict, **cosine_fuzzy_dict}\n", "                # combine all queries\n", "                query_list = query_list + query_fuzzy_list\n", "            # Fields stored in list (does not require Fuzzy search)\n", "            elif field in [\"streetname_name\", \"suburb_name\"]:\n", "                query_list = [\n", "                    preprocess(query) for query in [service_provider_info[field]]\n", "                ]  # Convert string to list of strings\n", "            # fields not stored in lists\n", "            else:\n", "                query_list = [preprocess(service_provider_info[field])]  # Put string in list\n", "\n", "            # # Build up index of matches\n", "            # if field in [\"streetname_name\", \"suburb_name\"] and len(query_list) > 0:\n", "            #     match_index = []\n", "            #     match_query = []\n", "            #     match_length = []\n", "            #     for query in query_list:  # Check all suburbs in query list\n", "            #         match_check = serv_prov[\n", "            #             sp_dict[field]\n", "            #         ].apply(\n", "            #             lambda x: query in x\n", "            #         )  # check if suburb or streetname in contained in list of SP names\n", "            #         match_index += serv_prov[\n", "            #             match_check\n", "            #         ].index.values.tolist()  # Append index list for each query\n", "            #         match_query += serv_prov[match_check][sp_dict[field]].tolist()\n", "            if field in [\"abn\"] and len(query_list) > 0:\n", "                match_index = []\n", "                match_query = []\n", "                match_length = []\n", "                for query in query_list:\n", "                    match_check = serv_prov[sp_dict[field]].isin(query_list)\n", "                    match_index += serv_prov[match_check].index.values.tolist()\n", "                    match_query += serv_prov[match_check][sp_dict[field]].tolist()\n", "                    match_length += [\n", "                        len(match_index) for index in range(0, len(match_index))\n", "                    ]  # Scaling of cosine score from number of matches returned\n", "            else:\n", "                match_check = serv_prov[sp_dict[field]].isin(query_list)\n", "                match_index = serv_prov[match_check].index.values.tolist()\n", "                match_query = serv_prov[match_check][sp_dict[field]].tolist()\n", "            \n", "            # Map to cosine values\n", "            if field in [\"Name\", \"Address\"]:\n", "                match_dict = dict(\n", "                    zip(match_index, [cosine_dict[item] for item in match_query])\n", "                )\n", "                match_cosine = [match_dict[idx] for idx in match_index]\n", "            elif field in [\"abn\"]:\n", "                match_cosine = [\n", "                    1.0 / (A_log * np.log(match_length[idx]) + 1)\n", "                    for idx in range(0, len(match_index))\n", "                ]\n", "            else:\n", "                match_cosine = [1.0 for idx in range(0, len(match_index))]\n", "            match_index_list.append(match_index)\n", "            match_cosine_list.append(match_cosine)\n", "        else:\n", "            match_index_list.append([])\n", "            match_cosine_list.append([])\n", "\n", "    # Flatten entire match index list\n", "    flat_index_list = [ind for sublist in match_index_list for ind in sublist]\n", "\n", "    # Calculate counts, priority score and entities matched\n", "    count_list = []\n", "    for ind in set(flat_index_list):\n", "        priority_score = 0\n", "        fields_matched = []\n", "        for n, match_index in enumerate(match_index_list):\n", "            if ind in match_index:\n", "                cosine_score = match_cosine_list[n][match_index.index(ind)]\n", "                priority_score += priority_scores[n] * cosine_score\n", "                fields_matched.append(field_list[n])\n", "        count_list.append(\n", "            (ind, flat_index_list.count(ind), priority_score, fields_matched)\n", "        )\n", "\n", "    # Keep Top 5 best matches in descending order of priority score\n", "    sorted_count_list = sorted(count_list, key=lambda tup: (tup[2], len(tup[3])), reverse=True)[\n", "        :5\n", "    ]  # Sort by match count/ match priority?\n", "\n", "    # print(sorted_count_list)\n", "\n", "    # Print best and second best matches to file\n", "    if len(sorted_count_list) > 1:\n", "        best_match_index = int([item[0] for item in sorted_count_list][0])\n", "        best_match_count = int([item[1] for item in sorted_count_list][0])\n", "        best_match_priority = [item[2] for item in sorted_count_list][0]\n", "        best_match_fields = [item[3] for item in sorted_count_list][0]\n", "        best_match_list = (\n", "            serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "            .iloc[best_match_index]\n", "            .values.tolist()\n", "        )  # ,'Address','City','State','PostCode','PhoneNo_Home','PhoneNo_Work','PhoneNo_Mobile','FaxNo','Email','HomePage','ABN']].iloc[best_match_index].values.tolist()\n", "\n", "        best_match_index_2 = int([item[0] for item in sorted_count_list][1])\n", "        best_match_count_2 = int([item[1] for item in sorted_count_list][1])\n", "        best_match_priority_2 = [item[2] for item in sorted_count_list][1]\n", "        best_match_fields_2 = [item[3] for item in sorted_count_list][1]\n", "        best_match_list_2 = (\n", "            serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "            .iloc[best_match_index_2]\n", "            .values.tolist()\n", "        )  # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index_2].values.tolist()\n", "    # Single Match\n", "    elif len(sorted_count_list) == 1:\n", "        best_match_index = int([item[0] for item in sorted_count_list][0])\n", "        best_match_count = int([item[1] for item in sorted_count_list][0])\n", "        best_match_priority = [item[2] for item in sorted_count_list][0]\n", "        best_match_fields = [item[3] for item in sorted_count_list][0]\n", "        best_match_list = (\n", "            serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "            .iloc[best_match_index]\n", "            .values.tolist()\n", "        )  # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index].values.tolist()\n", "\n", "        best_match_index_2 = None\n", "        best_match_count_2 = 0\n", "        best_match_priority_2 = 0\n", "        best_match_fields_2 = None\n", "        best_match_list_2 = [None]\n", "    # No matches\n", "    else:\n", "        best_match_index = None\n", "        best_match_count = 0\n", "        best_match_priority = 0\n", "        best_match_fields = None\n", "        best_match_list = [\"\",\"\"]\n", "\n", "        best_match_index_2 = None\n", "        best_match_count_2 = 0\n", "        best_match_priority_2 = None\n", "        best_match_fields_2 = None\n", "        best_match_list_2 = [None]\n", "\n", "    # export to list\n", "    return {\n", "        \"best_match_list\": best_match_list,\n", "        \"best_match_evidence\": (best_match_fields, best_match_count, best_match_priority),\n", "        \"best_match_list2\": best_match_list_2,\n", "        \"best_match_evidence2\": (best_match_fields_2, best_match_count_2, best_match_priority_2),\n", "    }\n", "    \n"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["def fuzzy_match_service_provider(service_provider_info: Dict):\n", "    # Prepare list of match index and cosine values\n", "    match_index_list = []\n", "    match_cosine_list = []\n", "\n", "    # Begin iterating over field list\n", "    for field in field_list:\n", "        if (service_provider_info.get(field, None) is not None or service_provider_info[field] is not np.NaN) and service_provider_info.get(field, \"\"):\n", "            # fields stored in lists (numerical)\n", "            if field in [\"abn\", \"phone_home\", \"phone_work\", \"phone_mobile\", \"fax\"]:\n", "                query_list = [\n", "                    preprocess_numbers(query)\n", "                    for query in service_provider_info[field]\n", "                ]  # Convert string to list of integers\n", "                query_list = [query for query in query_list if query != 99999999]\n", "                # print(query_list)\n", "            # fields stored in lists (non-numerical)\n", "            elif field in [\"email\", \"web\"]:\n", "                query_list = [\n", "                    preprocess_web(query) for query in service_provider_info[field]\n", "                ]  # Convert string to list of strings\n", "            # # fields stored in lists (requires Fuzzy Search)\n", "            # elif field in [\"Name\", \"Address\"]:\n", "            #     query_list = [\n", "            #         query for query in [service_provider_info[field]]\n", "            #     ]  # multiple strings in list\n", "            #     # print(query_list)\n", "            #     # flatten entire fuzzy match list\n", "            #     fuzzy_list = []\n", "            #     for query in query_list:\n", "            #         fuzzy_list += FT_model[field].wv.most_similar(query, topn=top_n)\n", "            #     cosine_dict = {\n", "            #         query: 1.0 for query in query_list if query\n", "            #     }  # Prepare dictionary of cosine value mappings\n", "            #     query_fuzzy_list = [\n", "            #         item[0] for item in fuzzy_list if item[1] >= cos_l\n", "            #     ]  # only keep items above threshold\n", "            #     # print(fuzzy_list)\n", "            #     # Add mappings for cosing values\n", "            #     cosine_fuzzy_dict = {\n", "            #         item[0]: item[1] for item in fuzzy_list if item[1] >= cos_l\n", "            #     }  # only keep items above threshold\n", "            #     # combine all mappings\n", "            #     cosine_dict = {**cosine_dict, **cosine_fuzzy_dict}\n", "            #     # combine all queries\n", "            #     query_list = query_list + query_fuzzy_list\n", "            #     print(query)\n", "            #     print(cosine_dict)\n", "            #     print(query_list)\n", "                      # fields stored in lists (requires Fuzzy Search)\n", "            elif field in [\"Name\", \"Address\"]:\n", "                query_list = [\n", "                    query for query in [service_provider_info[field]]\n", "                ]  # multiple strings in list\n", "                \n", "                # Use fuzzywuzzy for fuzzy matching\n", "                fuzzy_list = []\n", "                for query in query_list:\n", "                    # Perform fuzzy matching with each item in the field_list\n", "                    for field_item in serv_prov[sp_dict[field]]:\n", "                        similarity_score = fuzz.ratio(query.lower(), field_item.lower())\n", "                        # print(query.lower(),field_item.lower(),similarity_score)\n", "                        if similarity_score >= cos_l * 100:  # Check if the score exceeds the threshold\n", "                            fuzzy_list.append((field_item, similarity_score))\n", "                            # print(fuzzy_list)\n", "                \n", "                # Sort by similarity score in descending order\n", "                fuzzy_list = sorted(fuzzy_list, key=lambda x: x[1], reverse=True)\n", "\n", "                # Get the top N results based on similarity score\n", "                query_fuzzy_list = [item[0] for item in fuzzy_list[:top_n]]\n", "                cosine_dict = {\n", "                    item[0]: item[1] / 100.0 for item in fuzzy_list[:top_n]\n", "                }  # Prepare dictionary of cosine value mappings\n", "                \n", "                # Combine the original query with the fuzzy results\n", "                query_list = query_list + query_fuzzy_list\n", "                # print(query)\n", "                # print(cosine_dict)\n", "                # print(query_list)\n", "                \n", "            # Fields stored in list (does not require Fuzzy search)\n", "            elif field in [\"streetname_name\", \"suburb_name\"]:\n", "                query_list = [\n", "                    preprocess(query) for query in [service_provider_info[field]]\n", "                ]  # Convert string to list of strings\n", "            # fields not stored in lists\n", "            else:\n", "                query_list = [preprocess(service_provider_info[field])]  # Put string in list\n", "\n", "            # # Build up index of matches\n", "            # if field in [\"streetname_name\", \"suburb_name\"] and len(query_list) > 0:\n", "            #     match_index = []\n", "            #     match_query = []\n", "            #     match_length = []\n", "            #     for query in query_list:  # Check all suburbs in query list\n", "            #         match_check = serv_prov[\n", "            #             sp_dict[field]\n", "            #         ].apply(\n", "            #             lambda x: query in x\n", "            #         )  # check if suburb or streetname in contained in list of SP names\n", "            #         match_index += serv_prov[\n", "            #             match_check\n", "            #         ].index.values.tolist()  # Append index list for each query\n", "            #         match_query += serv_prov[match_check][sp_dict[field]].tolist()\n", "            # print(query_list)\n", "            if field in [\"abn\"] and len(query_list) > 0:\n", "                match_index = []\n", "                match_query = []\n", "                match_length = []\n", "                for query in query_list:\n", "                    match_check = serv_prov[sp_dict[field]].isin(query_list)\n", "                    match_index += serv_prov[match_check].index.values.tolist()\n", "                    match_query += serv_prov[match_check][sp_dict[field]].tolist()\n", "                    match_length += [\n", "                        len(match_index) for index in range(0, len(match_index))\n", "                    ]  # Scaling of cosine score from number of matches returned\n", "                    # print(match_query)\n", "            else:\n", "                match_check = serv_prov[sp_dict[field]].isin(query_list)\n", "                match_index = serv_prov[match_check].index.values.tolist()\n", "                match_query = serv_prov[match_check][sp_dict[field]].tolist()\n", "                # print(match_check,match_index,match_query)\n", "            \n", "            # Map to cosine values\n", "            if field in [\"Name\", \"Address\"]:\n", "                match_dict = dict(\n", "                    zip(match_index, [cosine_dict[item] for item in match_query])\n", "                )\n", "                match_cosine = [match_dict[idx] for idx in match_index]\n", "            # elif field in [\"abn\"]:\n", "            #     # print('abn')\n", "            #     match_cosine = [\n", "            #         1.0 / (A_log * np.log(match_length[idx]) + 1)\n", "            #         for idx in range(0, len(match_index))\n", "            #     ]\n", "                # print(match_cosine)\n", "            else:\n", "                match_cosine = [1.0 for idx in range(0, len(match_index))]\n", "            match_index_list.append(match_index)\n", "            match_cosine_list.append(match_cosine)\n", "        else:\n", "            match_index_list.append([])\n", "            match_cosine_list.append([])\n", "\n", "    # Flatten entire match index list\n", "    flat_index_list = [ind for sublist in match_index_list for ind in sublist]\n", "\n", "    # Calculate counts, priority score and entities matched\n", "    count_list = []\n", "    for ind in set(flat_index_list):\n", "        priority_score = 0\n", "        fields_matched = []\n", "        for n, match_index in enumerate(match_index_list):\n", "            if ind in match_index:\n", "                cosine_score = match_cosine_list[n][match_index.index(ind)]\n", "                priority_score += priority_scores[n] * cosine_score\n", "                fields_matched.append(field_list[n])\n", "        count_list.append(\n", "            (ind, flat_index_list.count(ind), priority_score, fields_matched)\n", "        )\n", "\n", "    # Keep Top 5 best matches in descending order of priority score\n", "    sorted_count_list = sorted(count_list, key=lambda tup: (tup[2], len(tup[3])), reverse=True)[\n", "        :5\n", "    ]  # Sort by match count/ match priority?\n", "    # print(sorted_count_list)\n", "    # print(sorted_count_list)\n", "\n", "    # Print best and second best matches to file\n", "\n", "    # Initialize the second-best match variables\n", "    best_match_count_2 = 0\n", "    best_match_priority_2 = 0\n", "    best_match_fields_2 = None\n", "    best_match_list_2 = [None]\n", "\n", "    if len(sorted_count_list) > 1:\n", "        best_match_index = int([item[0] for item in sorted_count_list][0])\n", "        best_match_count = int([item[1] for item in sorted_count_list][0])\n", "        best_match_priority = [item[2] for item in sorted_count_list][0]\n", "        best_match_fields = [item[3] for item in sorted_count_list][0]\n", "        best_match_list = (\n", "            serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "            .iloc[best_match_index]\n", "            .values.tolist()\n", "        )\n", "\n", "        # Check for a tie in priority score and resolve by field match count\n", "        best_match_index_2 = int([item[0] for item in sorted_count_list][1])\n", "        best_match_fields_2 = [item[3] for item in sorted_count_list][1]\n", "        best_match_priority_2 = [item[2] for item in sorted_count_list][1]\n", "        best_match_list_2 = (\n", "            serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "            .iloc[best_match_index_2]\n", "            .values.tolist()\n", "        )\n", "\n", "        # If priority scores are the same, compare the number of fields matched\n", "        if best_match_priority == best_match_priority_2:\n", "            if len(best_match_fields) < len(best_match_fields_2):\n", "                best_match_index_2 = int([item[0] for item in sorted_count_list][1])\n", "                best_match_fields_2 = [item[3] for item in sorted_count_list][1]\n", "                best_match_list_2 = (\n", "                    serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "                    .iloc[best_match_index_2]\n", "                    .values.tolist()\n", "                )\n", "            # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index_2].values.tolist()\n", "    \n", "    # Single Match\n", "    elif len(sorted_count_list) == 1:\n", "        best_match_index = int([item[0] for item in sorted_count_list][0])\n", "        best_match_count = int([item[1] for item in sorted_count_list][0])\n", "        best_match_priority = [item[2] for item in sorted_count_list][0]\n", "        best_match_fields = [item[3] for item in sorted_count_list][0]\n", "        best_match_list = (\n", "            serv_prov[[\"ServiceProviderNo\", \"ServiceProviderName\"]]\n", "            .iloc[best_match_index]\n", "            .values.tolist()\n", "        )  # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index].values.tolist()\n", "\n", "        best_match_index_2 = None\n", "        best_match_count_2 = 0\n", "        best_match_priority_2 = 0\n", "        best_match_fields_2 = None\n", "        best_match_list_2 = [None]\n", "    # No matches\n", "    else:\n", "        best_match_index = None\n", "        best_match_count = 0\n", "        best_match_priority = 0\n", "        best_match_fields = None\n", "        best_match_list = [\"\",\"\"]\n", "\n", "        best_match_index_2 = None\n", "        best_match_count_2 = 0\n", "        best_match_priority_2 = None\n", "        best_match_fields_2 = None\n", "        best_match_list_2 = [None]\n", "\n", "    # export to list\n", "    return {\n", "        \"best_match_list\": best_match_list,\n", "        \"best_match_evidence\": (best_match_fields, best_match_count, best_match_priority),\n", "        \"best_match_list2\": best_match_list_2,\n", "        \"best_match_evidence2\": (best_match_fields_2, best_match_count_2, best_match_priority_2),\n", "        \"list\" : sorted_count_list\n", "    }"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/repos/OCR_in_house/.venv/lib/python3.10/site-packages/fuzzywuzzy/fuzz.py:11: UserWarning: Using slow pure-python SequenceMatcher. Install python-Levenshtein to remove this warning\n", "  warnings.warn('Using slow pure-python SequenceMatcher. Install python-Levenshtein to remove this warning')\n"]}], "source": ["import pandas as pd\n", "from fuzzywuzzy import fuzz"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Provider: Johnston Street Veterinary Clinic, Similarity Score: 94%\n", "Provider: Bronson Veterinary Clinic, Similarity Score: 85%\n", "Provider: Thornton Veterinary Clinic, Similarity Score: 84%\n", "Provider: Stephen St Veterinary Clinic, Similarity Score: 84%\n", "Provider: Collins St. Veterinary Clinic, Similarity Score: 83%\n"]}], "source": ["\n", "\n", "# Define the term you want to compare against\n", "term_to_compare = \"JOHNSTON ST VETERINARY CLINIC\"\n", "\n", "# Create a list of tuples (ServiceProviderName, similarity_score)\n", "similarity_results = []\n", "\n", "for provider in serv_prov_raw['ServiceProviderName']:\n", "    # Ensure the provider is a string before comparing\n", "    if isinstance(provider, str):\n", "        similarity_score = fuzz.ratio(term_to_compare.lower(), provider.lower())\n", "        similarity_results.append((provider, similarity_score))\n", "    else:\n", "        similarity_results.append((provider, None))  # You can handle non-strings differently if needed\n", "\n", "# Remove results where similarity_score is None (for non-strings)\n", "similarity_results = [result for result in similarity_results if result[1] is not None]\n", "\n", "# Sort the results by similarity score in descending order\n", "sorted_results = sorted(similarity_results, key=lambda x: x[1], reverse=True)\n", "\n", "# Get the top 5 results\n", "top_5_results = sorted_results[:5]\n", "\n", "# Display the top 5 results\n", "for provider, score in top_5_results:\n", "    print(f\"Provider: {provider}, Similarity Score: {score}%\")"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('tafesa pets in tuition vet', 0.529055655002594),\n", " ('wilston vet', 0.5213812589645386),\n", " ('petstock clifton hill', 0.5213267207145691),\n", " ('pets health hilton vet', 0.5170567035675049),\n", " ('laverton vet', 0.49975675344467163),\n", " ('emerton vet', 0.48095032572746277),\n", " ('moogoo skin care', 0.46003010869026184),\n", " ('care compounding pharmacy', 0.4570271670818329),\n", " ('calanna pharmacy atherton', 0.4462214708328247),\n", " ('south grafton vet', 0.4444141685962677),\n", " ('redcliffe compounding pharmacy', 0.4429132640361786),\n", " ('clifton hill vet', 0.442175030708313),\n", " ('coomera pet barn', 0.4377287030220032),\n", " ('carlton vet', 0.4365738332271576),\n", " ('ladhope compounding pharmacy', 0.43523266911506653),\n", " ('your solution compounding pharmacy', 0.4301952123641968),\n", " ('atara compounding pharmacy', 0.42846307158470154),\n", " ('rockdale compounding pharmacy', 0.4263155460357666),\n", " ('galston vet', 0.4237748384475708),\n", " ('clifton vet', 0.42250606417655945),\n", " ('brookton vet', 0.4210107624530792),\n", " ('johnston st vet', 0.41601094603538513),\n", " ('terry white chemists riverton', 0.41372254490852356),\n", " ('all pet products', 0.41243845224380493),\n", " ('pettina park pet care', 0.4119678735733032),\n", " ('invocare petcare pty', 0.4105437099933624),\n", " ('millicent and kingston vet', 0.4103308618068695),\n", " ('yandina hound hotel', 0.4063465893268585),\n", " ('fairwater compounding pharmacy', 0.4054727256298065),\n", " ('frankston dog obedience club inc', 0.40412405133247375),\n", " ('synergy compounding pharmacy', 0.4010266065597534),\n", " ('karen ramsay dog minder', 0.40017691254615784),\n", " ('redlands dog obedience club', 0.39780834317207336),\n", " ('nollamara compounding pharmacy', 0.39757466316223145),\n", " ('grafton vet', 0.3952302932739258),\n", " ('melton dog obedience club', 0.3911625146865845),\n", " ('junction fair terrywhite chemmart', 0.3883937895298004),\n", " ('glebe compounding pharmacy', 0.38816675543785095),\n", " ('minlaton vet', 0.3873662054538727),\n", " ('thornton vet', 0.3858216404914856),\n", " ('grreat value pet products', 0.3855752646923065),\n", " ('weston vet', 0.38539668917655945),\n", " ('k9 manners', 0.38493093848228455),\n", " ('pets in motion', 0.383995920419693),\n", " ('petstock kingston', 0.3837583661079407),\n", " ('the compounding lab', 0.38298049569129944),\n", " ('wanneroo discount drug store', 0.38233891129493713),\n", " ('terry white chemists fountain gate 2', 0.3821563422679901),\n", " ('terrywhite chemmart kerang', 0.38191747665405273),\n", " ('plant power pets', 0.3810795247554779),\n", " ('terrywhite chemmart kawana', 0.3803732991218567),\n", " ('nova compounding pharmacy vermont south', 0.3795645534992218),\n", " ('petstock chadstone', 0.3795621395111084),\n", " ('he<PERSON> johnstones calmadog', 0.3790804147720337),\n", " ('terry white chemists strathfield', 0.3776933550834656),\n", " ('big w winston hills', 0.377493679523468),\n", " ('edge compounding pharmacy', 0.3767787218093872),\n", " ('adelaide vet dentistry and oromaxillofacial', 0.37665826082229614),\n", " ('pooraka compounding pharmacy', 0.376275897026062),\n", " ('petstock plainland', 0.37612584233283997),\n", " ('nanango pet motel', 0.3753303289413452),\n", " ('john v russ<PERSON>', 0.37358590960502625),\n", " ('lifecare compounding pharmacy', 0.3731366991996765),\n", " ('clinicare compounding pharmacy', 0.37297436594963074),\n", " ('lynne bodell vet', 0.3727172315120697),\n", " ('synergy pharmacy and health store', 0.3726849853992462),\n", " ('terry white chemist winston hills', 0.3722141683101654),\n", " ('ermington vet', 0.3721977472305298),\n", " ('crofton downs vet', 0.3715670108795166),\n", " ('terry white chemists mt gravatt plaza', 0.3714209496974945),\n", " ('ij ca hough ft', 0.37050557136535645),\n", " ('the goondiwindi pastoral vet', 0.3699754774570465),\n", " ('the country pet motel', 0.3688299059867859),\n", " ('tropicats cattery and dog boarding', 0.36879318952560425),\n", " ('woolaston vet', 0.36850401759147644),\n", " ('jeff ramsey compounding pharmacy', 0.36572280526161194),\n", " ('pet wellbeing aus', 0.36399760842323303),\n", " ('dr gemma coulter housecall vet', 0.3636751174926758),\n", " ('perth compounding suites', 0.3634050786495209),\n", " ('dog obedience graduation school of vic', 0.363295316696167),\n", " ('petstock rockingham', 0.36225205659866333),\n", " ('allambie compounding pharmacy', 0.36148762702941895),\n", " ('oz grooming world', 0.3605403006076813),\n", " ('great value pet products', 0.3604009449481964),\n", " ('careplus compounding pharmacy', 0.3591350018978119),\n", " ('petstock clayfield', 0.35831278562545776),\n", " ('petstock preston', 0.3571694493293762),\n", " ('cat protection society of nsw', 0.3562237322330475),\n", " ('enfield produce', 0.3551884591579437),\n", " ('boomerang pet food', 0.35467931628227234),\n", " ('terry white chemists north lakes', 0.35395413637161255),\n", " ('busselton discount drug store', 0.35368141531944275),\n", " ('petstock wollongong', 0.3535515367984772),\n", " ('lawson pharmacy', 0.35339757800102234),\n", " ('ramsay pharmacy whites hill', 0.3529616594314575),\n", " ('petvax vet', 0.35276252031326294),\n", " ('ramsey pharmacy', 0.3527047336101532),\n", " ('milton vet', 0.35240626335144043),\n", " ('frankston compounding pharmacy', 0.3514785170555115),\n", " ('fernvale produce mitre 10', 0.3513457775115967)]"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["f_list = []\n", "f_list += FT_model['Name'].wv.most_similar('Johnston St', topn=100)\n", "f_list"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["a = mp_dataset['C7720011']\n", "\n", "dicta = {}\n", "dicta[\"c\"] = a"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'docfiles': {'f700d43a-6716-438d-b886-65e441e0422b.pdf': {'content': \"Page 1 / 1\\nClient ID:\\n35861\\nMr. <PERSON><PERSON>\\nTax Invoice #:\\n545337\\n26 Harford Street\\nDate:\\n<PERSON><PERSON><PERSON><PERSON>, ACT 2905\\n17/09/2024\\n0423 243 167\\<EMAIL>\\nPatient ID: 83692\\nSpecies: Canine\\nWeight: 26.00\\nkilograms\\nPatient Name: Reggie\\nBreed: Bulldog, British\\nBirthday: 04/01/2024\\nSex: Male\\nDescription\\nStaff Name\\nQuantity\\nTotal\\n17/09/2024\\nConsultation\\nDr. <PERSON>\\n1.00\\n$104.00\\nCephalexin 200mg Beef\\n28.00\\n$62.80\\nAntinol Plus Dog 60's\\n1.00\\n$59.15\\nFungassay- Fungal culture\\n1.00\\n$26.75\\nPatient Subtotal:\\n$252.70\\nAdditional Information\\nThere has been a few cases in Canberra where dogs were presented with signs of tick paralysis without a history of\\ntravel to the coast.\\nWe recommend having your dog on prevention against ticks at least for the warmer months of the year, even if you are\\nnot planning to take your dog down the coast, or travel to the coast yourself.\\nPlease talk to one of our staff if you would like advice on tick prevention.\\nReminder\\n18/03/2025 C5 (Triennial C3) Vaccination\\n11/04/2025 Annual Heartworm Injection\\nInvoice Total:\\n$252.70\\nTotal:\\n$252.70\\nBalance Due:\\n$252.70\\nPrevious Balance:\\n$0.00\\nBalance Due:\\n$252.70\\nEFTPOS:\\n($252.70)\\nLess Payment:\\n($252.70)\\nBalance Due:\\n$0.00\\nThe total price includes GST of $22.97\",\n", "   'is_mp': 0,\n", "   'parse_result': [{'service_provider': 'Mr. <PERSON><PERSON> Mr. <PERSON><PERSON>',\n", "     'service_provider_address': '26 Harford Street Richardson, ACT 2905',\n", "     'service_provider_streetname_name': '26 Harford Street',\n", "     'service_provider_suburb_name': '<PERSON>',\n", "     'content': \"Page 1 / 1\\nClient ID:\\n35861\\nMr. <PERSON><PERSON>\\nTax Invoice #:\\n545337\\n26 Harford Street\\nDate:\\n<PERSON><PERSON><PERSON><PERSON>, ACT 2905\\n17/09/2024\\n0423 243 167\\<EMAIL>\\nPatient ID: 83692\\nSpecies: Canine\\nWeight: 26.00\\nkilograms\\nPatient Name: Reggie\\nBreed: Bulldog, British\\nBirthday: 04/01/2024\\nSex: Male\\nDescription\\nStaff Name\\nQuantity\\nTotal\\n17/09/2024\\nConsultation\\nDr. <PERSON>\\n1.00\\n$104.00\\nCephalexin 200mg Beef\\n28.00\\n$62.80\\nAntinol Plus Dog 60's\\n1.00\\n$59.15\\nFungassay- Fungal culture\\n1.00\\n$26.75\\nPatient Subtotal:\\n$252.70\\nAdditional Information\\nThere has been a few cases in Canberra where dogs were presented with signs of tick paralysis without a history of\\ntravel to the coast.\\nWe recommend having your dog on prevention against ticks at least for the warmer months of the year, even if you are\\nnot planning to take your dog down the coast, or travel to the coast yourself.\\nPlease talk to one of our staff if you would like advice on tick prevention.\\nReminder\\n18/03/2025 C5 (Triennial C3) Vaccination\\n11/04/2025 Annual Heartworm Injection\\nInvoice Total:\\n$252.70\\nTotal:\\n$252.70\\nBalance Due:\\n$252.70\\nPrevious Balance:\\n$0.00\\nBalance Due:\\n$252.70\\nEFTPOS:\\n($252.70)\\nLess Payment:\\n($252.70)\\nBalance Due:\\n$0.00\\nThe total price includes GST of $22.97\",\n", "     'invoice_no': '545337',\n", "     'invoice_date': '2024-09-17',\n", "     'invoice_total': 252.7,\n", "     'service_provider_conf': 0.885,\n", "     'invoice_no_conf': 0.938,\n", "     'invoice_date_conf': 0.947,\n", "     'invoice_total_conf': 0.769,\n", "     'treatments': [{'treatment_date': '2024-09-17',\n", "       'treatment': 'Consultation',\n", "       'amount': 104.0,\n", "       'treatment_date_conf': 0.886,\n", "       'treatment_conf': 0.909999909000009,\n", "       'amount_conf': 0.908,\n", "       'treatmentline_conf': 0.597},\n", "      {'treatment_date': '2024-09-17',\n", "       'treatment': 'Cephalexin 200mg Beef',\n", "       'amount': 62.8,\n", "       'treatment_date_conf': 0.886,\n", "       'treatment_conf': 0.910999908900009,\n", "       'amount_conf': 0.906,\n", "       'treatmentline_conf': 0.911},\n", "      {'treatment_date': '2024-09-17',\n", "       'treatment': \"Antinol Plus Dog 60's\",\n", "       'amount': 59.15,\n", "       'treatment_date_conf': 0.886,\n", "       'treatment_conf': 0.910999908900009,\n", "       'amount_conf': 0.908,\n", "       'treatmentline_conf': 0.916},\n", "      {'treatment_date': '2024-09-17',\n", "       'treatment': 'Fungassay- Fungal culture',\n", "       'amount': 26.75,\n", "       'treatment_date_conf': 0.886,\n", "       'treatment_conf': 0.908999909100009,\n", "       'amount_conf': 0.906,\n", "       'treatmentline_conf': 0.911}],\n", "     'raw': {'api_version': '2023-07-31',\n", "      'model_id': 'prebuilt-invoice',\n", "      'content': \"Page 1 / 1\\nClient ID:\\n35861\\nMr. <PERSON><PERSON>\\nTax Invoice #:\\n545337\\n26 Harford Street\\nDate:\\n<PERSON><PERSON><PERSON><PERSON>, ACT 2905\\n17/09/2024\\n0423 243 167\\<EMAIL>\\nPatient ID: 83692\\nSpecies: Canine\\nWeight: 26.00\\nkilograms\\nPatient Name: Reggie\\nBreed: Bulldog, British\\nBirthday: 04/01/2024\\nSex: Male\\nDescription\\nStaff Name\\nQuantity\\nTotal\\n17/09/2024\\nConsultation\\nDr. <PERSON>\\n1.00\\n$104.00\\nCephalexin 200mg Beef\\n28.00\\n$62.80\\nAntinol Plus Dog 60's\\n1.00\\n$59.15\\nFungassay- Fungal culture\\n1.00\\n$26.75\\nPatient Subtotal:\\n$252.70\\nAdditional Information\\nThere has been a few cases in Canberra where dogs were presented with signs of tick paralysis without a history of\\ntravel to the coast.\\nWe recommend having your dog on prevention against ticks at least for the warmer months of the year, even if you are\\nnot planning to take your dog down the coast, or travel to the coast yourself.\\nPlease talk to one of our staff if you would like advice on tick prevention.\\nReminder\\n18/03/2025 C5 (Triennial C3) Vaccination\\n11/04/2025 Annual Heartworm Injection\\nInvoice Total:\\n$252.70\\nTotal:\\n$252.70\\nBalance Due:\\n$252.70\\nPrevious Balance:\\n$0.00\\nBalance Due:\\n$252.70\\nEFTPOS:\\n($252.70)\\nLess Payment:\\n($252.70)\\nBalance Due:\\n$0.00\\nThe total price includes GST of $22.97\",\n", "      'languages': [],\n", "      'pages': [{'page_number': 1,\n", "        'angle': None,\n", "        'width': 8.5,\n", "        'height': 11.0,\n", "        'unit': 'inch',\n", "        'lines': [{'content': 'Page 1 / 1',\n", "          'polygon': [{'x': 7.0435, 'y': 0.5347},\n", "           {'x': 7.6643, 'y': 0.5347},\n", "           {'x': 7.6643, 'y': 0.6923},\n", "           {'x': 7.0435, 'y': 0.6875}],\n", "          'spans': [{'offset': 0, 'length': 10}]},\n", "         {'content': 'Client ID:',\n", "          'polygon': [{'x': 6.2031, 'y': 1.5326},\n", "           {'x': 6.7904, 'y': 1.5326},\n", "           {'x': 6.7857, 'y': 1.6662},\n", "           {'x': 6.2031, 'y': 1.6662}],\n", "          'spans': [{'offset': 11, 'length': 10}]},\n", "         {'content': '35861',\n", "          'polygon': [{'x': 7.3062, 'y': 1.5326},\n", "           {'x': 7.6978, 'y': 1.5326},\n", "           {'x': 7.693, 'y': 1.6662},\n", "           {'x': 7.3062, 'y': 1.6662}],\n", "          'spans': [{'offset': 22, 'length': 5}]},\n", "         {'content': 'Mr. <PERSON><PERSON>',\n", "          'polygon': [{'x': 0.3295, 'y': 1.7044},\n", "           {'x': 1.5424, 'y': 1.7044},\n", "           {'x': 1.5472, 'y': 1.8524},\n", "           {'x': 0.3295, 'y': 1.8524}],\n", "          'spans': [{'offset': 28, 'length': 18}]},\n", "         {'content': 'Tax Invoice #:',\n", "          'polygon': [{'x': 5.9309, 'y': 1.7283},\n", "           {'x': 6.7904, 'y': 1.7283},\n", "           {'x': 6.7904, 'y': 1.862},\n", "           {'x': 5.9309, 'y': 1.862}],\n", "          'spans': [{'offset': 47, 'length': 14}]},\n", "         {'content': '545337',\n", "          'polygon': [{'x': 7.2298, 'y': 1.7283},\n", "           {'x': 7.7073, 'y': 1.7283},\n", "           {'x': 7.7073, 'y': 1.862},\n", "           {'x': 7.2298, 'y': 1.862}],\n", "          'spans': [{'offset': 62, 'length': 6}]},\n", "         {'content': '26 Harford Street',\n", "          'polygon': [{'x': 0.3199, 'y': 1.8811},\n", "           {'x': 1.4183, 'y': 1.8859},\n", "           {'x': 1.4183, 'y': 2.0339},\n", "           {'x': 0.3199, 'y': 2.0339}],\n", "          'spans': [{'offset': 69, 'length': 17}]},\n", "         {'content': 'Date:',\n", "          'polygon': [{'x': 6.4514, 'y': 1.9336},\n", "           {'x': 6.7904, 'y': 1.9336},\n", "           {'x': 6.7904, 'y': 2.0577},\n", "           {'x': 6.4514, 'y': 2.053}],\n", "          'spans': [{'offset': 87, 'length': 5}]},\n", "         {'content': 'Richardson, ACT 2905',\n", "          'polygon': [{'x': 0.3295, 'y': 2.0577},\n", "           {'x': 1.7478, 'y': 2.0577},\n", "           {'x': 1.7478, 'y': 2.2105},\n", "           {'x': 0.3295, 'y': 2.2153}],\n", "          'spans': [{'offset': 93, 'length': 20}]},\n", "         {'content': '17/09/2024',\n", "          'polygon': [{'x': 7.0053, 'y': 1.9097},\n", "           {'x': 7.7073, 'y': 1.9097},\n", "           {'x': 7.7073, 'y': 2.072},\n", "           {'x': 7.0053, 'y': 2.0768}],\n", "          'spans': [{'offset': 114, 'length': 10}]},\n", "         {'content': '0423 243 167',\n", "          'polygon': [{'x': 6.8525, 'y': 2.115},\n", "           {'x': 7.6978, 'y': 2.1102},\n", "           {'x': 7.6978, 'y': 2.2487},\n", "           {'x': 6.8525, 'y': 2.2487}],\n", "          'spans': [{'offset': 125, 'length': 12}]},\n", "         {'content': '<EMAIL>',\n", "          'polygon': [{'x': 5.8593, 'y': 2.3203},\n", "           {'x': 7.7073, 'y': 2.3155},\n", "           {'x': 7.7073, 'y': 2.454},\n", "           {'x': 5.8593, 'y': 2.4635}],\n", "          'spans': [{'offset': 138, 'length': 26}]},\n", "         {'content': 'Patient ID: 83692',\n", "          'polygon': [{'x': 0.4728, 'y': 2.6068},\n", "           {'x': 1.3466, 'y': 2.6068},\n", "           {'x': 1.3466, 'y': 2.7261},\n", "           {'x': 0.4728, 'y': 2.7309}],\n", "          'spans': [{'offset': 165, 'length': 17}]},\n", "         {'content': 'Species: Canine',\n", "          'polygon': [{'x': 2.9893, 'y': 2.6068},\n", "           {'x': 3.825, 'y': 2.602},\n", "           {'x': 3.825, 'y': 2.7309},\n", "           {'x': 2.9893, 'y': 2.7357}],\n", "          'spans': [{'offset': 183, 'length': 15}]},\n", "         {'content': 'Weight: 26.00',\n", "          'polygon': [{'x': 5.2576, 'y': 2.5877},\n", "           {'x': 6.0885, 'y': 2.5877},\n", "           {'x': 6.0837, 'y': 2.75},\n", "           {'x': 5.2576, 'y': 2.7452}],\n", "          'spans': [{'offset': 199, 'length': 13}]},\n", "         {'content': 'kilograms',\n", "          'polygon': [{'x': 6.1219, 'y': 2.5829},\n", "           {'x': 6.6806, 'y': 2.5877},\n", "           {'x': 6.6758, 'y': 2.7548},\n", "           {'x': 6.1219, 'y': 2.75}],\n", "          'spans': [{'offset': 213, 'length': 9}]},\n", "         {'content': 'Patient Name: <PERSON>',\n", "          'polygon': [{'x': 0.2865, 'y': 2.8121},\n", "           {'x': 1.4039, 'y': 2.8168},\n", "           {'x': 1.4039, 'y': 2.9457},\n", "           {'x': 0.2865, 'y': 2.941}],\n", "          'spans': [{'offset': 223, 'length': 20}]},\n", "         {'content': 'Breed: Bulldog, British',\n", "          'polygon': [{'x': 3.0896, 'y': 2.8168},\n", "           {'x': 4.2213, 'y': 2.8216},\n", "           {'x': 4.2213, 'y': 2.9553},\n", "           {'x': 3.0896, 'y': 2.9505}],\n", "          'spans': [{'offset': 244, 'length': 23}]},\n", "         {'content': 'Birthday: 04/01/2024',\n", "          'polygon': [{'x': 5.1955, 'y': 2.8216},\n", "           {'x': 6.2413, 'y': 2.8264},\n", "           {'x': 6.2413, 'y': 2.9505},\n", "           {'x': 5.1955, 'y': 2.9457}],\n", "          'spans': [{'offset': 268, 'length': 20}]},\n", "         {'content': 'Sex: Male',\n", "          'polygon': [{'x': 6.5278, 'y': 2.8216},\n", "           {'x': 7.0435, 'y': 2.8264},\n", "           {'x': 7.0435, 'y': 2.941},\n", "           {'x': 6.5278, 'y': 2.941}],\n", "          'spans': [{'offset': 289, 'length': 9}]},\n", "         {'content': 'Description',\n", "          'polygon': [{'x': 1.3371, 'y': 3.0508},\n", "           {'x': 2.1107, 'y': 3.0556},\n", "           {'x': 2.1107, 'y': 3.1988},\n", "           {'x': 1.3371, 'y': 3.194}],\n", "          'spans': [{'offset': 299, 'length': 11}]},\n", "         {'content': 'Staff Name',\n", "          'polygon': [{'x': 3.9348, 'y': 3.046},\n", "           {'x': 4.6846, 'y': 3.046},\n", "           {'x': 4.6846, 'y': 3.2083},\n", "           {'x': 3.9348, 'y': 3.2083}],\n", "          'spans': [{'offset': 311, 'length': 10}]},\n", "         {'content': 'Quantity',\n", "          'polygon': [{'x': 5.673, 'y': 3.046},\n", "           {'x': 6.2699, 'y': 3.046},\n", "           {'x': 6.2699, 'y': 3.2131},\n", "           {'x': 5.673, 'y': 3.2083}],\n", "          'spans': [{'offset': 322, 'length': 8}]},\n", "         {'content': 'Total',\n", "          'polygon': [{'x': 6.8334, 'y': 3.0556},\n", "           {'x': 7.1772, 'y': 3.0508},\n", "           {'x': 7.1772, 'y': 3.194},\n", "           {'x': 6.8382, 'y': 3.194}],\n", "          'spans': [{'offset': 331, 'length': 5}]},\n", "         {'content': '17/09/2024',\n", "          'polygon': [{'x': 0.2865, 'y': 3.2561},\n", "           {'x': 0.9789, 'y': 3.2561},\n", "           {'x': 0.9789, 'y': 3.3945},\n", "           {'x': 0.2865, 'y': 3.3945}],\n", "          'spans': [{'offset': 337, 'length': 10}]},\n", "         {'content': 'Consultation',\n", "          'polygon': [{'x': 1.3228, 'y': 3.2513},\n", "           {'x': 2.1107, 'y': 3.2513},\n", "           {'x': 2.1107, 'y': 3.3945},\n", "           {'x': 1.3228, 'y': 3.3898}],\n", "          'spans': [{'offset': 348, 'length': 12}]},\n", "         {'content': 'Dr. <PERSON>',\n", "          'polygon': [{'x': 3.9396, 'y': 3.2465},\n", "           {'x': 4.9376, 'y': 3.2465},\n", "           {'x': 4.9376, 'y': 3.4089},\n", "           {'x': 3.9396, 'y': 3.4089}],\n", "          'spans': [{'offset': 361, 'length': 15}]},\n", "         {'content': '1.00',\n", "          'polygon': [{'x': 5.8545, 'y': 3.2609},\n", "           {'x': 6.1219, 'y': 3.2561},\n", "           {'x': 6.1219, 'y': 3.3898},\n", "           {'x': 5.8545, 'y': 3.385}],\n", "          'spans': [{'offset': 377, 'length': 4}]},\n", "         {'content': '$104.00',\n", "          'polygon': [{'x': 6.8955, 'y': 3.2561},\n", "           {'x': 7.416, 'y': 3.2561},\n", "           {'x': 7.416, 'y': 3.3945},\n", "           {'x': 6.8955, 'y': 3.3945}],\n", "          'spans': [{'offset': 382, 'length': 7}]},\n", "         {'content': 'Cephalexin 200mg Beef',\n", "          'polygon': [{'x': 1.3228, 'y': 3.4136},\n", "           {'x': 2.827, 'y': 3.4136},\n", "           {'x': 2.827, 'y': 3.576},\n", "           {'x': 1.3228, 'y': 3.576}],\n", "          'spans': [{'offset': 390, 'length': 21}]},\n", "         {'content': '28.00',\n", "          'polygon': [{'x': 5.759, 'y': 3.428},\n", "           {'x': 6.1219, 'y': 3.4232},\n", "           {'x': 6.1219, 'y': 3.5569},\n", "           {'x': 5.7638, 'y': 3.5521}],\n", "          'spans': [{'offset': 412, 'length': 5}]},\n", "         {'content': '$62.80',\n", "          'polygon': [{'x': 6.9862, 'y': 3.4232},\n", "           {'x': 7.416, 'y': 3.4184},\n", "           {'x': 7.416, 'y': 3.5616},\n", "           {'x': 6.9862, 'y': 3.5616}],\n", "          'spans': [{'offset': 418, 'length': 6}]},\n", "         {'content': \"Antinol Plus Dog 60's\",\n", "          'polygon': [{'x': 1.3228, 'y': 3.5855},\n", "           {'x': 2.6503, 'y': 3.5855},\n", "           {'x': 2.6503, 'y': 3.7431},\n", "           {'x': 1.3228, 'y': 3.7335}],\n", "          'spans': [{'offset': 425, 'length': 21}]},\n", "         {'content': '1.00',\n", "          'polygon': [{'x': 5.8545, 'y': 3.5998},\n", "           {'x': 6.1219, 'y': 3.5951},\n", "           {'x': 6.1219, 'y': 3.724},\n", "           {'x': 5.8593, 'y': 3.7144}],\n", "          'spans': [{'offset': 447, 'length': 4}]},\n", "         {'content': '$59.15',\n", "          'polygon': [{'x': 6.9815, 'y': 3.5855},\n", "           {'x': 7.4208, 'y': 3.5855},\n", "           {'x': 7.4208, 'y': 3.7335},\n", "           {'x': 6.9815, 'y': 3.7287}],\n", "          'spans': [{'offset': 452, 'length': 6}]},\n", "         {'content': 'Fungassay- Fungal culture',\n", "          'polygon': [{'x': 1.3323, 'y': 3.7526},\n", "           {'x': 2.9798, 'y': 3.7526},\n", "           {'x': 2.9798, 'y': 3.9102},\n", "           {'x': 1.3323, 'y': 3.9102}],\n", "          'spans': [{'offset': 459, 'length': 25}]},\n", "         {'content': '1.00',\n", "          'polygon': [{'x': 5.8545, 'y': 3.7622},\n", "           {'x': 6.1219, 'y': 3.7622},\n", "           {'x': 6.1219, 'y': 3.8911},\n", "           {'x': 5.8593, 'y': 3.8815}],\n", "          'spans': [{'offset': 485, 'length': 4}]},\n", "         {'content': '$26.75',\n", "          'polygon': [{'x': 6.9671, 'y': 3.7478},\n", "           {'x': 7.4256, 'y': 3.7431},\n", "           {'x': 7.4303, 'y': 3.9006},\n", "           {'x': 6.9671, 'y': 3.9006}],\n", "          'spans': [{'offset': 490, 'length': 6}]},\n", "         {'content': 'Patient Subtotal:',\n", "          'polygon': [{'x': 5.0475, 'y': 3.9531},\n", "           {'x': 6.1601, 'y': 3.9531},\n", "           {'x': 6.1601, 'y': 4.1155},\n", "           {'x': 5.0475, 'y': 4.1155}],\n", "          'spans': [{'offset': 497, 'length': 17}]},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9003, 'y': 3.9627},\n", "           {'x': 7.4256, 'y': 3.9579},\n", "           {'x': 7.4256, 'y': 4.1107},\n", "           {'x': 6.9051, 'y': 4.1107}],\n", "          'spans': [{'offset': 515, 'length': 7}]},\n", "         {'content': 'Additional Information',\n", "          'polygon': [{'x': 0.2626, 'y': 4.2014},\n", "           {'x': 1.6236, 'y': 4.2014},\n", "           {'x': 1.6236, 'y': 4.3542},\n", "           {'x': 0.2626, 'y': 4.3542}],\n", "          'spans': [{'offset': 523, 'length': 22}]},\n", "         {'content': 'There has been a few cases in Canberra where dogs were presented with signs of tick paralysis without a history of',\n", "          'polygon': [{'x': 0.2483, 'y': 4.378},\n", "           {'x': 7.3301, 'y': 4.378},\n", "           {'x': 7.3301, 'y': 4.5499},\n", "           {'x': 0.2483, 'y': 4.5404}],\n", "          'spans': [{'offset': 546, 'length': 114}]},\n", "         {'content': 'travel to the coast.',\n", "          'polygon': [{'x': 0.2531, 'y': 4.5451},\n", "           {'x': 1.3992, 'y': 4.5451},\n", "           {'x': 1.3992, 'y': 4.6884},\n", "           {'x': 0.2531, 'y': 4.6884}],\n", "          'spans': [{'offset': 661, 'length': 20}]},\n", "         {'content': 'We recommend having your dog on prevention against ticks at least for the warmer months of the year, even if you are',\n", "          'polygon': [{'x': 0.2579, 'y': 4.6979},\n", "           {'x': 7.5258, 'y': 4.6979},\n", "           {'x': 7.5258, 'y': 4.8602},\n", "           {'x': 0.2579, 'y': 4.8555}],\n", "          'spans': [{'offset': 682, 'length': 116}]},\n", "         {'content': 'not planning to take your dog down the coast, or travel to the coast yourself.',\n", "          'polygon': [{'x': 0.2579, 'y': 4.8555},\n", "           {'x': 4.8994, 'y': 4.8555},\n", "           {'x': 4.8994, 'y': 5.013},\n", "           {'x': 0.2579, 'y': 5.013}],\n", "          'spans': [{'offset': 799, 'length': 78}]},\n", "         {'content': 'Please talk to one of our staff if you would like advice on tick prevention.',\n", "          'polygon': [{'x': 0.2579, 'y': 5.0178},\n", "           {'x': 4.6702, 'y': 5.0226},\n", "           {'x': 4.6702, 'y': 5.1801},\n", "           {'x': 0.2579, 'y': 5.1706}],\n", "          'spans': [{'offset': 878, 'length': 76}]},\n", "         {'content': 'Reminder',\n", "          'polygon': [{'x': 0.2388, 'y': 5.3759},\n", "           {'x': 0.8548, 'y': 5.3806},\n", "           {'x': 0.8548, 'y': 5.5191},\n", "           {'x': 0.2388, 'y': 5.5143}],\n", "          'spans': [{'offset': 955, 'length': 8}]},\n", "         {'content': '18/03/2025 C5 (Triennial C3) Vaccination',\n", "          'polygon': [{'x': 0.2817, 'y': 5.5382},\n", "           {'x': 2.8795, 'y': 5.543},\n", "           {'x': 2.8795, 'y': 5.7053},\n", "           {'x': 0.2817, 'y': 5.7005}],\n", "          'spans': [{'offset': 964, 'length': 40}]},\n", "         {'content': '11/04/2025 Annual Heartworm Injection',\n", "          'polygon': [{'x': 0.277, 'y': 5.7292},\n", "           {'x': 2.7792, 'y': 5.7339},\n", "           {'x': 2.7792, 'y': 5.8915},\n", "           {'x': 0.277, 'y': 5.8819}],\n", "          'spans': [{'offset': 1005, 'length': 37}]},\n", "         {'content': 'Invoice Total:',\n", "          'polygon': [{'x': 5.2242, 'y': 5.9822},\n", "           {'x': 6.1458, 'y': 5.9774},\n", "           {'x': 6.1458, 'y': 6.1398},\n", "           {'x': 5.2242, 'y': 6.1445}],\n", "          'spans': [{'offset': 1043, 'length': 14}]},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9003, 'y': 5.9822},\n", "           {'x': 7.4208, 'y': 5.9822},\n", "           {'x': 7.4208, 'y': 6.1302},\n", "           {'x': 6.9003, 'y': 6.135}],\n", "          'spans': [{'offset': 1058, 'length': 7}]},\n", "         {'content': 'Total:',\n", "          'polygon': [{'x': 5.7924, 'y': 6.2066},\n", "           {'x': 6.1362, 'y': 6.197},\n", "           {'x': 6.141, 'y': 6.3355},\n", "           {'x': 5.7924, 'y': 6.3307}],\n", "          'spans': [{'offset': 1066, 'length': 6}]},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9051, 'y': 6.1875},\n", "           {'x': 7.4112, 'y': 6.1875},\n", "           {'x': 7.416, 'y': 6.3498},\n", "           {'x': 6.9051, 'y': 6.3498}],\n", "          'spans': [{'offset': 1073, 'length': 7}]},\n", "         {'content': 'Balance Due:',\n", "          'polygon': [{'x': 5.2958, 'y': 6.4119},\n", "           {'x': 6.1362, 'y': 6.4119},\n", "           {'x': 6.1362, 'y': 6.5503},\n", "           {'x': 5.2958, 'y': 6.5503}],\n", "          'spans': [{'offset': 1081, 'length': 12}]},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9003, 'y': 6.4071},\n", "           {'x': 7.4112, 'y': 6.4119},\n", "           {'x': 7.4112, 'y': 6.5599},\n", "           {'x': 6.9003, 'y': 6.5599}],\n", "          'spans': [{'offset': 1094, 'length': 7}]},\n", "         {'content': 'Previous Balance:',\n", "          'polygon': [{'x': 5.014, 'y': 6.6315},\n", "           {'x': 6.1315, 'y': 6.6315},\n", "           {'x': 6.1315, 'y': 6.77},\n", "           {'x': 5.014, 'y': 6.7747}],\n", "          'spans': [{'offset': 1102, 'length': 17}]},\n", "         {'content': '$0.00',\n", "          'polygon': [{'x': 7.0531, 'y': 6.6363},\n", "           {'x': 7.4065, 'y': 6.6315},\n", "           {'x': 7.4112, 'y': 6.7795},\n", "           {'x': 7.0579, 'y': 6.7747}],\n", "          'spans': [{'offset': 1120, 'length': 5}]},\n", "         {'content': 'Balance Due:',\n", "          'polygon': [{'x': 5.3053, 'y': 6.8464},\n", "           {'x': 6.1362, 'y': 6.8464},\n", "           {'x': 6.1362, 'y': 6.9944},\n", "           {'x': 5.3053, 'y': 6.9944}],\n", "          'spans': [{'offset': 1126, 'length': 12}]},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9003, 'y': 6.8464},\n", "           {'x': 7.4112, 'y': 6.8511},\n", "           {'x': 7.4112, 'y': 6.9991},\n", "           {'x': 6.9003, 'y': 6.9991}],\n", "          'spans': [{'offset': 1139, 'length': 7}]},\n", "         {'content': 'EFTPOS:',\n", "          'polygon': [{'x': 5.5298, 'y': 7.0612},\n", "           {'x': 6.1362, 'y': 7.0612},\n", "           {'x': 6.1315, 'y': 7.214},\n", "           {'x': 5.5298, 'y': 7.2092}],\n", "          'spans': [{'offset': 1147, 'length': 7}]},\n", "         {'content': '($252.70)',\n", "          'polygon': [{'x': 6.8239, 'y': 7.066},\n", "           {'x': 7.4017, 'y': 7.066},\n", "           {'x': 7.4017, 'y': 7.2283},\n", "           {'x': 6.8239, 'y': 7.2235}],\n", "          'spans': [{'offset': 1155, 'length': 9}]},\n", "         {'content': 'Less Payment:',\n", "          'polygon': [{'x': 5.2098, 'y': 7.276},\n", "           {'x': 6.141, 'y': 7.276},\n", "           {'x': 6.141, 'y': 7.4479},\n", "           {'x': 5.2098, 'y': 7.4479}],\n", "          'spans': [{'offset': 1165, 'length': 13}]},\n", "         {'content': '($252.70)',\n", "          'polygon': [{'x': 6.8287, 'y': 7.2904},\n", "           {'x': 7.4112, 'y': 7.2856},\n", "           {'x': 7.4112, 'y': 7.4527},\n", "           {'x': 6.8287, 'y': 7.4575}],\n", "          'spans': [{'offset': 1179, 'length': 9}]},\n", "         {'content': 'Balance Due:',\n", "          'polygon': [{'x': 5.2528, 'y': 7.5195},\n", "           {'x': 6.141, 'y': 7.5195},\n", "           {'x': 6.141, 'y': 7.6723},\n", "           {'x': 5.2528, 'y': 7.6723}],\n", "          'spans': [{'offset': 1189, 'length': 12}]},\n", "         {'content': '$0.00',\n", "          'polygon': [{'x': 7.0531, 'y': 7.5482},\n", "           {'x': 7.416, 'y': 7.5482},\n", "           {'x': 7.416, 'y': 7.6914},\n", "           {'x': 7.0531, 'y': 7.6914}],\n", "          'spans': [{'offset': 1202, 'length': 5}]},\n", "         {'content': 'The total price includes GST of $22.97',\n", "          'polygon': [{'x': 4.8803, 'y': 7.8442},\n", "           {'x': 7.4303, 'y': 7.8346},\n", "           {'x': 7.4303, 'y': 8.0065},\n", "           {'x': 4.8803, 'y': 8.0161}],\n", "          'spans': [{'offset': 1208, 'length': 38}]}],\n", "        'words': [{'content': 'Page',\n", "          'polygon': [{'x': 7.0435, 'y': 0.5395},\n", "           {'x': 7.3635, 'y': 0.5395},\n", "           {'x': 7.3635, 'y': 0.697},\n", "           {'x': 7.0435, 'y': 0.6923}],\n", "          'span': {'offset': 0, 'length': 4},\n", "          'confidence': 0.962},\n", "         {'content': '1',\n", "          'polygon': [{'x': 7.4112, 'y': 0.5395},\n", "           {'x': 7.4733, 'y': 0.5395},\n", "           {'x': 7.4733, 'y': 0.697},\n", "           {'x': 7.4112, 'y': 0.697}],\n", "          'span': {'offset': 5, 'length': 1},\n", "          'confidence': 0.995},\n", "         {'content': '/',\n", "          'polygon': [{'x': 7.5067, 'y': 0.5395},\n", "           {'x': 7.564, 'y': 0.5395},\n", "           {'x': 7.564, 'y': 0.697},\n", "           {'x': 7.5067, 'y': 0.697}],\n", "          'span': {'offset': 7, 'length': 1},\n", "          'confidence': 0.997},\n", "         {'content': '1',\n", "          'polygon': [{'x': 7.5975, 'y': 0.5395},\n", "           {'x': 7.6596, 'y': 0.5395},\n", "           {'x': 7.6596, 'y': 0.697},\n", "           {'x': 7.5975, 'y': 0.697}],\n", "          'span': {'offset': 9, 'length': 1},\n", "          'confidence': 0.996},\n", "         {'content': 'Client',\n", "          'polygon': [{'x': 6.2079, 'y': 1.5373},\n", "           {'x': 6.5708, 'y': 1.5373},\n", "           {'x': 6.5708, 'y': 1.671},\n", "           {'x': 6.2079, 'y': 1.6662}],\n", "          'span': {'offset': 11, 'length': 6},\n", "          'confidence': 0.994},\n", "         {'content': 'ID:',\n", "          'polygon': [{'x': 6.5947, 'y': 1.5373},\n", "           {'x': 6.7904, 'y': 1.5373},\n", "           {'x': 6.7904, 'y': 1.671},\n", "           {'x': 6.5947, 'y': 1.671}],\n", "          'span': {'offset': 18, 'length': 3},\n", "          'confidence': 0.995},\n", "         {'content': '35861',\n", "          'polygon': [{'x': 7.3253, 'y': 1.5421},\n", "           {'x': 7.6978, 'y': 1.5373},\n", "           {'x': 7.6978, 'y': 1.671},\n", "           {'x': 7.3253, 'y': 1.6662}],\n", "          'span': {'offset': 22, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'Mr.',\n", "          'polygon': [{'x': 0.3295, 'y': 1.7044},\n", "           {'x': 0.5396, 'y': 1.7044},\n", "           {'x': 0.5444, 'y': 1.8524},\n", "           {'x': 0.3343, 'y': 1.8572}],\n", "          'span': {'offset': 28, 'length': 3},\n", "          'confidence': 0.98},\n", "         {'content': '<PERSON><PERSON>',\n", "          'polygon': [{'x': 0.5683, 'y': 1.7044},\n", "           {'x': 1.0315, 'y': 1.7092},\n", "           {'x': 1.0315, 'y': 1.8524},\n", "           {'x': 0.573, 'y': 1.8524}],\n", "          'span': {'offset': 32, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': '<PERSON><PERSON><PERSON>',\n", "          'polygon': [{'x': 1.0697, 'y': 1.7092},\n", "           {'x': 1.5472, 'y': 1.7044},\n", "           {'x': 1.5472, 'y': 1.8524},\n", "           {'x': 1.0697, 'y': 1.8524}],\n", "          'span': {'offset': 39, 'length': 7},\n", "          'confidence': 0.994},\n", "         {'content': 'Tax',\n", "          'polygon': [{'x': 5.95, 'y': 1.7331},\n", "           {'x': 6.1553, 'y': 1.7331},\n", "           {'x': 6.1601, 'y': 1.862},\n", "           {'x': 5.95, 'y': 1.8668}],\n", "          'span': {'offset': 47, 'length': 3},\n", "          'confidence': 0.996},\n", "         {'content': 'Invoice',\n", "          'polygon': [{'x': 6.1888, 'y': 1.7331},\n", "           {'x': 6.6185, 'y': 1.7331},\n", "           {'x': 6.6233, 'y': 1.862},\n", "           {'x': 6.1935, 'y': 1.862}],\n", "          'span': {'offset': 51, 'length': 7},\n", "          'confidence': 0.984},\n", "         {'content': '#:',\n", "          'polygon': [{'x': 6.6615, 'y': 1.7331},\n", "           {'x': 6.7857, 'y': 1.7331},\n", "           {'x': 6.7904, 'y': 1.862},\n", "           {'x': 6.6663, 'y': 1.862}],\n", "          'span': {'offset': 59, 'length': 2},\n", "          'confidence': 0.961},\n", "         {'content': '545337',\n", "          'polygon': [{'x': 7.2393, 'y': 1.7378},\n", "           {'x': 7.7025, 'y': 1.7331},\n", "           {'x': 7.7025, 'y': 1.8668},\n", "           {'x': 7.2393, 'y': 1.862}],\n", "          'span': {'offset': 62, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': '26',\n", "          'polygon': [{'x': 0.339, 'y': 1.8859},\n", "           {'x': 0.4823, 'y': 1.8859},\n", "           {'x': 0.4823, 'y': 2.0339},\n", "           {'x': 0.3438, 'y': 2.0386}],\n", "          'span': {'offset': 69, 'length': 2},\n", "          'confidence': 0.997},\n", "         {'content': 'Harford',\n", "          'polygon': [{'x': 0.5205, 'y': 1.8859},\n", "           {'x': 0.9885, 'y': 1.8906},\n", "           {'x': 0.9933, 'y': 2.0291},\n", "           {'x': 0.5205, 'y': 2.0339}],\n", "          'span': {'offset': 72, 'length': 7},\n", "          'confidence': 0.995},\n", "         {'content': 'Street',\n", "          'polygon': [{'x': 1.0362, 'y': 1.8906},\n", "           {'x': 1.4135, 'y': 1.8906},\n", "           {'x': 1.4183, 'y': 2.0339},\n", "           {'x': 1.0362, 'y': 2.0291}],\n", "          'span': {'offset': 80, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': 'Date:',\n", "          'polygon': [{'x': 6.4562, 'y': 1.9336},\n", "           {'x': 6.7809, 'y': 1.9336},\n", "           {'x': 6.7809, 'y': 2.0577},\n", "           {'x': 6.4562, 'y': 2.053}],\n", "          'span': {'offset': 87, 'length': 5},\n", "          'confidence': 0.994},\n", "         {'content': '<PERSON>,',\n", "          'polygon': [{'x': 0.3295, 'y': 2.0625},\n", "           {'x': 1.084, 'y': 2.072},\n", "           {'x': 1.084, 'y': 2.2105},\n", "           {'x': 0.3295, 'y': 2.2201}],\n", "          'span': {'offset': 93, 'length': 11},\n", "          'confidence': 0.993},\n", "         {'content': 'ACT',\n", "          'polygon': [{'x': 1.1126, 'y': 2.0673},\n", "           {'x': 1.4039, 'y': 2.0673},\n", "           {'x': 1.4039, 'y': 2.2105},\n", "           {'x': 1.1126, 'y': 2.2105}],\n", "          'span': {'offset': 105, 'length': 3},\n", "          'confidence': 0.994},\n", "         {'content': '2905',\n", "          'polygon': [{'x': 1.4421, 'y': 2.0673},\n", "           {'x': 1.743, 'y': 2.0577},\n", "           {'x': 1.743, 'y': 2.2153},\n", "           {'x': 1.4421, 'y': 2.2153}],\n", "          'span': {'offset': 109, 'length': 4},\n", "          'confidence': 0.992},\n", "         {'content': '17/09/2024',\n", "          'polygon': [{'x': 7.0197, 'y': 1.9097},\n", "           {'x': 7.7121, 'y': 1.9145},\n", "           {'x': 7.7121, 'y': 2.0768},\n", "           {'x': 7.0149, 'y': 2.0816}],\n", "          'span': {'offset': 114, 'length': 10},\n", "          'confidence': 0.995},\n", "         {'content': '0423',\n", "          'polygon': [{'x': 6.8525, 'y': 2.1198},\n", "           {'x': 7.1581, 'y': 2.1198},\n", "           {'x': 7.1629, 'y': 2.2487},\n", "           {'x': 6.8573, 'y': 2.2487}],\n", "          'span': {'offset': 125, 'length': 4},\n", "          'confidence': 0.988},\n", "         {'content': '243',\n", "          'polygon': [{'x': 7.2107, 'y': 2.1198},\n", "           {'x': 7.4256, 'y': 2.1198},\n", "           {'x': 7.4256, 'y': 2.2487},\n", "           {'x': 7.2154, 'y': 2.2487}],\n", "          'span': {'offset': 130, 'length': 3},\n", "          'confidence': 0.997},\n", "         {'content': '167',\n", "          'polygon': [{'x': 7.4876, 'y': 2.115},\n", "           {'x': 7.6978, 'y': 2.115},\n", "           {'x': 7.6978, 'y': 2.2535},\n", "           {'x': 7.4876, 'y': 2.2487}],\n", "          'span': {'offset': 134, 'length': 3},\n", "          'confidence': 0.994},\n", "         {'content': '<EMAIL>',\n", "          'polygon': [{'x': 5.864, 'y': 2.3251},\n", "           {'x': 7.6548, 'y': 2.3203},\n", "           {'x': 7.6548, 'y': 2.4588},\n", "           {'x': 5.864, 'y': 2.4635}],\n", "          'span': {'offset': 138, 'length': 26},\n", "          'confidence': 0.934},\n", "         {'content': 'Patient',\n", "          'polygon': [{'x': 0.4775, 'y': 2.6068},\n", "           {'x': 0.8309, 'y': 2.6115},\n", "           {'x': 0.8357, 'y': 2.7309},\n", "           {'x': 0.4775, 'y': 2.7357}],\n", "          'span': {'offset': 165, 'length': 7},\n", "          'confidence': 0.994},\n", "         {'content': 'ID:',\n", "          'polygon': [{'x': 0.8548, 'y': 2.6115},\n", "           {'x': 1.0124, 'y': 2.6115},\n", "           {'x': 1.0171, 'y': 2.7309},\n", "           {'x': 0.8548, 'y': 2.7309}],\n", "          'span': {'offset': 173, 'length': 3},\n", "          'confidence': 0.993},\n", "         {'content': '83692',\n", "          'polygon': [{'x': 1.0362, 'y': 2.6115},\n", "           {'x': 1.3514, 'y': 2.6068},\n", "           {'x': 1.3514, 'y': 2.7261},\n", "           {'x': 1.041, 'y': 2.7309}],\n", "          'span': {'offset': 177, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'Species:',\n", "          'polygon': [{'x': 2.9989, 'y': 2.6115},\n", "           {'x': 3.4334, 'y': 2.6068},\n", "           {'x': 3.4334, 'y': 2.7357},\n", "           {'x': 2.9989, 'y': 2.7405}],\n", "          'span': {'offset': 183, 'length': 8},\n", "          'confidence': 0.992},\n", "         {'content': 'Canine',\n", "          'polygon': [{'x': 3.4573, 'y': 2.6068},\n", "           {'x': 3.8202, 'y': 2.602},\n", "           {'x': 3.8202, 'y': 2.7357},\n", "           {'x': 3.4621, 'y': 2.7357}],\n", "          'span': {'offset': 192, 'length': 6},\n", "          'confidence': 0.993},\n", "         {'content': 'Weight:',\n", "          'polygon': [{'x': 5.2576, 'y': 2.6115},\n", "           {'x': 5.6826, 'y': 2.602},\n", "           {'x': 5.6874, 'y': 2.7452},\n", "           {'x': 5.2624, 'y': 2.7309}],\n", "          'span': {'offset': 199, 'length': 7},\n", "          'confidence': 0.994},\n", "         {'content': '26.00',\n", "          'polygon': [{'x': 5.7829, 'y': 2.602},\n", "           {'x': 6.0742, 'y': 2.5924},\n", "           {'x': 6.0742, 'y': 2.7548},\n", "           {'x': 5.7876, 'y': 2.75}],\n", "          'span': {'offset': 207, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'kilograms',\n", "          'polygon': [{'x': 6.1649, 'y': 2.5877},\n", "           {'x': 6.6806, 'y': 2.5924},\n", "           {'x': 6.6806, 'y': 2.7548},\n", "           {'x': 6.1649, 'y': 2.7548}],\n", "          'span': {'offset': 213, 'length': 9},\n", "          'confidence': 0.989},\n", "         {'content': 'Patient',\n", "          'polygon': [{'x': 0.2865, 'y': 2.8168},\n", "           {'x': 0.6351, 'y': 2.8168},\n", "           {'x': 0.6399, 'y': 2.941},\n", "           {'x': 0.2913, 'y': 2.941}],\n", "          'span': {'offset': 223, 'length': 7},\n", "          'confidence': 0.994},\n", "         {'content': 'Name:',\n", "          'polygon': [{'x': 0.6638, 'y': 2.8168},\n", "           {'x': 1.0028, 'y': 2.8168},\n", "           {'x': 1.0076, 'y': 2.9457},\n", "           {'x': 0.6638, 'y': 2.941}],\n", "          'span': {'offset': 231, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': '<PERSON>',\n", "          'polygon': [{'x': 1.0315, 'y': 2.8168},\n", "           {'x': 1.3896, 'y': 2.8216},\n", "           {'x': 1.3944, 'y': 2.9505},\n", "           {'x': 1.0315, 'y': 2.9457}],\n", "          'span': {'offset': 237, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': 'Breed:',\n", "          'polygon': [{'x': 3.0944, 'y': 2.8216},\n", "           {'x': 3.4287, 'y': 2.8216},\n", "           {'x': 3.4287, 'y': 2.9553},\n", "           {'x': 3.0944, 'y': 2.9505}],\n", "          'span': {'offset': 244, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': 'Bulldog,',\n", "          'polygon': [{'x': 3.4525, 'y': 2.8216},\n", "           {'x': 3.8728, 'y': 2.8216},\n", "           {'x': 3.8728, 'y': 2.9601},\n", "           {'x': 3.4573, 'y': 2.9553}],\n", "          'span': {'offset': 251, 'length': 8},\n", "          'confidence': 0.993},\n", "         {'content': 'British',\n", "          'polygon': [{'x': 3.8966, 'y': 2.8216},\n", "           {'x': 4.207, 'y': 2.8216},\n", "           {'x': 4.207, 'y': 2.9601},\n", "           {'x': 3.8966, 'y': 2.9601}],\n", "          'span': {'offset': 260, 'length': 7},\n", "          'confidence': 0.993},\n", "         {'content': 'Birthday:',\n", "          'polygon': [{'x': 5.2051, 'y': 2.8264},\n", "           {'x': 5.6396, 'y': 2.8264},\n", "           {'x': 5.6444, 'y': 2.9505},\n", "           {'x': 5.2051, 'y': 2.9505}],\n", "          'span': {'offset': 268, 'length': 9},\n", "          'confidence': 0.991},\n", "         {'content': '04/01/2024',\n", "          'polygon': [{'x': 5.6683, 'y': 2.8264},\n", "           {'x': 6.2365, 'y': 2.8264},\n", "           {'x': 6.2365, 'y': 2.9505},\n", "           {'x': 5.6683, 'y': 2.9505}],\n", "          'span': {'offset': 278, 'length': 10},\n", "          'confidence': 0.994},\n", "         {'content': 'Sex:',\n", "          'polygon': [{'x': 6.5374, 'y': 2.8264},\n", "           {'x': 6.757, 'y': 2.8264},\n", "           {'x': 6.7618, 'y': 2.9457},\n", "           {'x': 6.5374, 'y': 2.941}],\n", "          'span': {'offset': 289, 'length': 4},\n", "          'confidence': 0.992},\n", "         {'content': 'Male',\n", "          'polygon': [{'x': 6.7809, 'y': 2.8264},\n", "           {'x': 7.0244, 'y': 2.8264},\n", "           {'x': 7.0244, 'y': 2.9457},\n", "           {'x': 6.7809, 'y': 2.9457}],\n", "          'span': {'offset': 294, 'length': 4},\n", "          'confidence': 0.993},\n", "         {'content': 'Description',\n", "          'polygon': [{'x': 1.3371, 'y': 3.0508},\n", "           {'x': 2.0916, 'y': 3.0603},\n", "           {'x': 2.0916, 'y': 3.2036},\n", "           {'x': 1.3419, 'y': 3.1988}],\n", "          'span': {'offset': 299, 'length': 11},\n", "          'confidence': 0.993},\n", "         {'content': 'Staff',\n", "          'polygon': [{'x': 3.9587, 'y': 3.0508},\n", "           {'x': 4.2643, 'y': 3.046},\n", "           {'x': 4.2596, 'y': 3.2131},\n", "           {'x': 3.9539, 'y': 3.2131}],\n", "          'span': {'offset': 311, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'Name',\n", "          'polygon': [{'x': 4.293, 'y': 3.046},\n", "           {'x': 4.6846, 'y': 3.0508},\n", "           {'x': 4.6846, 'y': 3.2131},\n", "           {'x': 4.293, 'y': 3.2131}],\n", "          'span': {'offset': 317, 'length': 4},\n", "          'confidence': 0.993},\n", "         {'content': 'Quantity',\n", "          'polygon': [{'x': 5.6969, 'y': 3.0508},\n", "           {'x': 6.2652, 'y': 3.0508},\n", "           {'x': 6.2604, 'y': 3.2131},\n", "           {'x': 5.6969, 'y': 3.2131}],\n", "          'span': {'offset': 322, 'length': 8},\n", "          'confidence': 0.995},\n", "         {'content': 'Total',\n", "          'polygon': [{'x': 6.8478, 'y': 3.0508},\n", "           {'x': 7.1772, 'y': 3.0508},\n", "           {'x': 7.1772, 'y': 3.1892},\n", "           {'x': 6.8478, 'y': 3.194}],\n", "          'span': {'offset': 331, 'length': 5},\n", "          'confidence': 0.993},\n", "         {'content': '17/09/2024',\n", "          'polygon': [{'x': 0.2961, 'y': 3.2561},\n", "           {'x': 0.9742, 'y': 3.2561},\n", "           {'x': 0.9742, 'y': 3.3993},\n", "           {'x': 0.2961, 'y': 3.3945}],\n", "          'span': {'offset': 337, 'length': 10},\n", "          'confidence': 0.995},\n", "         {'content': 'Consultation',\n", "          'polygon': [{'x': 1.3323, 'y': 3.2561},\n", "           {'x': 2.0963, 'y': 3.2561},\n", "           {'x': 2.0963, 'y': 3.3993},\n", "           {'x': 1.3323, 'y': 3.3898}],\n", "          'span': {'offset': 348, 'length': 12},\n", "          'confidence': 0.99},\n", "         {'content': 'Dr.',\n", "          'polygon': [{'x': 3.9396, 'y': 3.2465},\n", "           {'x': 4.1306, 'y': 3.2513},\n", "           {'x': 4.1354, 'y': 3.4089},\n", "           {'x': 3.9444, 'y': 3.4089}],\n", "          'span': {'offset': 361, 'length': 3},\n", "          'confidence': 0.995},\n", "         {'content': '<PERSON>',\n", "          'polygon': [{'x': 4.164, 'y': 3.2513},\n", "           {'x': 4.5031, 'y': 3.2513},\n", "           {'x': 4.5079, 'y': 3.4136},\n", "           {'x': 4.164, 'y': 3.4089}],\n", "          'span': {'offset': 365, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'Brown',\n", "          'polygon': [{'x': 4.5365, 'y': 3.2513},\n", "           {'x': 4.9281, 'y': 3.2465},\n", "           {'x': 4.9329, 'y': 3.4089},\n", "           {'x': 4.5365, 'y': 3.4136}],\n", "          'span': {'offset': 371, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': '1.00',\n", "          'polygon': [{'x': 5.8593, 'y': 3.2561},\n", "           {'x': 6.1219, 'y': 3.2561},\n", "           {'x': 6.1219, 'y': 3.3898},\n", "           {'x': 5.8593, 'y': 3.3898}],\n", "          'span': {'offset': 377, 'length': 4},\n", "          'confidence': 0.993},\n", "         {'content': '$104.00',\n", "          'polygon': [{'x': 6.9051, 'y': 3.2609},\n", "           {'x': 7.4065, 'y': 3.2609},\n", "           {'x': 7.4112, 'y': 3.3993},\n", "           {'x': 6.9098, 'y': 3.3945}],\n", "          'span': {'offset': 382, 'length': 7},\n", "          'confidence': 0.995},\n", "         {'content': 'Cephalexin',\n", "          'polygon': [{'x': 1.3323, 'y': 3.4232},\n", "           {'x': 2.0295, 'y': 3.4232},\n", "           {'x': 2.0295, 'y': 3.5807},\n", "           {'x': 1.3371, 'y': 3.5712}],\n", "          'span': {'offset': 390, 'length': 10},\n", "          'confidence': 0.991},\n", "         {'content': '200mg',\n", "          'polygon': [{'x': 2.0725, 'y': 3.4232},\n", "           {'x': 2.4975, 'y': 3.4184},\n", "           {'x': 2.4975, 'y': 3.5807},\n", "           {'x': 2.0725, 'y': 3.5807}],\n", "          'span': {'offset': 401, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'Beef',\n", "          'polygon': [{'x': 2.5261, 'y': 3.4184},\n", "           {'x': 2.8317, 'y': 3.4136},\n", "           {'x': 2.8317, 'y': 3.5807},\n", "           {'x': 2.5261, 'y': 3.5807}],\n", "          'span': {'offset': 407, 'length': 4},\n", "          'confidence': 0.984},\n", "         {'content': '28.00',\n", "          'polygon': [{'x': 5.7829, 'y': 3.4232},\n", "           {'x': 6.1124, 'y': 3.4232},\n", "           {'x': 6.1124, 'y': 3.5569},\n", "           {'x': 5.7829, 'y': 3.5569}],\n", "          'span': {'offset': 412, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': '$62.80',\n", "          'polygon': [{'x': 6.991, 'y': 3.4327},\n", "           {'x': 7.4017, 'y': 3.4232},\n", "           {'x': 7.4065, 'y': 3.5664},\n", "           {'x': 6.991, 'y': 3.5616}],\n", "          'span': {'offset': 418, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': 'Antinol',\n", "          'polygon': [{'x': 1.3323, 'y': 3.5903},\n", "           {'x': 1.7573, 'y': 3.5903},\n", "           {'x': 1.7621, 'y': 3.7383},\n", "           {'x': 1.3371, 'y': 3.7287}],\n", "          'span': {'offset': 425, 'length': 7},\n", "          'confidence': 0.994},\n", "         {'content': 'Plus',\n", "          'polygon': [{'x': 1.7907, 'y': 3.5903},\n", "           {'x': 2.0772, 'y': 3.5903},\n", "           {'x': 2.0772, 'y': 3.7431},\n", "           {'x': 1.7907, 'y': 3.7383}],\n", "          'span': {'offset': 433, 'length': 4},\n", "          'confidence': 0.991},\n", "         {'content': 'Dog',\n", "          'polygon': [{'x': 2.1059, 'y': 3.5903},\n", "           {'x': 2.3638, 'y': 3.5855},\n", "           {'x': 2.3638, 'y': 3.7431},\n", "           {'x': 2.1107, 'y': 3.7431}],\n", "          'span': {'offset': 438, 'length': 3},\n", "          'confidence': 0.989},\n", "         {'content': \"60's\",\n", "          'polygon': [{'x': 2.3924, 'y': 3.5855},\n", "           {'x': 2.6551, 'y': 3.5855},\n", "           {'x': 2.6551, 'y': 3.7478},\n", "           {'x': 2.3972, 'y': 3.7431}],\n", "          'span': {'offset': 442, 'length': 4},\n", "          'confidence': 0.982},\n", "         {'content': '1.00',\n", "          'polygon': [{'x': 5.8593, 'y': 3.5951},\n", "           {'x': 6.1124, 'y': 3.5951},\n", "           {'x': 6.1076, 'y': 3.724},\n", "           {'x': 5.8593, 'y': 3.7192}],\n", "          'span': {'offset': 447, 'length': 4},\n", "          'confidence': 0.993},\n", "         {'content': '$59.15',\n", "          'polygon': [{'x': 6.9815, 'y': 3.5903},\n", "           {'x': 7.4065, 'y': 3.5855},\n", "           {'x': 7.4065, 'y': 3.7383},\n", "           {'x': 6.9862, 'y': 3.7335}],\n", "          'span': {'offset': 452, 'length': 6},\n", "          'confidence': 0.993},\n", "         {'content': 'Fungassay-',\n", "          'polygon': [{'x': 1.3323, 'y': 3.7574},\n", "           {'x': 2.0581, 'y': 3.7574},\n", "           {'x': 2.0581, 'y': 3.9149},\n", "           {'x': 1.3323, 'y': 3.9149}],\n", "          'span': {'offset': 459, 'length': 10},\n", "          'confidence': 0.994},\n", "         {'content': 'Fungal',\n", "          'polygon': [{'x': 2.0868, 'y': 3.7574},\n", "           {'x': 2.5213, 'y': 3.7574},\n", "           {'x': 2.5213, 'y': 3.9149},\n", "           {'x': 2.0868, 'y': 3.9149}],\n", "          'span': {'offset': 470, 'length': 6},\n", "          'confidence': 0.994},\n", "         {'content': 'culture',\n", "          'polygon': [{'x': 2.5548, 'y': 3.7574},\n", "           {'x': 2.9798, 'y': 3.7526},\n", "           {'x': 2.9798, 'y': 3.9102},\n", "           {'x': 2.5548, 'y': 3.9149}],\n", "          'span': {'offset': 477, 'length': 7},\n", "          'confidence': 0.994},\n", "         {'content': '1.00',\n", "          'polygon': [{'x': 5.8593, 'y': 3.7622},\n", "           {'x': 6.1171, 'y': 3.7622},\n", "           {'x': 6.1171, 'y': 3.8911},\n", "           {'x': 5.8593, 'y': 3.8863}],\n", "          'span': {'offset': 485, 'length': 4},\n", "          'confidence': 0.993},\n", "         {'content': '$26.75',\n", "          'polygon': [{'x': 6.9719, 'y': 3.7431},\n", "           {'x': 7.416, 'y': 3.7431},\n", "           {'x': 7.416, 'y': 3.9006},\n", "           {'x': 6.9767, 'y': 3.9006}],\n", "          'span': {'offset': 490, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': 'Patient',\n", "          'polygon': [{'x': 5.057, 'y': 3.9531},\n", "           {'x': 5.5346, 'y': 3.9627},\n", "           {'x': 5.5346, 'y': 4.1155},\n", "           {'x': 5.057, 'y': 4.1202}],\n", "          'span': {'offset': 497, 'length': 7},\n", "          'confidence': 0.995},\n", "         {'content': 'Subtotal:',\n", "          'polygon': [{'x': 5.568, 'y': 3.9627},\n", "           {'x': 6.1601, 'y': 3.9627},\n", "           {'x': 6.1553, 'y': 4.1107},\n", "           {'x': 5.568, 'y': 4.1155}],\n", "          'span': {'offset': 505, 'length': 9},\n", "          'confidence': 0.993},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9051, 'y': 3.9674},\n", "           {'x': 7.4065, 'y': 3.9627},\n", "           {'x': 7.4065, 'y': 4.1155},\n", "           {'x': 6.9098, 'y': 4.1107}],\n", "          'span': {'offset': 515, 'length': 7},\n", "          'confidence': 0.995},\n", "         {'content': 'Additional',\n", "          'polygon': [{'x': 0.2722, 'y': 4.2014},\n", "           {'x': 0.8787, 'y': 4.2062},\n", "           {'x': 0.8787, 'y': 4.3542},\n", "           {'x': 0.277, 'y': 4.3589}],\n", "          'span': {'offset': 523, 'length': 10},\n", "          'confidence': 0.993},\n", "         {'content': 'Information',\n", "          'polygon': [{'x': 0.9073, 'y': 4.2062},\n", "           {'x': 1.6093, 'y': 4.2062},\n", "           {'x': 1.6093, 'y': 4.3542},\n", "           {'x': 0.9073, 'y': 4.3542}],\n", "          'span': {'offset': 534, 'length': 11},\n", "          'confidence': 0.983},\n", "         {'content': 'There',\n", "          'polygon': [{'x': 0.2722, 'y': 4.3828},\n", "           {'x': 0.616, 'y': 4.3828},\n", "           {'x': 0.616, 'y': 4.5308},\n", "           {'x': 0.2722, 'y': 4.526}],\n", "          'span': {'offset': 546, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'has',\n", "          'polygon': [{'x': 0.6447, 'y': 4.3828},\n", "           {'x': 0.8834, 'y': 4.3828},\n", "           {'x': 0.8834, 'y': 4.5308},\n", "           {'x': 0.6447, 'y': 4.5308}],\n", "          'span': {'offset': 552, 'length': 3},\n", "          'confidence': 0.995},\n", "         {'content': 'been',\n", "          'polygon': [{'x': 0.9169, 'y': 4.3828},\n", "           {'x': 1.2272, 'y': 4.3828},\n", "           {'x': 1.2272, 'y': 4.5356},\n", "           {'x': 0.9169, 'y': 4.5356}],\n", "          'span': {'offset': 556, 'length': 4},\n", "          'confidence': 0.991},\n", "         {'content': 'a',\n", "          'polygon': [{'x': 1.2702, 'y': 4.3828},\n", "           {'x': 1.3466, 'y': 4.3828},\n", "           {'x': 1.3466, 'y': 4.5356},\n", "           {'x': 1.2702, 'y': 4.5356}],\n", "          'span': {'offset': 561, 'length': 1},\n", "          'confidence': 0.995},\n", "         {'content': 'few',\n", "          'polygon': [{'x': 1.3801, 'y': 4.3828},\n", "           {'x': 1.5854, 'y': 4.3828},\n", "           {'x': 1.5854, 'y': 4.5404},\n", "           {'x': 1.3801, 'y': 4.5356}],\n", "          'span': {'offset': 563, 'length': 3},\n", "          'confidence': 0.995},\n", "         {'content': 'cases',\n", "          'polygon': [{'x': 1.6379, 'y': 4.3828},\n", "           {'x': 1.9913, 'y': 4.378},\n", "           {'x': 1.9913, 'y': 4.5404},\n", "           {'x': 1.6379, 'y': 4.5404}],\n", "          'span': {'offset': 567, 'length': 5},\n", "          'confidence': 0.993},\n", "         {'content': 'in',\n", "          'polygon': [{'x': 2.0247, 'y': 4.378},\n", "           {'x': 2.1537, 'y': 4.378},\n", "           {'x': 2.1537, 'y': 4.5451},\n", "           {'x': 2.0247, 'y': 4.5404}],\n", "          'span': {'offset': 573, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': 'Canberra',\n", "          'polygon': [{'x': 2.1871, 'y': 4.378},\n", "           {'x': 2.7553, 'y': 4.378},\n", "           {'x': 2.7553, 'y': 4.5451},\n", "           {'x': 2.1871, 'y': 4.5451}],\n", "          'span': {'offset': 576, 'length': 8},\n", "          'confidence': 0.994},\n", "         {'content': 'where',\n", "          'polygon': [{'x': 2.7888, 'y': 4.378},\n", "           {'x': 3.1756, 'y': 4.378},\n", "           {'x': 3.1756, 'y': 4.5499},\n", "           {'x': 2.7888, 'y': 4.5451}],\n", "          'span': {'offset': 585, 'length': 5},\n", "          'confidence': 0.994},\n", "         {'content': 'dogs',\n", "          'polygon': [{'x': 3.209, 'y': 4.378},\n", "           {'x': 3.5098, 'y': 4.378},\n", "           {'x': 3.5098, 'y': 4.5499},\n", "           {'x': 3.209, 'y': 4.5499}],\n", "          'span': {'offset': 591, 'length': 4},\n", "          'confidence': 0.991},\n", "         {'content': 'were',\n", "          'polygon': [{'x': 3.5433, 'y': 4.378},\n", "           {'x': 3.8537, 'y': 4.378},\n", "           {'x': 3.8537, 'y': 4.5499},\n", "           {'x': 3.5433, 'y': 4.5499}],\n", "          'span': {'offset': 596, 'length': 4},\n", "          'confidence': 0.989},\n", "         {'content': 'presented',\n", "          'polygon': [{'x': 3.8871, 'y': 4.378},\n", "           {'x': 4.4983, 'y': 4.378},\n", "           {'x': 4.4983, 'y': 4.5499},\n", "           {'x': 3.8871, 'y': 4.5499}],\n", "          'span': {'offset': 601, 'length': 9},\n", "          'confidence': 0.993},\n", "         {'content': 'with',\n", "          'polygon': [{'x': 4.5317, 'y': 4.378},\n", "           {'x': 4.7896, 'y': 4.378},\n", "           {'x': 4.7896, 'y': 4.5499},\n", "           {'x': 4.5317, 'y': 4.5499}],\n", "          'span': {'offset': 611, 'length': 4},\n", "          'confidence': 0.989},\n", "         {'content': 'signs',\n", "          'polygon': [{'x': 4.823, 'y': 4.378},\n", "           {'x': 5.1573, 'y': 4.378},\n", "           {'x': 5.1573, 'y': 4.5499},\n", "           {'x': 4.823, 'y': 4.5499}],\n", "          'span': {'offset': 616, 'length': 5},\n", "          'confidence': 0.993},\n", "         {'content': 'of',\n", "          'polygon': [{'x': 5.1907, 'y': 4.378},\n", "           {'x': 5.3053, 'y': 4.378},\n", "           {'x': 5.3053, 'y': 4.5499},\n", "           {'x': 5.1907, 'y': 4.5499}],\n", "          'span': {'offset': 622, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': 'tick',\n", "          'polygon': [{'x': 5.3388, 'y': 4.378},\n", "           {'x': 5.5537, 'y': 4.378},\n", "           {'x': 5.5537, 'y': 4.5499},\n", "           {'x': 5.3388, 'y': 4.5499}],\n", "          'span': {'offset': 625, 'length': 4},\n", "          'confidence': 0.991},\n", "         {'content': 'paralysis',\n", "          'polygon': [{'x': 5.5871, 'y': 4.378},\n", "           {'x': 6.1362, 'y': 4.378},\n", "           {'x': 6.1362, 'y': 4.5499},\n", "           {'x': 5.5871, 'y': 4.5499}],\n", "          'span': {'offset': 630, 'length': 9},\n", "          'confidence': 0.993},\n", "         {'content': 'without',\n", "          'polygon': [{'x': 6.1697, 'y': 4.378},\n", "           {'x': 6.6185, 'y': 4.3828},\n", "           {'x': 6.6185, 'y': 4.5499},\n", "           {'x': 6.1697, 'y': 4.5499}],\n", "          'span': {'offset': 640, 'length': 7},\n", "          'confidence': 0.993},\n", "         {'content': 'a',\n", "          'polygon': [{'x': 6.652, 'y': 4.3828},\n", "           {'x': 6.7188, 'y': 4.3828},\n", "           {'x': 6.7188, 'y': 4.5499},\n", "           {'x': 6.652, 'y': 4.5499}],\n", "          'span': {'offset': 648, 'length': 1},\n", "          'confidence': 0.996},\n", "         {'content': 'history',\n", "          'polygon': [{'x': 6.7475, 'y': 4.3828},\n", "           {'x': 7.1677, 'y': 4.3828},\n", "           {'x': 7.1677, 'y': 4.5451},\n", "           {'x': 6.7475, 'y': 4.5499}],\n", "          'span': {'offset': 650, 'length': 7},\n", "          'confidence': 0.993},\n", "         {'content': 'of',\n", "          'polygon': [{'x': 7.2011, 'y': 4.3828},\n", "           {'x': 7.3348, 'y': 4.3828},\n", "           {'x': 7.3348, 'y': 4.5451},\n", "           {'x': 7.2011, 'y': 4.5451}],\n", "          'span': {'offset': 658, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': 'travel',\n", "          'polygon': [{'x': 0.2579, 'y': 4.5451},\n", "           {'x': 0.6065, 'y': 4.5499},\n", "           {'x': 0.6112, 'y': 4.6884},\n", "           {'x': 0.2579, 'y': 4.6931}],\n", "          'span': {'offset': 661, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': 'to',\n", "          'polygon': [{'x': 0.6351, 'y': 4.5499},\n", "           {'x': 0.7449, 'y': 4.5499},\n", "           {'x': 0.7497, 'y': 4.6884},\n", "           {'x': 0.6351, 'y': 4.6884}],\n", "          'span': {'offset': 668, 'length': 2},\n", "          'confidence': 0.997},\n", "         {'content': 'the',\n", "          'polygon': [{'x': 0.7831, 'y': 4.5499},\n", "           {'x': 0.9789, 'y': 4.5499},\n", "           {'x': 0.9789, 'y': 4.6884},\n", "           {'x': 0.7879, 'y': 4.6884}],\n", "          'span': {'offset': 671, 'length': 3},\n", "          'confidence': 0.994},\n", "         {'content': 'coast.',\n", "          'polygon': [{'x': 1.0267, 'y': 4.5499},\n", "           {'x': 1.3992, 'y': 4.5499},\n", "           {'x': 1.3992, 'y': 4.6884},\n", "           {'x': 1.0267, 'y': 4.6884}],\n", "          'span': {'offset': 675, 'length': 6},\n", "          'confidence': 0.994},\n", "         {'content': 'We',\n", "          'polygon': [{'x': 0.2579, 'y': 4.7027},\n", "           {'x': 0.4632, 'y': 4.7027},\n", "           {'x': 0.468, 'y': 4.8507},\n", "           {'x': 0.2626, 'y': 4.8507}],\n", "          'span': {'offset': 682, 'length': 2},\n", "          'confidence': 0.997},\n", "         {'content': 'recommend',\n", "          'polygon': [{'x': 0.4966, 'y': 4.7027},\n", "           {'x': 1.2511, 'y': 4.7027},\n", "           {'x': 1.2511, 'y': 4.8555},\n", "           {'x': 0.5014, 'y': 4.8507}],\n", "          'span': {'offset': 685, 'length': 9},\n", "          'confidence': 0.994},\n", "         {'content': 'having',\n", "          'polygon': [{'x': 1.2846, 'y': 4.7027},\n", "           {'x': 1.7, 'y': 4.7027},\n", "           {'x': 1.7048, 'y': 4.8555},\n", "           {'x': 1.2846, 'y': 4.8555}],\n", "          'span': {'offset': 695, 'length': 6},\n", "          'confidence': 0.994},\n", "         {'content': 'your',\n", "          'polygon': [{'x': 1.7334, 'y': 4.7027},\n", "           {'x': 1.9961, 'y': 4.7027},\n", "           {'x': 1.9961, 'y': 4.8602},\n", "           {'x': 1.7334, 'y': 4.8555}],\n", "          'span': {'offset': 702, 'length': 4},\n", "          'confidence': 0.993},\n", "         {'content': 'dog',\n", "          'polygon': [{'x': 2.0247, 'y': 4.7027},\n", "           {'x': 2.2683, 'y': 4.6979},\n", "           {'x': 2.2683, 'y': 4.8602},\n", "           {'x': 2.0295, 'y': 4.8602}],\n", "          'span': {'offset': 707, 'length': 3},\n", "          'confidence': 0.974},\n", "         {'content': 'on',\n", "          'polygon': [{'x': 2.2969, 'y': 4.6979},\n", "           {'x': 2.4545, 'y': 4.6979},\n", "           {'x': 2.4593, 'y': 4.8602},\n", "           {'x': 2.3017, 'y': 4.8602}],\n", "          'span': {'offset': 711, 'length': 2},\n", "          'confidence': 0.997},\n", "         {'content': 'prevention',\n", "          'polygon': [{'x': 2.4879, 'y': 4.6979},\n", "           {'x': 3.1374, 'y': 4.6979},\n", "           {'x': 3.1374, 'y': 4.8602},\n", "           {'x': 2.4879, 'y': 4.8602}],\n", "          'span': {'offset': 714, 'length': 10},\n", "          'confidence': 0.991},\n", "         {'content': 'against',\n", "          'polygon': [{'x': 3.1803, 'y': 4.6979},\n", "           {'x': 3.6197, 'y': 4.6979},\n", "           {'x': 3.6197, 'y': 4.8602},\n", "           {'x': 3.1803, 'y': 4.8602}],\n", "          'span': {'offset': 725, 'length': 7},\n", "          'confidence': 0.994},\n", "         {'content': 'ticks',\n", "          'polygon': [{'x': 3.6483, 'y': 4.6979},\n", "           {'x': 3.9539, 'y': 4.6979},\n", "           {'x': 3.9539, 'y': 4.8602},\n", "           {'x': 3.6531, 'y': 4.8602}],\n", "          'span': {'offset': 733, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'at',\n", "          'polygon': [{'x': 3.9874, 'y': 4.6979},\n", "           {'x': 4.0876, 'y': 4.6979},\n", "           {'x': 4.0924, 'y': 4.8602},\n", "           {'x': 3.9874, 'y': 4.8602}],\n", "          'span': {'offset': 739, 'length': 2},\n", "          'confidence': 0.996},\n", "         {'content': 'least',\n", "          'polygon': [{'x': 4.1211, 'y': 4.6979},\n", "           {'x': 4.4267, 'y': 4.6979},\n", "           {'x': 4.4267, 'y': 4.865},\n", "           {'x': 4.1211, 'y': 4.8602}],\n", "          'span': {'offset': 742, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'for',\n", "          'polygon': [{'x': 4.4553, 'y': 4.6979},\n", "           {'x': 4.6225, 'y': 4.6979},\n", "           {'x': 4.6272, 'y': 4.865},\n", "           {'x': 4.4553, 'y': 4.865}],\n", "          'span': {'offset': 748, 'length': 3},\n", "          'confidence': 0.997},\n", "         {'content': 'the',\n", "          'polygon': [{'x': 4.6559, 'y': 4.6979},\n", "           {'x': 4.866, 'y': 4.6979},\n", "           {'x': 4.866, 'y': 4.865},\n", "           {'x': 4.6559, 'y': 4.865}],\n", "          'span': {'offset': 752, 'length': 3},\n", "          'confidence': 0.997},\n", "         {'content': 'warmer',\n", "          'polygon': [{'x': 4.8947, 'y': 4.6979},\n", "           {'x': 5.3579, 'y': 4.6979},\n", "           {'x': 5.3579, 'y': 4.865},\n", "           {'x': 4.8994, 'y': 4.865}],\n", "          'span': {'offset': 756, 'length': 6},\n", "          'confidence': 0.992},\n", "         {'content': 'months',\n", "          'polygon': [{'x': 5.3865, 'y': 4.6979},\n", "           {'x': 5.8593, 'y': 4.6979},\n", "           {'x': 5.8593, 'y': 4.865},\n", "           {'x': 5.3913, 'y': 4.865}],\n", "          'span': {'offset': 763, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': 'of',\n", "          'polygon': [{'x': 5.8927, 'y': 4.6979},\n", "           {'x': 6.0073, 'y': 4.6979},\n", "           {'x': 6.0073, 'y': 4.865},\n", "           {'x': 5.8927, 'y': 4.865}],\n", "          'span': {'offset': 770, 'length': 2},\n", "          'confidence': 0.996},\n", "         {'content': 'the',\n", "          'polygon': [{'x': 6.036, 'y': 4.6979},\n", "           {'x': 6.2461, 'y': 4.6979},\n", "           {'x': 6.2461, 'y': 4.8602},\n", "           {'x': 6.0407, 'y': 4.8602}],\n", "          'span': {'offset': 773, 'length': 3},\n", "          'confidence': 0.997},\n", "         {'content': 'year,',\n", "          'polygon': [{'x': 6.2795, 'y': 4.6979},\n", "           {'x': 6.5947, 'y': 4.6979},\n", "           {'x': 6.5947, 'y': 4.8602},\n", "           {'x': 6.2795, 'y': 4.8602}],\n", "          'span': {'offset': 777, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'even',\n", "          'polygon': [{'x': 6.6233, 'y': 4.6979},\n", "           {'x': 6.9194, 'y': 4.6979},\n", "           {'x': 6.9194, 'y': 4.8602},\n", "           {'x': 6.6233, 'y': 4.8602}],\n", "          'span': {'offset': 783, 'length': 4},\n", "          'confidence': 0.991},\n", "         {'content': 'if',\n", "          'polygon': [{'x': 6.948, 'y': 4.6979},\n", "           {'x': 7.034, 'y': 4.6979},\n", "           {'x': 7.034, 'y': 4.8602},\n", "           {'x': 6.948, 'y': 4.8602}],\n", "          'span': {'offset': 788, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': 'you',\n", "          'polygon': [{'x': 7.0626, 'y': 4.6979},\n", "           {'x': 7.2966, 'y': 4.6979},\n", "           {'x': 7.2966, 'y': 4.8602},\n", "           {'x': 7.0626, 'y': 4.8602}],\n", "          'span': {'offset': 791, 'length': 3},\n", "          'confidence': 0.997},\n", "         {'content': 'are',\n", "          'polygon': [{'x': 7.3253, 'y': 4.6979},\n", "           {'x': 7.5258, 'y': 4.6979},\n", "           {'x': 7.5258, 'y': 4.8602},\n", "           {'x': 7.3253, 'y': 4.8602}],\n", "          'span': {'offset': 795, 'length': 3},\n", "          'confidence': 0.995},\n", "         {'content': 'not',\n", "          'polygon': [{'x': 0.2579, 'y': 4.865},\n", "           {'x': 0.4489, 'y': 4.865},\n", "           {'x': 0.4584, 'y': 5.013},\n", "           {'x': 0.2626, 'y': 5.013}],\n", "          'span': {'offset': 799, 'length': 3},\n", "          'confidence': 0.995},\n", "         {'content': 'planning',\n", "          'polygon': [{'x': 0.4823, 'y': 4.865},\n", "           {'x': 1.0028, 'y': 4.8602},\n", "           {'x': 1.0076, 'y': 5.0178},\n", "           {'x': 0.4871, 'y': 5.013}],\n", "          'span': {'offset': 803, 'length': 8},\n", "          'confidence': 0.994},\n", "         {'content': 'to',\n", "          'polygon': [{'x': 1.0362, 'y': 4.8602},\n", "           {'x': 1.1556, 'y': 4.8602},\n", "           {'x': 1.1652, 'y': 5.0178},\n", "           {'x': 1.041, 'y': 5.0178}],\n", "          'span': {'offset': 812, 'length': 2},\n", "          'confidence': 0.997},\n", "         {'content': 'take',\n", "          'polygon': [{'x': 1.189, 'y': 4.8602},\n", "           {'x': 1.466, 'y': 4.8602},\n", "           {'x': 1.4708, 'y': 5.0178},\n", "           {'x': 1.1938, 'y': 5.0178}],\n", "          'span': {'offset': 815, 'length': 4},\n", "          'confidence': 0.993},\n", "         {'content': 'your',\n", "          'polygon': [{'x': 1.4947, 'y': 4.8602},\n", "           {'x': 1.7716, 'y': 4.8602},\n", "           {'x': 1.7764, 'y': 5.0178},\n", "           {'x': 1.4994, 'y': 5.0178}],\n", "          'span': {'offset': 820, 'length': 4},\n", "          'confidence': 0.99},\n", "         {'content': 'dog',\n", "          'polygon': [{'x': 1.8051, 'y': 4.8602},\n", "           {'x': 2.039, 'y': 4.8602},\n", "           {'x': 2.0438, 'y': 5.0178},\n", "           {'x': 1.8098, 'y': 5.0178}],\n", "          'span': {'offset': 825, 'length': 3},\n", "          'confidence': 0.996},\n", "         {'content': 'down',\n", "          'polygon': [{'x': 2.0725, 'y': 4.8602},\n", "           {'x': 2.4067, 'y': 4.8555},\n", "           {'x': 2.4115, 'y': 5.0178},\n", "           {'x': 2.0725, 'y': 5.0178}],\n", "          'span': {'offset': 829, 'length': 4},\n", "          'confidence': 0.992},\n", "         {'content': 'the',\n", "          'polygon': [{'x': 2.4402, 'y': 4.8555},\n", "           {'x': 2.636, 'y': 4.8555},\n", "           {'x': 2.636, 'y': 5.0178},\n", "           {'x': 2.4449, 'y': 5.0178}],\n", "          'span': {'offset': 834, 'length': 3},\n", "          'confidence': 0.995},\n", "         {'content': 'coast,',\n", "          'polygon': [{'x': 2.6742, 'y': 4.8555},\n", "           {'x': 3.0562, 'y': 4.8555},\n", "           {'x': 3.0562, 'y': 5.0178},\n", "           {'x': 2.6789, 'y': 5.0178}],\n", "          'span': {'offset': 838, 'length': 6},\n", "          'confidence': 0.993},\n", "         {'content': 'or',\n", "          'polygon': [{'x': 3.0848, 'y': 4.8555},\n", "           {'x': 3.209, 'y': 4.8555},\n", "           {'x': 3.2138, 'y': 5.0178},\n", "           {'x': 3.0896, 'y': 5.0178}],\n", "          'span': {'offset': 845, 'length': 2},\n", "          'confidence': 0.997},\n", "         {'content': 'travel',\n", "          'polygon': [{'x': 3.2376, 'y': 4.8555},\n", "           {'x': 3.5862, 'y': 4.8555},\n", "           {'x': 3.591, 'y': 5.0178},\n", "           {'x': 3.2424, 'y': 5.0178}],\n", "          'span': {'offset': 848, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': 'to',\n", "          'polygon': [{'x': 3.6197, 'y': 4.8602},\n", "           {'x': 3.7295, 'y': 4.8602},\n", "           {'x': 3.7343, 'y': 5.0178},\n", "           {'x': 3.6197, 'y': 5.0178}],\n", "          'span': {'offset': 855, 'length': 2},\n", "          'confidence': 0.996},\n", "         {'content': 'the',\n", "          'polygon': [{'x': 3.7629, 'y': 4.8602},\n", "           {'x': 3.9683, 'y': 4.8602},\n", "           {'x': 3.9683, 'y': 5.0178},\n", "           {'x': 3.7629, 'y': 5.0178}],\n", "          'span': {'offset': 858, 'length': 3},\n", "          'confidence': 0.996},\n", "         {'content': 'coast',\n", "          'polygon': [{'x': 4.0065, 'y': 4.8602},\n", "           {'x': 4.3455, 'y': 4.8602},\n", "           {'x': 4.3503, 'y': 5.0178},\n", "           {'x': 4.0112, 'y': 5.0178}],\n", "          'span': {'offset': 862, 'length': 5},\n", "          'confidence': 0.993},\n", "         {'content': 'yourself.',\n", "          'polygon': [{'x': 4.3789, 'y': 4.8602},\n", "           {'x': 4.9042, 'y': 4.865},\n", "           {'x': 4.9042, 'y': 5.013},\n", "           {'x': 4.3789, 'y': 5.0178}],\n", "          'span': {'offset': 868, 'length': 9},\n", "          'confidence': 0.992},\n", "         {'content': 'Please',\n", "          'polygon': [{'x': 0.2626, 'y': 5.0226},\n", "           {'x': 0.6781, 'y': 5.0226},\n", "           {'x': 0.6781, 'y': 5.1706},\n", "           {'x': 0.2626, 'y': 5.1658}],\n", "          'span': {'offset': 878, 'length': 6},\n", "          'confidence': 0.994},\n", "         {'content': 'talk',\n", "          'polygon': [{'x': 0.7115, 'y': 5.0226},\n", "           {'x': 0.936, 'y': 5.0226},\n", "           {'x': 0.936, 'y': 5.1706},\n", "           {'x': 0.7115, 'y': 5.1706}],\n", "          'span': {'offset': 885, 'length': 4},\n", "          'confidence': 0.992},\n", "         {'content': 'to',\n", "          'polygon': [{'x': 0.9646, 'y': 5.0226},\n", "           {'x': 1.0888, 'y': 5.0226},\n", "           {'x': 1.0888, 'y': 5.1706},\n", "           {'x': 0.9646, 'y': 5.1706}],\n", "          'span': {'offset': 890, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': 'one',\n", "          'polygon': [{'x': 1.1317, 'y': 5.0226},\n", "           {'x': 1.3657, 'y': 5.0226},\n", "           {'x': 1.3657, 'y': 5.1753},\n", "           {'x': 1.1317, 'y': 5.1706}],\n", "          'span': {'offset': 893, 'length': 3},\n", "          'confidence': 0.997},\n", "         {'content': 'of',\n", "          'polygon': [{'x': 1.3992, 'y': 5.0226},\n", "           {'x': 1.5185, 'y': 5.0226},\n", "           {'x': 1.5185, 'y': 5.1753},\n", "           {'x': 1.3992, 'y': 5.1753}],\n", "          'span': {'offset': 897, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': 'our',\n", "          'polygon': [{'x': 1.552, 'y': 5.0226},\n", "           {'x': 1.7669, 'y': 5.0226},\n", "           {'x': 1.7669, 'y': 5.1801},\n", "           {'x': 1.552, 'y': 5.1753}],\n", "          'span': {'offset': 900, 'length': 3},\n", "          'confidence': 0.995},\n", "         {'content': 'staff',\n", "          'polygon': [{'x': 1.7955, 'y': 5.0226},\n", "           {'x': 2.0534, 'y': 5.0226},\n", "           {'x': 2.0534, 'y': 5.1801},\n", "           {'x': 1.7955, 'y': 5.1801}],\n", "          'span': {'offset': 904, 'length': 5},\n", "          'confidence': 0.994},\n", "         {'content': 'if',\n", "          'polygon': [{'x': 2.0868, 'y': 5.0226},\n", "           {'x': 2.1775, 'y': 5.0226},\n", "           {'x': 2.1775, 'y': 5.1801},\n", "           {'x': 2.0868, 'y': 5.1801}],\n", "          'span': {'offset': 910, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': 'you',\n", "          'polygon': [{'x': 2.2062, 'y': 5.0226},\n", "           {'x': 2.4211, 'y': 5.0226},\n", "           {'x': 2.4211, 'y': 5.1801},\n", "           {'x': 2.2062, 'y': 5.1801}],\n", "          'span': {'offset': 913, 'length': 3},\n", "          'confidence': 0.994},\n", "         {'content': 'would',\n", "          'polygon': [{'x': 2.4545, 'y': 5.0226},\n", "           {'x': 2.8126, 'y': 5.0226},\n", "           {'x': 2.8126, 'y': 5.1849},\n", "           {'x': 2.4545, 'y': 5.1801}],\n", "          'span': {'offset': 917, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'like',\n", "          'polygon': [{'x': 2.8413, 'y': 5.0226},\n", "           {'x': 3.0705, 'y': 5.0226},\n", "           {'x': 3.0705, 'y': 5.1849},\n", "           {'x': 2.8413, 'y': 5.1849}],\n", "          'span': {'offset': 923, 'length': 4},\n", "          'confidence': 0.992},\n", "         {'content': 'advice',\n", "          'polygon': [{'x': 3.1087, 'y': 5.0226},\n", "           {'x': 3.5098, 'y': 5.0226},\n", "           {'x': 3.5098, 'y': 5.1849},\n", "           {'x': 3.1087, 'y': 5.1849}],\n", "          'span': {'offset': 928, 'length': 6},\n", "          'confidence': 0.994},\n", "         {'content': 'on',\n", "          'polygon': [{'x': 3.5433, 'y': 5.0226},\n", "           {'x': 3.6961, 'y': 5.0226},\n", "           {'x': 3.6961, 'y': 5.1849},\n", "           {'x': 3.5433, 'y': 5.1849}],\n", "          'span': {'offset': 935, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': 'tick',\n", "          'polygon': [{'x': 3.7247, 'y': 5.0226},\n", "           {'x': 3.9492, 'y': 5.0226},\n", "           {'x': 3.9492, 'y': 5.1849},\n", "           {'x': 3.7247, 'y': 5.1849}],\n", "          'span': {'offset': 938, 'length': 4},\n", "          'confidence': 0.992},\n", "         {'content': 'prevention.',\n", "          'polygon': [{'x': 3.9826, 'y': 5.0226},\n", "           {'x': 4.6702, 'y': 5.0226},\n", "           {'x': 4.6702, 'y': 5.1801},\n", "           {'x': 3.9826, 'y': 5.1849}],\n", "          'span': {'offset': 943, 'length': 11},\n", "          'confidence': 0.984},\n", "         {'content': 'Reminder',\n", "          'polygon': [{'x': 0.2388, 'y': 5.3759},\n", "           {'x': 0.8596, 'y': 5.3806},\n", "           {'x': 0.8548, 'y': 5.5239},\n", "           {'x': 0.2435, 'y': 5.5143}],\n", "          'span': {'offset': 955, 'length': 8},\n", "          'confidence': 0.995},\n", "         {'content': '18/03/2025',\n", "          'polygon': [{'x': 0.2817, 'y': 5.5525},\n", "           {'x': 0.9598, 'y': 5.543},\n", "           {'x': 0.9598, 'y': 5.7053},\n", "           {'x': 0.2865, 'y': 5.691}],\n", "          'span': {'offset': 964, 'length': 10},\n", "          'confidence': 0.993},\n", "         {'content': 'C5',\n", "          'polygon': [{'x': 1.0744, 'y': 5.543},\n", "           {'x': 1.2511, 'y': 5.543},\n", "           {'x': 1.2559, 'y': 5.7053},\n", "           {'x': 1.0744, 'y': 5.7053}],\n", "          'span': {'offset': 975, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': '(Triennial',\n", "          'polygon': [{'x': 1.2846, 'y': 5.543},\n", "           {'x': 1.8719, 'y': 5.543},\n", "           {'x': 1.8671, 'y': 5.7101},\n", "           {'x': 1.2846, 'y': 5.7053}],\n", "          'span': {'offset': 978, 'length': 10},\n", "          'confidence': 0.985},\n", "         {'content': 'C3)',\n", "          'polygon': [{'x': 1.9006, 'y': 5.543},\n", "           {'x': 2.1298, 'y': 5.543},\n", "           {'x': 2.1298, 'y': 5.7101},\n", "           {'x': 1.9006, 'y': 5.7101}],\n", "          'span': {'offset': 989, 'length': 3},\n", "          'confidence': 0.992},\n", "         {'content': 'Vaccination',\n", "          'polygon': [{'x': 2.1632, 'y': 5.543},\n", "           {'x': 2.8652, 'y': 5.5477},\n", "           {'x': 2.8604, 'y': 5.7053},\n", "           {'x': 2.1632, 'y': 5.7101}],\n", "          'span': {'offset': 993, 'length': 11},\n", "          'confidence': 0.982},\n", "         {'content': '11/04/2025',\n", "          'polygon': [{'x': 0.277, 'y': 5.7387},\n", "           {'x': 0.9455, 'y': 5.7339},\n", "           {'x': 0.9503, 'y': 5.8867},\n", "           {'x': 0.2817, 'y': 5.8772}],\n", "          'span': {'offset': 1005, 'length': 10},\n", "          'confidence': 0.993},\n", "         {'content': 'Annual',\n", "          'polygon': [{'x': 1.0744, 'y': 5.7339},\n", "           {'x': 1.509, 'y': 5.7339},\n", "           {'x': 1.509, 'y': 5.8915},\n", "           {'x': 1.0792, 'y': 5.8867}],\n", "          'span': {'offset': 1016, 'length': 6},\n", "          'confidence': 0.995},\n", "         {'content': 'Heartworm',\n", "          'polygon': [{'x': 1.5376, 'y': 5.7339},\n", "           {'x': 2.1775, 'y': 5.7339},\n", "           {'x': 2.1775, 'y': 5.8963},\n", "           {'x': 1.5376, 'y': 5.8915}],\n", "          'span': {'offset': 1023, 'length': 9},\n", "          'confidence': 0.994},\n", "         {'content': 'Injection',\n", "          'polygon': [{'x': 2.2492, 'y': 5.7339},\n", "           {'x': 2.7697, 'y': 5.7387},\n", "           {'x': 2.7697, 'y': 5.8963},\n", "           {'x': 2.2492, 'y': 5.8963}],\n", "          'span': {'offset': 1033, 'length': 9},\n", "          'confidence': 0.976},\n", "         {'content': 'Invoice',\n", "          'polygon': [{'x': 5.2289, 'y': 5.987},\n", "           {'x': 5.7208, 'y': 5.987},\n", "           {'x': 5.7208, 'y': 6.1445},\n", "           {'x': 5.2289, 'y': 6.1445}],\n", "          'span': {'offset': 1043, 'length': 7},\n", "          'confidence': 0.967},\n", "         {'content': 'Total:',\n", "          'polygon': [{'x': 5.7638, 'y': 5.9822},\n", "           {'x': 6.1458, 'y': 5.9774},\n", "           {'x': 6.1458, 'y': 6.1445},\n", "           {'x': 5.7638, 'y': 6.1445}],\n", "          'span': {'offset': 1051, 'length': 6},\n", "          'confidence': 0.994},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9003, 'y': 5.9918},\n", "           {'x': 7.4065, 'y': 5.987},\n", "           {'x': 7.4065, 'y': 6.135},\n", "           {'x': 6.9051, 'y': 6.135}],\n", "          'span': {'offset': 1058, 'length': 7},\n", "          'confidence': 0.994},\n", "         {'content': 'Total:',\n", "          'polygon': [{'x': 5.8067, 'y': 6.197},\n", "           {'x': 6.1315, 'y': 6.197},\n", "           {'x': 6.1362, 'y': 6.3307},\n", "           {'x': 5.8067, 'y': 6.3355}],\n", "          'span': {'offset': 1066, 'length': 6},\n", "          'confidence': 0.994},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9051, 'y': 6.1923},\n", "           {'x': 7.4065, 'y': 6.1875},\n", "           {'x': 7.4112, 'y': 6.3546},\n", "           {'x': 6.9051, 'y': 6.3546}],\n", "          'span': {'offset': 1073, 'length': 7},\n", "          'confidence': 0.995},\n", "         {'content': 'Balance',\n", "          'polygon': [{'x': 5.3006, 'y': 6.4214},\n", "           {'x': 5.7924, 'y': 6.4214},\n", "           {'x': 5.7924, 'y': 6.5551},\n", "           {'x': 5.3006, 'y': 6.5503}],\n", "          'span': {'offset': 1081, 'length': 7},\n", "          'confidence': 0.995},\n", "         {'content': 'Due:',\n", "          'polygon': [{'x': 5.8354, 'y': 6.4214},\n", "           {'x': 6.1362, 'y': 6.4119},\n", "           {'x': 6.1362, 'y': 6.5551},\n", "           {'x': 5.8354, 'y': 6.5551}],\n", "          'span': {'offset': 1089, 'length': 4},\n", "          'confidence': 0.992},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9003, 'y': 6.4167},\n", "           {'x': 7.4112, 'y': 6.4119},\n", "           {'x': 7.4112, 'y': 6.5647},\n", "           {'x': 6.9051, 'y': 6.5599}],\n", "          'span': {'offset': 1094, 'length': 7},\n", "          'confidence': 0.995},\n", "         {'content': 'Previous',\n", "          'polygon': [{'x': 5.014, 'y': 6.6363},\n", "           {'x': 5.5537, 'y': 6.6411},\n", "           {'x': 5.5537, 'y': 6.7747},\n", "           {'x': 5.014, 'y': 6.7795}],\n", "          'span': {'offset': 1102, 'length': 8},\n", "          'confidence': 0.994},\n", "         {'content': 'Balance:',\n", "          'polygon': [{'x': 5.5871, 'y': 6.6411},\n", "           {'x': 6.1315, 'y': 6.6363},\n", "           {'x': 6.1315, 'y': 6.7747},\n", "           {'x': 5.5871, 'y': 6.7747}],\n", "          'span': {'offset': 1111, 'length': 8},\n", "          'confidence': 0.993},\n", "         {'content': '$0.00',\n", "          'polygon': [{'x': 7.0531, 'y': 6.6315},\n", "           {'x': 7.4065, 'y': 6.6315},\n", "           {'x': 7.4065, 'y': 6.7795},\n", "           {'x': 7.0531, 'y': 6.7795}],\n", "          'span': {'offset': 1120, 'length': 5},\n", "          'confidence': 0.994},\n", "         {'content': 'Balance',\n", "          'polygon': [{'x': 5.3053, 'y': 6.8607},\n", "           {'x': 5.7924, 'y': 6.8559},\n", "           {'x': 5.7924, 'y': 6.9896},\n", "           {'x': 5.3053, 'y': 6.9896}],\n", "          'span': {'offset': 1126, 'length': 7},\n", "          'confidence': 0.994},\n", "         {'content': 'Due:',\n", "          'polygon': [{'x': 5.8354, 'y': 6.8559},\n", "           {'x': 6.1315, 'y': 6.8511},\n", "           {'x': 6.1315, 'y': 6.9991},\n", "           {'x': 5.8354, 'y': 6.9896}],\n", "          'span': {'offset': 1134, 'length': 4},\n", "          'confidence': 0.979},\n", "         {'content': '$252.70',\n", "          'polygon': [{'x': 6.9003, 'y': 6.8559},\n", "           {'x': 7.4065, 'y': 6.8511},\n", "           {'x': 7.4065, 'y': 7.0039},\n", "           {'x': 6.9051, 'y': 6.9991}],\n", "          'span': {'offset': 1139, 'length': 7},\n", "          'confidence': 0.995},\n", "         {'content': 'EFTPOS:',\n", "          'polygon': [{'x': 5.5346, 'y': 7.0755},\n", "           {'x': 6.1315, 'y': 7.066},\n", "           {'x': 6.1315, 'y': 7.2188},\n", "           {'x': 5.5346, 'y': 7.2044}],\n", "          'span': {'offset': 1147, 'length': 7},\n", "          'confidence': 0.993},\n", "         {'content': '($252.70)',\n", "          'polygon': [{'x': 6.8287, 'y': 7.0755},\n", "           {'x': 7.4017, 'y': 7.066},\n", "           {'x': 7.4017, 'y': 7.2331},\n", "           {'x': 6.8287, 'y': 7.2235}],\n", "          'span': {'offset': 1155, 'length': 9},\n", "          'confidence': 0.994},\n", "         {'content': 'Less',\n", "          'polygon': [{'x': 5.2146, 'y': 7.2904},\n", "           {'x': 5.5107, 'y': 7.2904},\n", "           {'x': 5.5107, 'y': 7.4431},\n", "           {'x': 5.2146, 'y': 7.4431}],\n", "          'span': {'offset': 1165, 'length': 4},\n", "          'confidence': 0.992},\n", "         {'content': 'Payment:',\n", "          'polygon': [{'x': 5.5393, 'y': 7.2904},\n", "           {'x': 6.141, 'y': 7.2808},\n", "           {'x': 6.141, 'y': 7.4527},\n", "           {'x': 5.5441, 'y': 7.4431}],\n", "          'span': {'offset': 1170, 'length': 8},\n", "          'confidence': 0.974},\n", "         {'content': '($252.70)',\n", "          'polygon': [{'x': 6.8334, 'y': 7.2951},\n", "           {'x': 7.4112, 'y': 7.2856},\n", "           {'x': 7.4112, 'y': 7.4575},\n", "           {'x': 6.8287, 'y': 7.4575}],\n", "          'span': {'offset': 1179, 'length': 9},\n", "          'confidence': 0.98},\n", "         {'content': 'Balance',\n", "          'polygon': [{'x': 5.2576, 'y': 7.5243},\n", "           {'x': 5.7829, 'y': 7.5243},\n", "           {'x': 5.7829, 'y': 7.6723},\n", "           {'x': 5.2576, 'y': 7.6723}],\n", "          'span': {'offset': 1189, 'length': 7},\n", "          'confidence': 0.993},\n", "         {'content': 'Due:',\n", "          'polygon': [{'x': 5.8258, 'y': 7.5243},\n", "           {'x': 6.141, 'y': 7.5243},\n", "           {'x': 6.141, 'y': 7.6771},\n", "           {'x': 5.8258, 'y': 7.6723}],\n", "          'span': {'offset': 1197, 'length': 4},\n", "          'confidence': 0.993},\n", "         {'content': '$0.00',\n", "          'polygon': [{'x': 7.0531, 'y': 7.5482},\n", "           {'x': 7.4112, 'y': 7.5482},\n", "           {'x': 7.4112, 'y': 7.6914},\n", "           {'x': 7.0531, 'y': 7.6914}],\n", "          'span': {'offset': 1202, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'The',\n", "          'polygon': [{'x': 4.9138, 'y': 7.849},\n", "           {'x': 5.1478, 'y': 7.8442},\n", "           {'x': 5.1382, 'y': 8.0208},\n", "           {'x': 4.9042, 'y': 8.0208}],\n", "          'span': {'offset': 1208, 'length': 3},\n", "          'confidence': 0.996},\n", "         {'content': 'total',\n", "          'polygon': [{'x': 5.1812, 'y': 7.8442},\n", "           {'x': 5.482, 'y': 7.8442},\n", "           {'x': 5.4772, 'y': 8.0208},\n", "           {'x': 5.1716, 'y': 8.0208}],\n", "          'span': {'offset': 1212, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'price',\n", "          'polygon': [{'x': 5.5154, 'y': 7.8442},\n", "           {'x': 5.8449, 'y': 7.8442},\n", "           {'x': 5.8354, 'y': 8.0161},\n", "           {'x': 5.5107, 'y': 8.0161}],\n", "          'span': {'offset': 1218, 'length': 5},\n", "          'confidence': 0.995},\n", "         {'content': 'includes',\n", "          'polygon': [{'x': 5.8784, 'y': 7.8442},\n", "           {'x': 6.461, 'y': 7.8442},\n", "           {'x': 6.4562, 'y': 8.0113},\n", "           {'x': 5.8736, 'y': 8.0161}],\n", "          'span': {'offset': 1224, 'length': 8},\n", "          'confidence': 0.992},\n", "         {'content': 'GST',\n", "          'polygon': [{'x': 6.4944, 'y': 7.8442},\n", "           {'x': 6.7857, 'y': 7.8442},\n", "           {'x': 6.7809, 'y': 8.0113},\n", "           {'x': 6.4896, 'y': 8.0113}],\n", "          'span': {'offset': 1233, 'length': 3},\n", "          'confidence': 0.995},\n", "         {'content': 'of',\n", "          'polygon': [{'x': 6.8191, 'y': 7.8442},\n", "           {'x': 6.9433, 'y': 7.8394},\n", "           {'x': 6.9385, 'y': 8.0113},\n", "           {'x': 6.8143, 'y': 8.0113}],\n", "          'span': {'offset': 1237, 'length': 2},\n", "          'confidence': 0.995},\n", "         {'content': '$22.97',\n", "          'polygon': [{'x': 6.9767, 'y': 7.8394},\n", "           {'x': 7.4256, 'y': 7.8394},\n", "           {'x': 7.4256, 'y': 8.0065},\n", "           {'x': 6.9719, 'y': 8.0113}],\n", "          'span': {'offset': 1240, 'length': 6},\n", "          'confidence': 0.995}],\n", "        'selection_marks': [],\n", "        'spans': [{'offset': 0, 'length': 1246}],\n", "        'barcodes': [],\n", "        'formulas': []}],\n", "      'paragraphs': [],\n", "      'tables': [{'row_count': 8,\n", "        'column_count': 5,\n", "        'cells': [{'kind': 'columnHeader',\n", "          'row_index': 0,\n", "          'column_index': 0,\n", "          'row_span': 2,\n", "          'column_span': 2,\n", "          'content': 'Patient ID: 83692\\nPatient Name: <PERSON>',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.216, 'y': 2.5719},\n", "             {'x': 2.9702, 'y': 2.5719},\n", "             {'x': 2.9702, 'y': 2.9894},\n", "             {'x': 0.216, 'y': 2.9894}]}],\n", "          'spans': [{'offset': 165, 'length': 17},\n", "           {'offset': 223, 'length': 20}]},\n", "         {'kind': 'columnHeader',\n", "          'row_index': 0,\n", "          'column_index': 2,\n", "          'row_span': 2,\n", "          'column_span': 1,\n", "          'content': 'Species: Canine\\nBreed: Bulldog, British',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 2.9702, 'y': 2.5719},\n", "             {'x': 5.0745, 'y': 2.5719},\n", "             {'x': 5.0822, 'y': 2.9894},\n", "             {'x': 2.9702, 'y': 2.9894}]}],\n", "          'spans': [{'offset': 183, 'length': 15},\n", "           {'offset': 244, 'length': 23}]},\n", "         {'kind': 'columnHeader',\n", "          'row_index': 0,\n", "          'column_index': 3,\n", "          'row_span': 1,\n", "          'column_span': 2,\n", "          'content': 'Weight: 26.00\\nkilograms',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 5.0745, 'y': 2.5719},\n", "             {'x': 7.6353, 'y': 2.5719},\n", "             {'x': 7.6353, 'y': 2.7806},\n", "             {'x': 5.0822, 'y': 2.7806}]}],\n", "          'spans': [{'offset': 199, 'length': 23}]},\n", "         {'kind': 'columnHeader',\n", "          'row_index': 1,\n", "          'column_index': 3,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Birthday: 04/01/2024',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 5.0822, 'y': 2.7806},\n", "             {'x': 6.3974, 'y': 2.7806},\n", "             {'x': 6.4052, 'y': 2.9894},\n", "             {'x': 5.0822, 'y': 2.9894}]}],\n", "          'spans': [{'offset': 268, 'length': 20}]},\n", "         {'kind': 'columnHeader',\n", "          'row_index': 1,\n", "          'column_index': 4,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Sex: Male',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.3974, 'y': 2.7806},\n", "             {'x': 7.6353, 'y': 2.7806},\n", "             {'x': 7.6353, 'y': 2.9894},\n", "             {'x': 6.4052, 'y': 2.9894}]}],\n", "          'spans': [{'offset': 289, 'length': 9}]},\n", "         {'kind': 'columnHeader',\n", "          'row_index': 2,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.216, 'y': 2.9894},\n", "             {'x': 1.1675, 'y': 2.9894},\n", "             {'x': 1.1675, 'y': 3.1905},\n", "             {'x': 0.216, 'y': 3.1827}]}],\n", "          'spans': []},\n", "         {'kind': 'columnHeader',\n", "          'row_index': 2,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Description',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 1.1675, 'y': 2.9894},\n", "             {'x': 2.9702, 'y': 2.9894},\n", "             {'x': 2.9779, 'y': 3.1905},\n", "             {'x': 1.1675, 'y': 3.1905}]}],\n", "          'spans': [{'offset': 299, 'length': 11}]},\n", "         {'kind': 'columnHeader',\n", "          'row_index': 2,\n", "          'column_index': 2,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Staff Name',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 2.9702, 'y': 2.9894},\n", "             {'x': 5.0822, 'y': 2.9894},\n", "             {'x': 5.0822, 'y': 3.1905},\n", "             {'x': 2.9779, 'y': 3.1905}]}],\n", "          'spans': [{'offset': 311, 'length': 10}]},\n", "         {'kind': 'columnHeader',\n", "          'row_index': 2,\n", "          'column_index': 3,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Quantity',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 5.0822, 'y': 2.9894},\n", "             {'x': 6.4052, 'y': 2.9894},\n", "             {'x': 6.4052, 'y': 3.1905},\n", "             {'x': 5.0822, 'y': 3.1905}]}],\n", "          'spans': [{'offset': 322, 'length': 8}]},\n", "         {'kind': 'columnHeader',\n", "          'row_index': 2,\n", "          'column_index': 4,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Total',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4052, 'y': 2.9894},\n", "             {'x': 7.6353, 'y': 2.9894},\n", "             {'x': 7.6353, 'y': 3.1905},\n", "             {'x': 6.4052, 'y': 3.1905}]}],\n", "          'spans': [{'offset': 331, 'length': 5}]},\n", "         {'kind': 'content',\n", "          'row_index': 3,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '17/09/2024',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.216, 'y': 3.1827},\n", "             {'x': 1.1675, 'y': 3.1905},\n", "             {'x': 1.1675, 'y': 3.4147},\n", "             {'x': 0.216, 'y': 3.4147}]}],\n", "          'spans': [{'offset': 337, 'length': 10}]},\n", "         {'kind': 'content',\n", "          'row_index': 3,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Consultation',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 1.1675, 'y': 3.1905},\n", "             {'x': 2.9779, 'y': 3.1905},\n", "             {'x': 2.9779, 'y': 3.4147},\n", "             {'x': 1.1675, 'y': 3.4147}]}],\n", "          'spans': [{'offset': 348, 'length': 12}]},\n", "         {'kind': 'content',\n", "          'row_index': 3,\n", "          'column_index': 2,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Dr. <PERSON>',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 2.9779, 'y': 3.1905},\n", "             {'x': 5.0822, 'y': 3.1905},\n", "             {'x': 5.0822, 'y': 3.407},\n", "             {'x': 2.9779, 'y': 3.4147}]}],\n", "          'spans': [{'offset': 361, 'length': 15}]},\n", "         {'kind': 'content',\n", "          'row_index': 3,\n", "          'column_index': 3,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '1.00',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 5.0822, 'y': 3.1905},\n", "             {'x': 6.4052, 'y': 3.1905},\n", "             {'x': 6.4052, 'y': 3.407},\n", "             {'x': 5.0822, 'y': 3.407}]}],\n", "          'spans': [{'offset': 377, 'length': 4}]},\n", "         {'kind': 'content',\n", "          'row_index': 3,\n", "          'column_index': 4,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$104.00',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4052, 'y': 3.1905},\n", "             {'x': 7.6353, 'y': 3.1905},\n", "             {'x': 7.6353, 'y': 3.407},\n", "             {'x': 6.4052, 'y': 3.407}]}],\n", "          'spans': [{'offset': 382, 'length': 7}]},\n", "         {'kind': 'content',\n", "          'row_index': 4,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.216, 'y': 3.4147},\n", "             {'x': 1.1675, 'y': 3.4147},\n", "             {'x': 1.1675, 'y': 3.5848},\n", "             {'x': 0.216, 'y': 3.5848}]}],\n", "          'spans': []},\n", "         {'kind': 'content',\n", "          'row_index': 4,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Cephalexin 200mg Beef',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 1.1675, 'y': 3.4147},\n", "             {'x': 2.9779, 'y': 3.4147},\n", "             {'x': 2.9779, 'y': 3.5848},\n", "             {'x': 1.1675, 'y': 3.5848}]}],\n", "          'spans': [{'offset': 390, 'length': 21}]},\n", "         {'kind': 'content',\n", "          'row_index': 4,\n", "          'column_index': 2,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 2.9779, 'y': 3.4147},\n", "             {'x': 5.0822, 'y': 3.407},\n", "             {'x': 5.0822, 'y': 3.5771},\n", "             {'x': 2.9779, 'y': 3.5848}]}],\n", "          'spans': []},\n", "         {'kind': 'content',\n", "          'row_index': 4,\n", "          'column_index': 3,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '28.00',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 5.0822, 'y': 3.407},\n", "             {'x': 6.4052, 'y': 3.407},\n", "             {'x': 6.4052, 'y': 3.5771},\n", "             {'x': 5.0822, 'y': 3.5771}]}],\n", "          'spans': [{'offset': 412, 'length': 5}]},\n", "         {'kind': 'content',\n", "          'row_index': 4,\n", "          'column_index': 4,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$62.80',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4052, 'y': 3.407},\n", "             {'x': 7.6353, 'y': 3.407},\n", "             {'x': 7.6353, 'y': 3.5771},\n", "             {'x': 6.4052, 'y': 3.5771}]}],\n", "          'spans': [{'offset': 418, 'length': 6}]},\n", "         {'kind': 'content',\n", "          'row_index': 5,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.216, 'y': 3.5848},\n", "             {'x': 1.1675, 'y': 3.5848},\n", "             {'x': 1.1675, 'y': 3.7472},\n", "             {'x': 0.216, 'y': 3.7472}]}],\n", "          'spans': []},\n", "         {'kind': 'content',\n", "          'row_index': 5,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': \"Antinol Plus Dog 60's\",\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 1.1675, 'y': 3.5848},\n", "             {'x': 2.9779, 'y': 3.5848},\n", "             {'x': 2.9856, 'y': 3.7472},\n", "             {'x': 1.1675, 'y': 3.7472}]}],\n", "          'spans': [{'offset': 425, 'length': 21}]},\n", "         {'kind': 'content',\n", "          'row_index': 5,\n", "          'column_index': 2,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 2.9779, 'y': 3.5848},\n", "             {'x': 5.0822, 'y': 3.5771},\n", "             {'x': 5.0822, 'y': 3.7472},\n", "             {'x': 2.9856, 'y': 3.7472}]}],\n", "          'spans': []},\n", "         {'kind': 'content',\n", "          'row_index': 5,\n", "          'column_index': 3,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '1.00',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 5.0822, 'y': 3.5771},\n", "             {'x': 6.4052, 'y': 3.5771},\n", "             {'x': 6.4052, 'y': 3.7472},\n", "             {'x': 5.0822, 'y': 3.7472}]}],\n", "          'spans': [{'offset': 447, 'length': 4}]},\n", "         {'kind': 'content',\n", "          'row_index': 5,\n", "          'column_index': 4,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$59.15',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4052, 'y': 3.5771},\n", "             {'x': 7.6353, 'y': 3.5771},\n", "             {'x': 7.6353, 'y': 3.7472},\n", "             {'x': 6.4052, 'y': 3.7472}]}],\n", "          'spans': [{'offset': 452, 'length': 6}]},\n", "         {'kind': 'content',\n", "          'row_index': 6,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.216, 'y': 3.7472},\n", "             {'x': 1.1675, 'y': 3.7472},\n", "             {'x': 1.1675, 'y': 3.9482},\n", "             {'x': 0.216, 'y': 3.9482}]}],\n", "          'spans': []},\n", "         {'kind': 'content',\n", "          'row_index': 6,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Fungassay- Fungal culture',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 1.1675, 'y': 3.7472},\n", "             {'x': 2.9856, 'y': 3.7472},\n", "             {'x': 2.9856, 'y': 3.9482},\n", "             {'x': 1.1675, 'y': 3.9482}]}],\n", "          'spans': [{'offset': 459, 'length': 25}]},\n", "         {'kind': 'content',\n", "          'row_index': 6,\n", "          'column_index': 2,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 2.9856, 'y': 3.7472},\n", "             {'x': 5.0822, 'y': 3.7472},\n", "             {'x': 5.0822, 'y': 3.9405},\n", "             {'x': 2.9856, 'y': 3.9482}]}],\n", "          'spans': []},\n", "         {'kind': 'content',\n", "          'row_index': 6,\n", "          'column_index': 3,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '1.00',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 5.0822, 'y': 3.7472},\n", "             {'x': 6.4052, 'y': 3.7472},\n", "             {'x': 6.4052, 'y': 3.9405},\n", "             {'x': 5.0822, 'y': 3.9405}]}],\n", "          'spans': [{'offset': 485, 'length': 4}]},\n", "         {'kind': 'content',\n", "          'row_index': 6,\n", "          'column_index': 4,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$26.75',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4052, 'y': 3.7472},\n", "             {'x': 7.6353, 'y': 3.7472},\n", "             {'x': 7.6353, 'y': 3.9405},\n", "             {'x': 6.4052, 'y': 3.9405}]}],\n", "          'spans': [{'offset': 490, 'length': 6}]},\n", "         {'kind': 'content',\n", "          'row_index': 7,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.216, 'y': 3.9482},\n", "             {'x': 1.1675, 'y': 3.9482},\n", "             {'x': 1.1675, 'y': 4.1647},\n", "             {'x': 0.216, 'y': 4.1647}]}],\n", "          'spans': []},\n", "         {'kind': 'content',\n", "          'row_index': 7,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 1.1675, 'y': 3.9482},\n", "             {'x': 2.9856, 'y': 3.9482},\n", "             {'x': 2.9856, 'y': 4.1647},\n", "             {'x': 1.1675, 'y': 4.1647}]}],\n", "          'spans': []},\n", "         {'kind': 'content',\n", "          'row_index': 7,\n", "          'column_index': 2,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 2.9856, 'y': 3.9482},\n", "             {'x': 5.0822, 'y': 3.9405},\n", "             {'x': 5.0822, 'y': 4.1647},\n", "             {'x': 2.9856, 'y': 4.1647}]}],\n", "          'spans': []},\n", "         {'kind': 'content',\n", "          'row_index': 7,\n", "          'column_index': 3,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Patient Subtotal:',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 5.0822, 'y': 3.9405},\n", "             {'x': 6.4052, 'y': 3.9405},\n", "             {'x': 6.4052, 'y': 4.1647},\n", "             {'x': 5.0822, 'y': 4.1647}]}],\n", "          'spans': [{'offset': 497, 'length': 17}]},\n", "         {'kind': 'content',\n", "          'row_index': 7,\n", "          'column_index': 4,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$252.70',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4052, 'y': 3.9405},\n", "             {'x': 7.6353, 'y': 3.9405},\n", "             {'x': 7.6275, 'y': 4.1647},\n", "             {'x': 6.4052, 'y': 4.1647}]}],\n", "          'spans': [{'offset': 515, 'length': 7}]}],\n", "        'bounding_regions': [{'page_number': 1,\n", "          'polygon': [{'x': 0.226, 'y': 2.519},\n", "           {'x': 7.7664, 'y': 2.5141},\n", "           {'x': 7.7688, 'y': 4.174},\n", "           {'x': 0.2279, 'y': 4.1786}]}],\n", "        'spans': [{'offset': 165, 'length': 17},\n", "         {'offset': 223, 'length': 20},\n", "         {'offset': 183, 'length': 15},\n", "         {'offset': 244, 'length': 23},\n", "         {'offset': 199, 'length': 23},\n", "         {'offset': 268, 'length': 254}]},\n", "       {'row_count': 8,\n", "        'column_count': 2,\n", "        'cells': [{'kind': 'content',\n", "          'row_index': 0,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Invoice Total:',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 4.952, 'y': 5.9345},\n", "             {'x': 6.4801, 'y': 5.9392},\n", "             {'x': 6.4801, 'y': 6.1636},\n", "             {'x': 4.9472, 'y': 6.1589}]}],\n", "          'spans': [{'offset': 1043, 'length': 14}]},\n", "         {'kind': 'content',\n", "          'row_index': 0,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$252.70',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4801, 'y': 5.9392},\n", "             {'x': 7.4638, 'y': 5.944},\n", "             {'x': 7.4638, 'y': 6.1636},\n", "             {'x': 6.4801, 'y': 6.1636}]}],\n", "          'spans': [{'offset': 1058, 'length': 7}]},\n", "         {'kind': 'content',\n", "          'row_index': 1,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Total:',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 4.9472, 'y': 6.1589},\n", "             {'x': 6.4801, 'y': 6.1636},\n", "             {'x': 6.4801, 'y': 6.3785},\n", "             {'x': 4.9424, 'y': 6.3737}]}],\n", "          'spans': [{'offset': 1066, 'length': 6}]},\n", "         {'kind': 'content',\n", "          'row_index': 1,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$252.70',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4801, 'y': 6.1636},\n", "             {'x': 7.4638, 'y': 6.1636},\n", "             {'x': 7.4638, 'y': 6.3785},\n", "             {'x': 6.4801, 'y': 6.3785}]}],\n", "          'spans': [{'offset': 1073, 'length': 7}]},\n", "         {'kind': 'content',\n", "          'row_index': 2,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Balance Due:',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 4.9424, 'y': 6.3737},\n", "             {'x': 6.4801, 'y': 6.3785},\n", "             {'x': 6.4801, 'y': 6.5933},\n", "             {'x': 4.9376, 'y': 6.5885}]}],\n", "          'spans': [{'offset': 1081, 'length': 12}]},\n", "         {'kind': 'content',\n", "          'row_index': 2,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$252.70',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4801, 'y': 6.3785},\n", "             {'x': 7.4638, 'y': 6.3785},\n", "             {'x': 7.459, 'y': 6.5981},\n", "             {'x': 6.4801, 'y': 6.5933}]}],\n", "          'spans': [{'offset': 1094, 'length': 7}]},\n", "         {'kind': 'content',\n", "          'row_index': 3,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Previous Balance:',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 4.9376, 'y': 6.5885},\n", "             {'x': 6.4801, 'y': 6.5933},\n", "             {'x': 6.4801, 'y': 6.8177},\n", "             {'x': 4.9329, 'y': 6.8129}]}],\n", "          'spans': [{'offset': 1102, 'length': 17}]},\n", "         {'kind': 'content',\n", "          'row_index': 3,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$0.00',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4801, 'y': 6.5933},\n", "             {'x': 7.459, 'y': 6.5981},\n", "             {'x': 7.459, 'y': 6.8225},\n", "             {'x': 6.4801, 'y': 6.8177}]}],\n", "          'spans': [{'offset': 1120, 'length': 5}]},\n", "         {'kind': 'content',\n", "          'row_index': 4,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Balance Due:',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 4.9329, 'y': 6.8129},\n", "             {'x': 6.4801, 'y': 6.8177},\n", "             {'x': 6.4801, 'y': 7.0326},\n", "             {'x': 4.9281, 'y': 7.0326}]}],\n", "          'spans': [{'offset': 1126, 'length': 12}]},\n", "         {'kind': 'content',\n", "          'row_index': 4,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$252.70',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4801, 'y': 6.8177},\n", "             {'x': 7.459, 'y': 6.8225},\n", "             {'x': 7.459, 'y': 7.0373},\n", "             {'x': 6.4801, 'y': 7.0326}]}],\n", "          'spans': [{'offset': 1139, 'length': 7}]},\n", "         {'kind': 'content',\n", "          'row_index': 5,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'EFTPOS:',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 4.9281, 'y': 7.0326},\n", "             {'x': 6.4801, 'y': 7.0326},\n", "             {'x': 6.4801, 'y': 7.2569},\n", "             {'x': 4.9233, 'y': 7.2522}]}],\n", "          'spans': [{'offset': 1147, 'length': 7}]},\n", "         {'kind': 'content',\n", "          'row_index': 5,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '($252.70)',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4801, 'y': 7.0326},\n", "             {'x': 7.459, 'y': 7.0373},\n", "             {'x': 7.459, 'y': 7.2569},\n", "             {'x': 6.4801, 'y': 7.2569}]}],\n", "          'spans': [{'offset': 1155, 'length': 9}]},\n", "         {'kind': 'content',\n", "          'row_index': 6,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Less Payment:',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 4.9233, 'y': 7.2522},\n", "             {'x': 6.4801, 'y': 7.2569},\n", "             {'x': 6.4801, 'y': 7.4909},\n", "             {'x': 4.9185, 'y': 7.4861}]}],\n", "          'spans': [{'offset': 1165, 'length': 13}]},\n", "         {'kind': 'content',\n", "          'row_index': 6,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '($252.70)',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4801, 'y': 7.2569},\n", "             {'x': 7.459, 'y': 7.2569},\n", "             {'x': 7.459, 'y': 7.4957},\n", "             {'x': 6.4801, 'y': 7.4909}]}],\n", "          'spans': [{'offset': 1179, 'length': 9}]},\n", "         {'kind': 'content',\n", "          'row_index': 7,\n", "          'column_index': 0,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': 'Balance Due:',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 4.9185, 'y': 7.4861},\n", "             {'x': 6.4801, 'y': 7.4909},\n", "             {'x': 6.4753, 'y': 7.7535},\n", "             {'x': 4.9138, 'y': 7.7487}]}],\n", "          'spans': [{'offset': 1189, 'length': 12}]},\n", "         {'kind': 'content',\n", "          'row_index': 7,\n", "          'column_index': 1,\n", "          'row_span': 1,\n", "          'column_span': 1,\n", "          'content': '$0.00',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.4801, 'y': 7.4909},\n", "             {'x': 7.459, 'y': 7.4957},\n", "             {'x': 7.4542, 'y': 7.7582},\n", "             {'x': 6.4753, 'y': 7.7535}]}],\n", "          'spans': [{'offset': 1202, 'length': 5}]}],\n", "        'bounding_regions': [{'page_number': 1,\n", "          'polygon': [{'x': 4.8784, 'y': 5.9465},\n", "           {'x': 7.4656, 'y': 5.9493},\n", "           {'x': 7.464, 'y': 7.776},\n", "           {'x': 4.8766, 'y': 7.774}]}],\n", "        'spans': [{'offset': 1043, 'length': 164}]}],\n", "      'key_value_pairs': [],\n", "      'styles': [],\n", "      'documents': [{'doc_type': 'invoice',\n", "        'bounding_regions': [{'page_number': 1,\n", "          'polygon': [{'x': 0.0, 'y': 0.0},\n", "           {'x': 8.5, 'y': 0.0},\n", "           {'x': 8.5, 'y': 11.0},\n", "           {'x': 0.0, 'y': 11.0}]}],\n", "        'spans': [{'offset': 0, 'length': 1246}],\n", "        'fields': {'AmountDue': {'value_type': 'currency',\n", "          'value': {'amount': 252.7, 'symbol': '$', 'code': 'AUD'},\n", "          'content': '$252.70',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.9003, 'y': 6.4167},\n", "             {'x': 7.4112, 'y': 6.4119},\n", "             {'x': 7.4112, 'y': 6.5647},\n", "             {'x': 6.9051, 'y': 6.5599}]}],\n", "          'spans': [{'offset': 1094, 'length': 7}],\n", "          'confidence': 0.644},\n", "         'CustomerId': {'value_type': 'string',\n", "          'value': '35861',\n", "          'content': '35861',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 7.3253, 'y': 1.5421},\n", "             {'x': 7.6978, 'y': 1.5373},\n", "             {'x': 7.6978, 'y': 1.671},\n", "             {'x': 7.3253, 'y': 1.6662}]}],\n", "          'spans': [{'offset': 22, 'length': 5}],\n", "          'confidence': 0.926},\n", "         'CustomerName': {'value_type': 'string',\n", "          'value': '<PERSON>',\n", "          'content': '<PERSON>',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 1.0315, 'y': 2.8168},\n", "             {'x': 1.3896, 'y': 2.8216},\n", "             {'x': 1.3944, 'y': 2.9505},\n", "             {'x': 1.0315, 'y': 2.9457}]}],\n", "          'spans': [{'offset': 237, 'length': 6}],\n", "          'confidence': 0.576},\n", "         'InvoiceDate': {'value_type': 'date',\n", "          'value': datetime.date(2024, 9, 17),\n", "          'content': '17/09/2024',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 7.0197, 'y': 1.9097},\n", "             {'x': 7.7121, 'y': 1.9145},\n", "             {'x': 7.7121, 'y': 2.0768},\n", "             {'x': 7.0149, 'y': 2.0816}]}],\n", "          'spans': [{'offset': 114, 'length': 10}],\n", "          'confidence': 0.947},\n", "         'InvoiceId': {'value_type': 'string',\n", "          'value': '545337',\n", "          'content': '545337',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 7.2393, 'y': 1.7378},\n", "             {'x': 7.7025, 'y': 1.7331},\n", "             {'x': 7.7025, 'y': 1.8668},\n", "             {'x': 7.2393, 'y': 1.862}]}],\n", "          'spans': [{'offset': 62, 'length': 6}],\n", "          'confidence': 0.938},\n", "         'InvoiceTotal': {'value_type': 'currency',\n", "          'value': {'amount': 252.7, 'symbol': '$', 'code': 'AUD'},\n", "          'content': '$252.70',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.9051, 'y': 6.1923},\n", "             {'x': 7.4065, 'y': 6.1875},\n", "             {'x': 7.4112, 'y': 6.3546},\n", "             {'x': 6.9051, 'y': 6.3546}]}],\n", "          'spans': [{'offset': 1073, 'length': 7}],\n", "          'confidence': 0.769},\n", "         'Items': {'value_type': 'list',\n", "          'value': [{'value_type': 'dictionary',\n", "            'value': {'Amount': {'value_type': 'currency',\n", "              'value': {'amount': 104.0, 'symbol': '$', 'code': 'AUD'},\n", "              'content': '$104.00',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 6.9051, 'y': 3.2609},\n", "                 {'x': 7.4065, 'y': 3.2609},\n", "                 {'x': 7.4112, 'y': 3.3993},\n", "                 {'x': 6.9098, 'y': 3.3945}]}],\n", "              'spans': [{'offset': 382, 'length': 7}],\n", "              'confidence': 0.908},\n", "             'Date': {'value_type': 'date',\n", "              'value': datetime.date(2024, 9, 17),\n", "              'content': '17/09/2024',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 0.2961, 'y': 3.2561},\n", "                 {'x': 0.9742, 'y': 3.2561},\n", "                 {'x': 0.9742, 'y': 3.3993},\n", "                 {'x': 0.2961, 'y': 3.3945}]}],\n", "              'spans': [{'offset': 337, 'length': 10}],\n", "              'confidence': 0.886},\n", "             'Description': {'value_type': 'string',\n", "              'value': 'Consultation',\n", "              'content': 'Consultation',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 1.3323, 'y': 3.2561},\n", "                 {'x': 2.0963, 'y': 3.2561},\n", "                 {'x': 2.0963, 'y': 3.3993},\n", "                 {'x': 1.3323, 'y': 3.3898}]}],\n", "              'spans': [{'offset': 348, 'length': 12}],\n", "              'confidence': 0.91},\n", "             'Quantity': {'value_type': 'float',\n", "              'value': 1.0,\n", "              'content': '1.00',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 5.8593, 'y': 3.2561},\n", "                 {'x': 6.1219, 'y': 3.2561},\n", "                 {'x': 6.1219, 'y': 3.3898},\n", "                 {'x': 5.8593, 'y': 3.3898}]}],\n", "              'spans': [{'offset': 377, 'length': 4}],\n", "              'confidence': 0.898}},\n", "            'content': '17/09/2024\\nConsultation\\nDr. <PERSON>\\n1.00\\n$104.00',\n", "            'bounding_regions': [{'page_number': 1,\n", "              'polygon': [{'x': 0.2963, 'y': 3.2243},\n", "               {'x': 7.4119, 'y': 3.2584},\n", "               {'x': 7.4111, 'y': 3.4301},\n", "               {'x': 0.2954, 'y': 3.3961}]}],\n", "            'spans': [{'offset': 337, 'length': 52}],\n", "            'confidence': 0.597},\n", "           {'value_type': 'dictionary',\n", "            'value': {'Amount': {'value_type': 'currency',\n", "              'value': {'amount': 62.8, 'symbol': '$', 'code': 'AUD'},\n", "              'content': '$62.80',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 6.991, 'y': 3.4327},\n", "                 {'x': 7.4017, 'y': 3.4232},\n", "                 {'x': 7.4065, 'y': 3.5664},\n", "                 {'x': 6.991, 'y': 3.5616}]}],\n", "              'spans': [{'offset': 418, 'length': 6}],\n", "              'confidence': 0.906},\n", "             'Description': {'value_type': 'string',\n", "              'value': 'Cephalexin 200mg Beef',\n", "              'content': 'Cephalexin 200mg Beef',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 1.3323, 'y': 3.4221},\n", "                 {'x': 2.8317, 'y': 3.4136},\n", "                 {'x': 2.8326, 'y': 3.5807},\n", "                 {'x': 1.3332, 'y': 3.5892}]}],\n", "              'spans': [{'offset': 390, 'length': 21}],\n", "              'confidence': 0.911},\n", "             'Quantity': {'value_type': 'float',\n", "              'value': 28.0,\n", "              'content': '28.00',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 5.7829, 'y': 3.4232},\n", "                 {'x': 6.1124, 'y': 3.4232},\n", "                 {'x': 6.1124, 'y': 3.5569},\n", "                 {'x': 5.7829, 'y': 3.5569}]}],\n", "              'spans': [{'offset': 412, 'length': 5}],\n", "              'confidence': 0.888}},\n", "            'content': 'Cephalexin 200mg Beef\\n28.00\\n$62.80',\n", "            'bounding_regions': [{'page_number': 1,\n", "              'polygon': [{'x': 1.3323, 'y': 3.4221},\n", "               {'x': 7.4055, 'y': 3.3878},\n", "               {'x': 7.4065, 'y': 3.5664},\n", "               {'x': 1.3333, 'y': 3.6007}]}],\n", "            'spans': [{'offset': 390, 'length': 34}],\n", "            'confidence': 0.911},\n", "           {'value_type': 'dictionary',\n", "            'value': {'Amount': {'value_type': 'currency',\n", "              'value': {'amount': 59.15, 'symbol': '$', 'code': 'AUD'},\n", "              'content': '$59.15',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 6.9815, 'y': 3.5903},\n", "                 {'x': 7.4065, 'y': 3.5855},\n", "                 {'x': 7.4065, 'y': 3.7383},\n", "                 {'x': 6.9862, 'y': 3.7335}]}],\n", "              'spans': [{'offset': 452, 'length': 6}],\n", "              'confidence': 0.908},\n", "             'Description': {'value_type': 'string',\n", "              'value': \"Antinol Plus Dog 60's\",\n", "              'content': \"Antinol Plus Dog 60's\",\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 1.3325, 'y': 3.5736},\n", "                 {'x': 2.6566, 'y': 3.5855},\n", "                 {'x': 2.6551, 'y': 3.7483},\n", "                 {'x': 1.331, 'y': 3.7364}]}],\n", "              'spans': [{'offset': 425, 'length': 21}],\n", "              'confidence': 0.911},\n", "             'Quantity': {'value_type': 'float',\n", "              'value': 1.0,\n", "              'content': '1.00',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 5.8593, 'y': 3.5951},\n", "                 {'x': 6.1124, 'y': 3.5951},\n", "                 {'x': 6.1076, 'y': 3.724},\n", "                 {'x': 5.8593, 'y': 3.7192}]}],\n", "              'spans': [{'offset': 447, 'length': 4}],\n", "              'confidence': 0.897}},\n", "            'content': \"Antinol Plus Dog 60's\\n1.00\\n$59.15\",\n", "            'bounding_regions': [{'page_number': 1,\n", "              'polygon': [{'x': 1.3328, 'y': 3.5307},\n", "               {'x': 7.4079, 'y': 3.5855},\n", "               {'x': 7.406, 'y': 3.7912},\n", "               {'x': 1.331, 'y': 3.7364}]}],\n", "            'spans': [{'offset': 425, 'length': 33}],\n", "            'confidence': 0.916},\n", "           {'value_type': 'dictionary',\n", "            'value': {'Amount': {'value_type': 'currency',\n", "              'value': {'amount': 26.75, 'symbol': '$', 'code': 'AUD'},\n", "              'content': '$26.75',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 6.9719, 'y': 3.7431},\n", "                 {'x': 7.416, 'y': 3.7431},\n", "                 {'x': 7.416, 'y': 3.9006},\n", "                 {'x': 6.9767, 'y': 3.9006}]}],\n", "              'spans': [{'offset': 490, 'length': 6}],\n", "              'confidence': 0.906},\n", "             'Description': {'value_type': 'string',\n", "              'value': 'Fungassay- Fungal culture',\n", "              'content': 'Fungassay- Fungal culture',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 1.3323, 'y': 3.7526},\n", "                 {'x': 2.9798, 'y': 3.7526},\n", "                 {'x': 2.9798, 'y': 3.9149},\n", "                 {'x': 1.3323, 'y': 3.9149}]}],\n", "              'spans': [{'offset': 459, 'length': 25}],\n", "              'confidence': 0.909},\n", "             'Quantity': {'value_type': 'float',\n", "              'value': 1.0,\n", "              'content': '1.00',\n", "              'bounding_regions': [{'page_number': 1,\n", "                'polygon': [{'x': 5.8593, 'y': 3.7622},\n", "                 {'x': 6.1171, 'y': 3.7622},\n", "                 {'x': 6.1171, 'y': 3.8911},\n", "                 {'x': 5.8593, 'y': 3.8863}]}],\n", "              'spans': [{'offset': 485, 'length': 4}],\n", "              'confidence': 0.892}},\n", "            'content': 'Fungassay- Fungal culture\\n1.00\\n$26.75',\n", "            'bounding_regions': [{'page_number': 1,\n", "              'polygon': [{'x': 1.3323, 'y': 3.7431},\n", "               {'x': 7.416, 'y': 3.7431},\n", "               {'x': 7.416, 'y': 3.9149},\n", "               {'x': 1.3323, 'y': 3.9149}]}],\n", "            'spans': [{'offset': 459, 'length': 37}],\n", "            'confidence': 0.911}],\n", "          'content': None,\n", "          'bounding_regions': [],\n", "          'spans': [],\n", "          'confidence': None},\n", "         'PreviousUnpaidBalance': {'value_type': 'currency',\n", "          'value': {'amount': 0.0, 'symbol': '$', 'code': 'AUD'},\n", "          'content': '$0.00',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 7.0531, 'y': 6.6315},\n", "             {'x': 7.4065, 'y': 6.6315},\n", "             {'x': 7.4065, 'y': 6.7795},\n", "             {'x': 7.0531, 'y': 6.7795}]}],\n", "          'spans': [{'offset': 1120, 'length': 5}],\n", "          'confidence': 0.937},\n", "         'SubTotal': {'value_type': 'currency',\n", "          'value': {'amount': 252.7, 'symbol': '$', 'code': 'AUD'},\n", "          'content': '$252.70',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 6.9051, 'y': 3.9674},\n", "             {'x': 7.4065, 'y': 3.9627},\n", "             {'x': 7.4065, 'y': 4.1155},\n", "             {'x': 6.9098, 'y': 4.1107}]}],\n", "          'spans': [{'offset': 515, 'length': 7}],\n", "          'confidence': 0.825},\n", "         'VendorAddress': {'value_type': 'address',\n", "          'value': {'house_number': '26',\n", "           'po_box': None,\n", "           'road': 'Harford Street',\n", "           'city': None,\n", "           'state': 'ACT',\n", "           'postal_code': '2905',\n", "           'country_region': None,\n", "           'street_address': '26 Harford Street',\n", "           'unit': None,\n", "           'city_district': None,\n", "           'state_district': None,\n", "           'suburb': 'Richardson',\n", "           'house': None,\n", "           'level': None},\n", "          'content': '26 Harford Street\\nRichardson, ACT 2905',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.3295, 'y': 1.8859},\n", "             {'x': 1.743, 'y': 1.8858},\n", "             {'x': 1.743, 'y': 2.22},\n", "             {'x': 0.3295, 'y': 2.2201}]}],\n", "          'spans': [{'offset': 69, 'length': 17},\n", "           {'offset': 93, 'length': 20}],\n", "          'confidence': 0.732},\n", "         'VendorAddressRecipient': {'value_type': 'string',\n", "          'value': 'Mr. <PERSON><PERSON>',\n", "          'content': 'Mr. <PERSON><PERSON>',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.3295, 'y': 1.7044},\n", "             {'x': 1.5472, 'y': 1.6983},\n", "             {'x': 1.5479, 'y': 1.8524},\n", "             {'x': 0.3303, 'y': 1.8585}]}],\n", "          'spans': [{'offset': 28, 'length': 18}],\n", "          'confidence': 0.885},\n", "         'VendorName': {'value_type': 'string',\n", "          'value': 'Mr. <PERSON><PERSON>',\n", "          'content': 'Mr. <PERSON><PERSON>',\n", "          'bounding_regions': [{'page_number': 1,\n", "            'polygon': [{'x': 0.3295, 'y': 1.7044},\n", "             {'x': 1.5472, 'y': 1.6983},\n", "             {'x': 1.5479, 'y': 1.8524},\n", "             {'x': 0.3303, 'y': 1.8585}]}],\n", "          'spans': [{'offset': 28, 'length': 18}],\n", "          'confidence': 0.885}},\n", "        'confidence': 1.0}]}}]}}}"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'dicta' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[79], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m claimno, claim_info \u001b[38;5;129;01min\u001b[39;00m \u001b[43mdicta\u001b[49m\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m      2\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m docfile, doc_info \u001b[38;5;129;01min\u001b[39;00m claim_info[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdocfiles\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m      3\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m info \u001b[38;5;129;01min\u001b[39;00m doc_info[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mparse_result\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n", "\u001b[0;31mNameError\u001b[0m: name 'dicta' is not defined"]}], "source": ["for claimno, claim_info in dicta.items():\n", "    for docfile, doc_info in claim_info[\"docfiles\"].items():\n", "        for info in doc_info[\"parse_result\"]:\n", "            print(info)\n", "            service_provider_info = prepare_service_provider_info(info)\n", "            service_provider_info.update(fuzzy_match_service_provider(service_provider_info))\n", "            doc_info[\"service_provider_info\"] = service_provider_info"]}, {"cell_type": "code", "execution_count": 223, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'phone_home': ['0423 243 167'],\n", " 'phone_work': ['0423 243 167'],\n", " 'fax': ['0423 243 167'],\n", " 'phone_mobile': [],\n", " 'email': ['<EMAIL>'],\n", " 'web': [],\n", " 'abn': [],\n", " 'Name': 'Mr. <PERSON><PERSON> Mr. <PERSON><PERSON>',\n", " 'Address': '26 Harford Street Richardson, ACT 2905',\n", " 'streetname_name': '26 Harford Street',\n", " 'suburb_name': '<PERSON>',\n", " 'best_match_list': ['CT1622858', 'angels touch vet'],\n", " 'best_match_evidence': (['Address'], 1, 0.71),\n", " 'best_match_list2': ['CT0416692', 'northside vet'],\n", " 'best_match_evidence2': (['Address'], 0, 0.6),\n", " 'list': [(9933, 1, 0.71, ['Address']),\n", "  (2974, 1, 0.6, ['Address']),\n", "  (7876, 1, 0.58, ['Address']),\n", "  (6504, 1, 0.56, ['Address']),\n", "  (6674, 1, 0.56, ['Address'])]}"]}, "execution_count": 223, "metadata": {}, "output_type": "execute_result"}], "source": ["dicta['c']['docfiles']['f700d43a-6716-438d-b886-65e441e0422b.pdf']['service_provider_info']"]}, {"cell_type": "code", "execution_count": 182, "metadata": {}, "outputs": [{"data": {"text/plain": ["***********.0"]}, "execution_count": 182, "metadata": {}, "output_type": "execute_result"}], "source": ["serv_prov_raw[serv_prov_raw['ABN']==***********]['ABN'].iloc[0]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "unhashable type: 'slice'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[30], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mmp_dataset\u001b[49m\u001b[43m[\u001b[49m\u001b[43m:\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m\n", "\u001b[0;31mTypeError\u001b[0m: unhashable type: 'slice'"]}], "source": ["mp_dataset[:1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["for claimno, claim_info in mp_dataset.items():\n", "    for docfile, doc_info in claim_info[\"docfiles\"].items():\n", "        for info in doc_info[\"parse_result\"]:\n", "            service_provider_info = prepare_service_provider_info(info)\n", "            service_provider_info.update(fuzzy_match_service_provider(service_provider_info))\n", "            doc_info[\"service_provider_info\"] = service_provider_info"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["for claimno, claim_info in mp_dataset.items():\n", "    for docfile, doc_info in claim_info[\"docfiles\"].items():\n", "        for info in doc_info[\"parse_result\"]:\n", "            service_provider_info = prepare_service_provider_info(info)\n", "            service_provider_info.update(fuzzy_match_service_provider(service_provider_info))\n", "            doc_info[\"service_provider_info\"] = service_provider_info"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"text/plain": ["1039"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["len( mp_dataset)"]}, {"cell_type": "code", "execution_count": 169, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ServiceProviderNo</th>\n", "      <th>ServiceProviderName</th>\n", "      <th>ServiceProviderType</th>\n", "      <th>PracticeTypeCode</th>\n", "      <th>Address</th>\n", "      <th>City</th>\n", "      <th>State</th>\n", "      <th>PostCode</th>\n", "      <th>PhoneNo_Home</th>\n", "      <th>PhoneNo_Work</th>\n", "      <th>PhoneNo_Mobile</th>\n", "      <th>FaxNo</th>\n", "      <th>Email</th>\n", "      <th>HomePage</th>\n", "      <th>ABN</th>\n", "      <th>Is_Blocked</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>618</th>\n", "      <td>*********</td>\n", "      <td>Marion Animal Hospital</td>\n", "      <td>Veterinary Practice</td>\n", "      <td>NaN</td>\n", "      <td>827 Marion Road</td>\n", "      <td>Mitchell Park</td>\n", "      <td>SA</td>\n", "      <td>5043.0</td>\n", "      <td>**********</td>\n", "      <td>08 8277 3733</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><EMAIL></td>\n", "      <td>www.southvet.com.au</td>\n", "      <td>7.708332e+10</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>789</th>\n", "      <td>CT0004288</td>\n", "      <td>Plympton Veterinary Clinic</td>\n", "      <td>Veterinary Practice</td>\n", "      <td>SMALL</td>\n", "      <td>284 Anzac Highway</td>\n", "      <td>Plympton</td>\n", "      <td>SA</td>\n", "      <td>5038.0</td>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td><EMAIL></td>\n", "      <td>www.plymptonvetclinic.com</td>\n", "      <td>7.708332e+10</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>858</th>\n", "      <td>CT0004645</td>\n", "      <td>Seaford Veterinary Hospital</td>\n", "      <td>Veterinary Practice</td>\n", "      <td>NaN</td>\n", "      <td>248 Seaford Road</td>\n", "      <td>Seaford</td>\n", "      <td>VIC</td>\n", "      <td>3198.0</td>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "      <td>NaN</td>\n", "      <td>0397825233</td>\n", "      <td><EMAIL></td>\n", "      <td>https://www.seafordvet.com.au/</td>\n", "      <td>7.708332e+10</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1132</th>\n", "      <td>CT0006250</td>\n", "      <td>Southern Animal Hospital</td>\n", "      <td>Veterinary Practice</td>\n", "      <td>NaN</td>\n", "      <td>130 Main South Road</td>\n", "      <td>Morphett Vale</td>\n", "      <td>SA</td>\n", "      <td>5162.0</td>\n", "      <td>**********</td>\n", "      <td>(08) 8382 3666</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td><EMAIL></td>\n", "      <td>www.southvet.com.au</td>\n", "      <td>7.708332e+10</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     ServiceProviderNo          ServiceProviderName  ServiceProviderType  \\\n", "618          *********       Marion Animal Hospital  Veterinary Practice   \n", "789          CT0004288   Plympton Veterinary Clinic  Veterinary Practice   \n", "858          CT0004645  Seaford Veterinary Hospital  Veterinary Practice   \n", "1132         CT0006250     Southern Animal Hospital  Veterinary Practice   \n", "\n", "     PracticeTypeCode              Address           City State  PostCode  \\\n", "618               NaN      827 Marion Road  Mitchell Park    SA    5043.0   \n", "789             SMALL    284 Anzac Highway       Plympton    SA    5038.0   \n", "858               NaN     248 Seaford Road       Seaford    VIC    3198.0   \n", "1132              NaN  130 Main South Road  Morphett Vale    SA    5162.0   \n", "\n", "     PhoneNo_Home    PhoneNo_Work PhoneNo_Mobile       FaxNo  \\\n", "618    **********    08 8277 3733            NaN         NaN   \n", "789    **********      **********            NaN  **********   \n", "858    **********      **********            NaN  0397825233   \n", "1132   **********  (08) 8382 3666            NaN  **********   \n", "\n", "                       Email                        HomePage           ABN  \\\n", "618      <EMAIL>             www.southvet.com.au  7.708332e+10   \n", "789      <EMAIL>       www.plymptonvetclinic.com  7.708332e+10   \n", "858   <EMAIL>  https://www.seafordvet.com.au/  7.708332e+10   \n", "1132     <EMAIL>             www.southvet.com.au  7.708332e+10   \n", "\n", "      Is_Blocked  \n", "618          0.0  \n", "789          0.0  \n", "858          0.0  \n", "1132         0.0  "]}, "execution_count": 169, "metadata": {}, "output_type": "execute_result"}], "source": ["serv_prov_raw[serv_prov_raw['ABN']==***********]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"17/09/2024, 14:15\\nMail - <PERSON> - Outlook\\nGreenies Original Regular Dog Dental Treats 36\\nChews\\nGreenies\\nORIGINAL\\nREGULAR :56\\nQuantity: 1\\n$59.99\\n$59.99\\nDAILY THEATS\\nBlackdog Pigs Ears 10 Pack\\nBLACKDOG\\nNATURM.LY GOOD TREATS\\nQuantity: 1\\n$33.49\\n$33.49\\nPig Ears\\nNexgard Spectra Large Dog 6 Pack\\nCAUTION\\nNexGard\\nSPECTRA\\nChemikles for Dogs 15.1 - 30 kg\\nQuantity: 1\\n$99.99\\n$99.99\\n500% 15.1 - 30 kg\\nAdvance Medium Adult Dry Dog Food Turkey With\\nRice 20kg\\nExpert Pet Nutrition'\\nADVANCE\\nADULT\\nQuantity: 1\\n$115.99\\n$115.99\\nKong Wild Knots Durable Inner Rope Giraffe Dog\\nToy Small\\nQuantity: 1\\n$19.49\\n$19.49\\nPaws For Life Cuddle Dwarf Each\\nQuantity: 1\\n$11.49\\n$11.49\\nSavourlife Training Treats Kangaroo 330g\\nO/ HELP SAVE\\nof our profine L\\nSavourLife\\nDG FOOD THAT MAKES A DIFFERENCE\\nQuantity: 1\\n$28.99\\n$28.99\\nAUSTRALIAN KANGAROO\\nTRAINING TREATS2\\nSub Total\\n$369.43\\nDiscount: TIER_DISCOUNT\\n(5.84)\\nI\\nShipping\\nFREE\\nTOTAL\\n$363.59\\nhttps://outlook.live.com/mail/0/id/AQMKADAwATZiZmYAZC05NjU3LTI5NWYtMDACLTAwCgBGAAADYUd2in8Kv0SA%2BKIH03JMrQcAvuqXg3M ...\\n2/5\""]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["mp_dataset['********']['docfiles']['67605337-0cb0-4d5a-8377-ab5b494cdf45.pdf']['parse_result'][0]['content']"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'phone_home': [],\n", " 'phone_work': [],\n", " 'fax': [],\n", " 'phone_mobile': [],\n", " 'email': [],\n", " 'web': [],\n", " 'abn': [],\n", " 'Name': 'Chemist Discount Centre Morley Chemist Discount Centre Morley',\n", " 'Address': '1-3/132 Russell Street Morley.WA6062',\n", " 'streetname_name': '1-3/132 Russell Street',\n", " 'suburb_name': '',\n", " 'best_match_list': ['CT0665522', 'chemmart pharmacy superstore morley'],\n", " 'best_match_evidence': (['Address'], 1, 0.85),\n", " 'best_match_list2': ['CT0896398', 'terrywhite chemmart morley'],\n", " 'best_match_evidence2': (['Address'], 0, 0.85),\n", " 'list': [(4255, 1, 0.85, ['Address']),\n", "  (5562, 1, 0.85, ['Address']),\n", "  (4124, 1, 0.81, ['Address']),\n", "  (7904, 1, 0.69, ['Address']),\n", "  (674, 1, 0.62, ['Address'])]}"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["mp_dataset['C7889982']['docfiles']['1163cf3d-2430-44a1-9747-8ab8b62c23b4.jpg']['service_provider_info']"]}, {"cell_type": "code", "execution_count": 133, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['af3bd8db-362d-4f75-a199-a16736210dde.jpeg', 'f1f1d4f3-39f4-4980-9d04-4e36c76e4da1.jpeg'])"]}, "execution_count": 133, "metadata": {}, "output_type": "execute_result"}], "source": ["mp_dataset['C7631537']['docfiles'].keys()"]}, {"cell_type": "code", "execution_count": 174, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'phone_home': [],\n", " 'phone_work': [],\n", " 'fax': [],\n", " 'phone_mobile': [],\n", " 'email': [],\n", " 'web': [],\n", " 'abn': ['***********'],\n", " 'Name': 'Brighton Vet',\n", " 'Address': '5 Kingsbridge Blvd Butler WA 6036',\n", " 'streetname_name': '5 Kingsbridge Blvd',\n", " 'suburb_name': '<PERSON>',\n", " 'best_match_list': ['CT0007870', 'brighton vet'],\n", " 'best_match_evidence': (['Name', 'Address'], 2, 1.88),\n", " 'best_match_list2': ['CT0007307', 'bicton vet'],\n", " 'best_match_evidence2': (['Name'], 0, 0.82),\n", " 'list': [(1502, 2, 1.88, ['Name', 'Address']),\n", "  (1327, 1, 0.82, ['Name']),\n", "  (336, 1, 0.8, ['Name']),\n", "  (642, 1, 0.77, ['Name']),\n", "  (676, 1, 0.77, ['Name'])]}"]}, "execution_count": 174, "metadata": {}, "output_type": "execute_result"}], "source": ["mp_dataset['C7631537']['docfiles']['af3bd8db-362d-4f75-a199-a16736210dde.jpeg']['service_provider_info'] "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ser"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## combine with existing data sheet"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["claimno2spno = {}\n", "for claimno, claim_info in mp_dataset.items():\n", "    doc2spno = {}\n", "    for docfile, doc_info in claim_info[\"docfiles\"].items():\n", "        spnames = []\n", "        spnos = []\n", "        spevidences = []\n", "        for info in doc_info[\"parse_result\"]:\n", "            spevidence= doc_info[\"service_provider_info\"][\"best_match_evidence\"][0]\n", "            spno, spname = doc_info[\"service_provider_info\"][\"best_match_list\"]\n", "            spnos.append(spno)\n", "            spnames.append(spname)\n", "            spevidences.append(spevidence)\n", "            \n", "        doc2spno[docfile] = {\n", "            \"ServiceProviderName\": spnames,\n", "            \"ServiceProviderNo\": spnos,\n", "            \"ServiceProviderMatch\": spevidences\n", "        }\n", "    claimno2spno[claimno] = doc2spno\n", "    "]}, {"cell_type": "code", "execution_count": 171, "metadata": {}, "outputs": [], "source": ["anno_df = pd.read_excel(ANNOTATION_PATH)"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["anno_df[\"ServiceProviderNo\"] = anno_df.apply(lambda x: claimno2spno.get(x[\"claimno\"], {}).get(x[\"docfile\"], {}).get(\"ServiceProviderNo\", \"\"), axis=1)\n", "anno_df[\"ServiceProviderName\"] = anno_df.apply(lambda x: claimno2spno.get(x[\"claimno\"], {}).get(x[\"docfile\"], {}).get(\"ServiceProviderName\", \"\"), axis=1)\n", "anno_df[\"ServiceProviderMatch\"] = anno_df.apply(lambda x: claimno2spno.get(x[\"claimno\"], {}).get(x[\"docfile\"], {}).get(\"ServiceProviderMatch\", \"\"), axis=1)"]}, {"cell_type": "code", "execution_count": 173, "metadata": {}, "outputs": [], "source": ["# anno_df.to_excel(\"/workspaces/OCR_in_house/data/OCR_in_house/res/1000_2024_sample_evaluation/1000_samples_DI_rule_res_spno.xlsx\", index=False)\n", "anno_df.to_excel(ROOTDIR/\"data/res/1000_2024_sample_evaluation/1000_samples_DI_rule_res_email_number_fuzzy__block_evidence.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["anno_df.to_excel(\"/home/<USER>/repos/OCR_in_house/data/nz_csp_samples_DI_rule_res2.xlsx\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "OCR_in_house", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}