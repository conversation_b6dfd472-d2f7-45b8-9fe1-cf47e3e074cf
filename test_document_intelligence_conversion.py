#!/usr/bin/env python3
"""
Test script for document_intelligence_rule_res_to_df function.
"""

import sys
import os
import json
import pandas as pd

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from document_processing import document_intelligence_rule_res_to_df

def test_with_sample_data():
    """Test the function with a small sample of data."""
    
    # Create sample data that matches the structure we observed
    sample_data = {
        "sample_file_1.pdf": [
            {
                "service_provider": "Test Veterinary Clinic",
                "service_provider_address": "123 Test Street, Test City",
                "content": "Sample invoice content...",
                "invoice_no": "INV001",
                "invoice_date": "2025-01-15",
                "invoice_total": 150.50,
                "service_provider_conf": 0.95,
                "invoice_no_conf": 0.98,
                "invoice_date_conf": 0.97,
                "invoice_total_conf": 0.96,
                "treatments": [
                    {
                        "treatment_date": "2025-01-15",
                        "treatment": "Vaccination",
                        "amount": 75.25,
                        "treatment_date_conf": 0.97,
                        "treatment_conf": 0.94,
                        "amount_conf": 0.96,
                        "treatmentline_conf": 0.85
                    },
                    {
                        "treatment_date": "2025-01-15",
                        "treatment": "Health Check",
                        "amount": 75.25,
                        "treatment_date_conf": 0.97,
                        "treatment_conf": 0.93,
                        "amount_conf": 0.95,
                        "treatmentline_conf": 0.84
                    }
                ],
                "ABN": ["***********"],
                "rule_res": "POSTPROCESS: Treatment Line Curation"
            }
        ],
        "sample_file_2.pdf": [
            {
                "service_provider": "Another Vet Clinic",
                "service_provider_address": "456 Another Street, Another City",
                "content": "Another sample invoice content...",
                "invoice_no": "INV002",
                "invoice_date": "2025-01-16",
                "invoice_total": 200.00,
                "service_provider_conf": 0.92,
                "invoice_no_conf": 0.99,
                "invoice_date_conf": 0.98,
                "invoice_total_conf": 0.97,
                "treatments": [],  # Empty treatments list
                "ABN": [],
                "rule_res": "FALL OUT: Empty treatment list"
            }
        ]
    }
    
    # Convert to DataFrame
    df = document_intelligence_rule_res_to_df(sample_data)
    
    # Print results
    print("Conversion successful!")
    print(f"DataFrame shape: {df.shape}")
    print("\nColumn names:")
    for i, col in enumerate(df.columns):
        print(f"{i+1:2d}. {col}")
    
    print("\nFirst few rows:")
    print(df.head())
    
    print("\nDataFrame info:")
    print(df.info())
    
    return df

def test_with_real_data():
    """Test with real data if available."""
    
    # Try to load real data from the samples
    sample_file = "data/samples/nz_csp_samples_DI_rule.json"
    
    if os.path.exists(sample_file):
        print(f"\nTesting with real data from {sample_file}")
        
        try:
            with open(sample_file, 'r') as f:
                real_data = json.load(f)
            
            # Take only first few entries for testing
            limited_data = dict(list(real_data.items())[:3])
            
            df = document_intelligence_rule_res_to_df(limited_data)
            
            print(f"Real data conversion successful!")
            print(f"DataFrame shape: {df.shape}")
            print("\nSample of real data:")
            print(df[['file_path', 'service_provider', 'invoice_no', 'treatment', 'treatment_amount']].head())
            
            return df
            
        except Exception as e:
            print(f"Error loading real data: {e}")
            return None
    else:
        print(f"Real data file not found: {sample_file}")
        return None

if __name__ == "__main__":
    print("Testing document_intelligence_rule_res_to_df function")
    print("=" * 60)
    
    # Test with sample data
    print("1. Testing with sample data:")
    sample_df = test_with_sample_data()
    
    # Test with real data if available
    print("\n" + "=" * 60)
    print("2. Testing with real data:")
    real_df = test_with_real_data()
    
    print("\n" + "=" * 60)
    print("Testing completed!")
