"""
OCR functions for document processing.
"""
import os
import json
import pickle as pk
import time
from pathlib import Path
from typing import List, Dict, Iterable
import fitz  # PyMuPDF
from paddleocr import PaddleOCR
from fitz import FileDataError
from loguru import logger
import pandas as pd
from src.config import MAX_RETRY, PADDLE_OCR_CHUNK_SIZE

# Default chunk size imported from config
DEFAULT_OCR_CHUNK_SIZE = PADDLE_OCR_CHUNK_SIZE

VALID_SUFFIXES = {".pdf", ".PDF", ".png", ".jpg", ".jpeg"}

def paddleocr_extract_consultation_notes(file_path: str, ocr: PaddleOCR = None) -> str:
    """
    Extracts text from a PDF or image file using PaddleOCR, handling broken files.

    Args:
        file_path: The full path to the file.

    Returns:
        A string containing the extracted text, or an empty string if the
        file is broken, unsupported, or contains no text.
    """
    file_suffix = Path(file_path).suffix.lower()
    logger.info(f"Processing {file_path}")

    # Initialize result to None
    result = None

    try:
        if file_suffix in [".pdf", ".PDF"]:
            pdf_document = fitz.open(file_path)
            number_of_pages = pdf_document.page_count
            pdf_document.close()
            logger.info(f"File {file_path} has {number_of_pages} pages")
            # If shared instance not provided, create one tailored to page count
            local_ocr = ocr or PaddleOCR(use_angle_cls=True, lang="en", page_num=number_of_pages)
            result = local_ocr.ocr(file_path, cls=True)
        elif file_suffix in [".png", ".jpg", ".jpeg"]:
            local_ocr = ocr or PaddleOCR(use_angle_cls=True, lang="en")
            result = local_ocr.ocr(file_path, cls=True)
        else:
            raise TypeError(f"Unsupported file type: {file_suffix}")

    except Exception as e:
        # Catch exceptions from broken/corrupted files (e.g., from fitz.open or ocr.ocr)
        logger.error(f"Could not process broken or corrupted file: {file_path}. Error: {e}")
        return "" # Return empty string for broken files

    # Process and sort the OCR results if extraction was successful
    if result and result[0] is not None:
        def get_sort_key(item):
            # Sort by top-left y coordinate, then x coordinate
            top_left_y = item[0][0][1]
            top_left_x = item[0][0][0]
            return top_left_y, top_left_x

        sorted_pages = []
        for page in result:
            if page:
                # Sort lines within each page
                sorted_page = sorted(page, key=get_sort_key)
                sorted_pages.append(sorted_page)

        if not sorted_pages:
            logger.error(f"No content extracted from {file_path}")
            return ""

        # Combine text from all pages
        all_text = []
        for page in sorted_pages:
            page_text = []
            for line in page:
                # line[1][0] contains the text part of the OCR result
                if line and len(line) > 1 and len(line[1]) > 0:
                    page_text.append(line[1][0])
            all_text.append(" ".join(page_text))

        return "\n".join(all_text)

    # Return an empty string if no result was generated
    logger.warning(f"No text detected in {file_path}")
    return ""

def _atomic_write_df(df: pd.DataFrame, target_path: str) -> None:
    """Write DataFrame atomically to avoid partial writes on crash."""
    tmp_path = f"{target_path}.tmp"
    df.to_csv(tmp_path, index=False)
    os.replace(tmp_path, target_path)

def _filter_files(data_folder: str) -> List[str]:
    return [f for f in sorted(os.listdir(data_folder)) if Path(f).suffix in VALID_SUFFIXES]

def _retry_extract(file_full_path: str, ocr: PaddleOCR) -> str:
    """Retry wrapper around paddleocr extraction, limited by MAX_RETRY."""
    attempts = 0
    while attempts < MAX_RETRY:
        attempts += 1
        try:
            return paddleocr_extract_consultation_notes(file_full_path, ocr=ocr)
        except Exception as e:
            logger.warning(f"Attempt {attempts}/{MAX_RETRY} failed for {file_full_path}: {e}")
            time.sleep(min(2 * attempts, 10))  # simple backoff
    logger.error(f"Giving up on {file_full_path} after {MAX_RETRY} attempts")
    return ""

def load_paddleocr_res_chunked(pcr_res_path: str, data_folder: str, chunk_size: int = DEFAULT_OCR_CHUNK_SIZE) -> List[Dict]:
    """
    Incrementally load or build PaddleOCR results in chunks.

    1. Load existing CSV if present (tolerate parse errors by rebuilding missing segment).
    2. Determine which files haven't been processed yet.
    3. Process remaining files in chunks, writing after each chunk (atomic).
    4. Return full list of records.
    """
    existing_records: List[Dict] = []
    processed_files: set = set()
    need_rebuild = False
    if os.path.exists(pcr_res_path):
        try:
            df_existing = pd.read_csv(pcr_res_path)
            if "file_path" not in df_existing.columns or "content" not in df_existing.columns:
                logger.warning("Existing PaddleOCR cache missing required columns. Rebuilding.")
                need_rebuild = True
            else:
                df_existing[["content"]] = df_existing[["content"]].fillna("")
                existing_records = df_existing.to_dict(orient="records")
                processed_files = {r["file_path"] for r in existing_records}
        except Exception as e:
            logger.warning(f"Failed to read existing PaddleOCR cache ({pcr_res_path}): {e}. Will attempt partial rebuild.")
            need_rebuild = True

    all_files = _filter_files(data_folder)
    if need_rebuild:
        # If rebuilding, discard existing (safer than mixing corrupted data)
        existing_records = []
        processed_files = set()

    remaining_files = [f for f in all_files if f not in processed_files]
    if not remaining_files:
        logger.info("No new files to process for PaddleOCR.")
        return existing_records

    logger.info(f"PaddleOCR: {len(processed_files)} already processed, {len(remaining_files)} remaining. Chunk size={chunk_size}.")

    # Initialize one OCR instance (generic; pdf page_num handled per file if not supplied)
    ocr = PaddleOCR(use_angle_cls=True, lang="en")

    new_records: List[Dict] = []
    start_time = time.time()
    for i in range(0, len(remaining_files), chunk_size):
        chunk = remaining_files[i:i+chunk_size]
        chunk_start = time.time()
        for fname in chunk:
            full_path = os.path.join(data_folder, fname)
            try:
                content = _retry_extract(full_path, ocr)
            except TypeError:  # unsupported file type already filtered, but safeguard
                content = ""
            new_records.append({"file_path": fname, "content": content})
        # Persist after each chunk
        combined = existing_records + new_records
        _atomic_write_df(pd.DataFrame(combined), pcr_res_path)
        elapsed_chunk = time.time() - chunk_start
        logger.info(f"PaddleOCR chunk processed {len(chunk)} files in {elapsed_chunk:.2f}s. Total processed now {len(combined)}/{len(all_files)}")
    total_elapsed = time.time() - start_time
    logger.info(f"PaddleOCR incremental processing complete in {total_elapsed:.2f}s for {len(new_records)} new files.")
    return existing_records + new_records

def load_paddleocr_res(pcr_res_path: str , data_folder: str, chunk_size: int = None) -> List[Dict]:
    """Backward compatible wrapper. If chunk_size provided or env var set, use chunked loader."""
    if chunk_size is None:
        # Allow env var override
        env_chunk = os.getenv("PADDLE_OCR_CHUNK_SIZE")
        if env_chunk:
            try:
                chunk_size = int(env_chunk)
            except ValueError:
                logger.warning(f"Invalid PADDLE_OCR_CHUNK_SIZE '{env_chunk}', falling back to non-chunked mode.")
    if chunk_size:
        return load_paddleocr_res_chunked(pcr_res_path, data_folder, chunk_size)
    # Original simple behavior
    try:
        df = pd.read_csv(pcr_res_path)
        df[["content"]] = df[["content"]].fillna(value="")
        return df.to_dict(orient="records")
    except FileNotFoundError:
        ocr_result = []
        ocr = PaddleOCR(use_angle_cls=True, lang="en")
        for file_path in _filter_files(data_folder):
            full_path = os.path.join(data_folder, file_path)
            try:
                consultation_note = _retry_extract(full_path, ocr)
            except TypeError:
                consultation_note = ""
            ocr_result.append({"file_path": file_path, "content": consultation_note})
        _atomic_write_df(pd.DataFrame(ocr_result), pcr_res_path)
        return ocr_result

def load_document_intelligence_res(res_data_folder: str, raw_data_folder: str) -> List[Dict]:
    try:
        with open(os.path.join(res_data_folder, "ans.pk"), "rb") as fin:
            ans = pk.load(fin)
    except FileNotFoundError:
        original_file_path_list = sorted(os.listdir(raw_data_folder))
        original_file_path_dict = {}
        for p in original_file_path_list:
            original_file_path_dict[str(Path(p).stem)] = p
        ans = []
        for file_path in sorted(os.listdir(res_data_folder)):
            if file_path.endswith(".json"):
                with open(os.path.join(res_data_folder, file_path), "r") as fin:
                    ans.append(
                        {
                            "file_path": original_file_path_dict[str(Path(file_path).stem)],
                            "invoice": json.load(fin),
                            }
                    )
    return ans