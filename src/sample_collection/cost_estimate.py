import glob
import os

import fitz  # PyMuPDF
import pandas as pd
import tiktoken
from tqdm import tqdm


def estimate_gpt4o_costs(folder_path):
    """
    Estimate the cost of processing PDF documents with GPT-4o.

    Args:
        folder_path (str): Path to the folder containing PDF files

    Returns:
        pd.DataFrame: DataFrame with filename, token counts, page counts, and cost estimates
    """
    # GPT-4o pricing (as of March 2025)
    INPUT_PRICE_PER_1K_TOKENS = 0.005  # $0.005 per 1K tokens for input
    OUTPUT_PRICE_PER_1K_TOKENS = 0.015  # $0.015 per 1K tokens for output
    PER_PAGE_COST = 0.01  # $0.01 per page

    # Get the encoding for GPT-4
    encoding = tiktoken.encoding_for_model("gpt-4")

    # Find all PDF files in the specified folder
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))

    results = []

    for pdf_file in tqdm(pdf_files, desc="Processing PDFs"):
        filename = os.path.basename(pdf_file)

        try:
            # Open PDF and extract text
            document = fitz.open(pdf_file)
            page_count = len(document)

            # Extract text from PDF
            text = ""
            for page_num in range(page_count):
                page = document.load_page(page_num)
                text += page.get_text()
            document.close()

            # Count tokens
            tokens = len(encoding.encode(text))

            # Calculate costs
            input_cost = (tokens / 1000) * INPUT_PRICE_PER_1K_TOKENS
            per_page_cost = page_count * PER_PAGE_COST

            # Estimate output tokens and cost
            estimated_output_tokens = int(tokens * 0.2)
            output_cost = (estimated_output_tokens / 1000) * OUTPUT_PRICE_PER_1K_TOKENS

            total_cost = input_cost + output_cost + per_page_cost

            results.append(
                {
                    "filename": filename,
                    "page_count": page_count,
                    "input_tokens": tokens,
                    "estimated_output_tokens": estimated_output_tokens,
                    "input_cost": input_cost,
                    "output_cost": output_cost,
                    "per_page_cost": per_page_cost,
                    "total_cost": total_cost,
                }
            )

        except Exception as e:
            results.append({"filename": filename, "error": str(e)})

    # Create DataFrame
    results_df = pd.DataFrame(results)

    # Add summary row
    if not results_df.empty and "input_tokens" in results_df.columns:
        summary = {
            "filename": "TOTAL",
            "page_count": results_df["page_count"].sum(),
            "input_tokens": results_df["input_tokens"].sum(),
            "estimated_output_tokens": results_df["estimated_output_tokens"].sum(),
            "input_cost": results_df["input_cost"].sum(),
            "output_cost": results_df["output_cost"].sum(),
            "per_page_cost": results_df["per_page_cost"].sum(),
            "total_cost": results_df["total_cost"].sum(),
        }
        results_df = pd.concat([results_df, pd.DataFrame([summary])], ignore_index=True)

    return results_df


if __name__ == "__main__":
    # Path to the folder containing PDF files
    pdf_folder = input("Enter the path to the folder containing PDFs: ")

    if not os.path.exists(pdf_folder):
        print(f"Folder '{pdf_folder}' does not exist.")
    else:
        # Estimate costs
        cost_estimates = estimate_gpt4o_costs(pdf_folder)

        # Display results
        if not cost_estimates.empty:
            pd.set_option("display.max_rows", None)
            pd.set_option("display.width", 140)
            pd.set_option(
                "display.float_format",
                lambda x: "${:.4f}".format(x) if isinstance(x, float) else x,
            )
            print("\nGPT-4o Cost Estimates:")
            print(cost_estimates.to_string(index=False))

            # Save results to CSV
            csv_path = os.path.join(pdf_folder, "gpt4o_cost_estimates.csv")
            cost_estimates.to_csv(csv_path, index=False)
            print(f"\nResults saved to: {csv_path}")
        else:
            print("No PDF files were found or processed successfully.")
