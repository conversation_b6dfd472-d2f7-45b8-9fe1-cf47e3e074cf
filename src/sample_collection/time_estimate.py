"""
Document Processing Benchmark: GPT-4o vs. Azure Document Intelligence

This script processes documents in a folder using both GPT-4o and Azure Document Intelligence,
measures the processing time, and saves the results to a DataFrame.
"""

import os
import time

from dotenv import load_dotenv

load_dotenv()
import docx
import openai
import pandas as pd
import PyPDF2
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential


def load_api_keys():
    """Load API keys from environment variables"""
    # For Azure Document Intelligence
    azure_di_endpoint = os.getenv("AZURE_FORM_RECOGNIZER_ENDPOINT")
    azure_di_key = os.getenv("AZURE_FORM_RECOGNIZER_KEY")

    # For Azure OpenAI GPT-4o
    azure_openai_key = os.getenv("AZURE_OPENAI_KEY")
    azure_openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    azure_openai_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT")

    # Validate Azure OpenAI credentials
    if not azure_openai_key:
        raise ValueError("AZURE_OPENAI_KEY environment variable not set")
    if not azure_openai_endpoint:
        raise ValueError("AZURE_OPENAI_ENDPOINT environment variable not set")
    if not azure_openai_deployment:
        raise ValueError("AZURE_OPENAI_DEPLOYMENT environment variable not set")

    # Validate Azure Document Intelligence credentials
    if not azure_di_endpoint:
        raise ValueError("AZURE_FORM_RECOGNIZER_ENDPOINT environment variable not set")
    if not azure_di_key:
        raise ValueError("AZURE_FORM_RECOGNIZER_KEY environment variable not set")

    # Initialize Azure OpenAI client
    openai_client = openai.AzureOpenAI(
        api_key=azure_openai_key,
        api_version="2023-05-15",  # Use appropriate version for your deployment
        azure_endpoint=azure_openai_endpoint,
    )

    return openai_client, azure_openai_deployment, azure_di_endpoint, azure_di_key


def extract_text_from_document(file_path):
    """Extract text from various document formats"""
    file_extension = os.path.splitext(file_path)[1].lower()

    if file_extension == ".pdf":
        return extract_text_from_pdf(file_path)
    elif file_extension in [".docx", ".doc"]:
        return extract_text_from_word(file_path)
    elif file_extension in [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".gif"]:
        # For images, we'll return a placeholder message
        return "[Image file - text extraction for GPT-4o not applicable]"
    else:
        return f"[Unsupported file format: {file_extension}]"


def extract_text_from_pdf(file_path):
    """Extract text from PDF document"""
    text = ""
    try:
        with open(file_path, "rb") as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num in range(len(pdf_reader.pages)):
                page_text = pdf_reader.pages[page_num].extract_text()
                if page_text:
                    text += page_text + "\n"
    except Exception as e:
        text = f"[Error extracting text from PDF: {e}]"
    return text


def extract_text_from_word(file_path):
    """Extract text from Word document"""
    try:
        doc = docx.Document(file_path)
        return "\n".join([paragraph.text for paragraph in doc.paragraphs])
    except Exception as e:
        return f"[Error extracting text from Word document: {e}]"


def process_with_azure_document_intelligence(file_path, azure_endpoint, azure_key):
    """Process document with Azure Document Intelligence"""
    start_time = time.time()

    try:
        document_analysis_client = DocumentAnalysisClient(
            endpoint=azure_endpoint, credential=AzureKeyCredential(azure_key)
        )

        with open(file_path, "rb") as f:
            poller = document_analysis_client.begin_analyze_document(
                "prebuilt-document", f
            )
            result = poller.result()

        # Extract the text from the result
        extracted_text = ""
        for page in result.pages:
            for line in page.lines:
                extracted_text += line.content + "\n"

        response = extracted_text
    except Exception as e:
        response = f"Error processing with Azure Document Intelligence: {e}"

    processing_time = time.time() - start_time
    return response, processing_time


def process_with_gpt4o(document_text, file_path, openai_client, deployment_name):
    """Process document text with Azure OpenAI GPT-4o"""
    start_time = time.time()

    file_extension = os.path.splitext(file_path)[1].lower()
    if file_extension in [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".gif"]:
        response_text = "GPT-4o processing skipped (image file)"
        processing_time = 0
    else:
        try:
            # Truncate text if too long
            if len(document_text) > 8000:
                document_text = document_text[:8000] + "...[truncated]"

            # Using the Azure OpenAI API
            response = openai_client.chat.completions.create(
                model=deployment_name,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a document analyzer.\
                            Your job is to analyze the following page of a document and accurately and precisely extract all the information present on the page. \
                            You are required to provide the following elements: Title, Description, Paragraphs, Tables, Key/Value pairs, Images, and any other element that is present on the page.\
                            For the title, you should extract the title of the document. If it is not available, leave it as a blank string. \
                            For the description, you should provide a one or two word summary of the document. If it is not available, leave it as a blank string.\
                            For paragraphs, you should seperate individual paragraphs into seperate fields. These fields should contain a title and the text of the paragraph. If a paragraph does not have a title, leave it as a blank string.\
                            For key/value pairs, you should extract the key and the value in seperate fields.\
                            For tables, you should extract the table title, column headers and the table rows in seperate fields.\
                            For images, you should include the text that is present in the image as well as a brief description of the image.\
                            For any text fields extracted, identify key value pairs and extract them as seperate fields.\
                            Each of these elements should be extracted and returned in the correct format.\
                            If a field does not exist, leave it as a blank string.\
                            You MUST include every element that is present on the page.\
                            Your final response should be a JSON object with the following structure: {'title': "
                        ", 'description': "
                        ", 'paragraphs': [ {'title': "
                        ", 'text': "
                        "} ],  'key/values': [ {'key': "
                        ", 'value': "
                        "} ],  'tables': [ {'title': "
                        ", 'columns': [], 'rows': [] } ], 'images': [ {'text': "
                        ", 'description': "
                        "} ], 'other': []}.",
                    },
                    {
                        "role": "user",
                        "content": f"Please analyze the following document and return the correct information:\n\n{document_text}",
                    },
                ],
            )
            response_text = response.choices[0].message.content
        except Exception as e:
            response_text = f"Error processing with Azure OpenAI GPT-4o: {e}"

        processing_time = time.time() - start_time

    return response_text, processing_time


def process_with_azure_document_intelligence(file_path, azure_endpoint, azure_key):
    """Process document with Azure Document Intelligence"""
    start_time = time.time()

    try:
        document_analysis_client = DocumentAnalysisClient(
            endpoint=azure_endpoint, credential=AzureKeyCredential(azure_key)
        )

        with open(file_path, "rb") as f:
            poller = document_analysis_client.begin_analyze_document(
                "prebuilt-document", f
            )
            result = poller.result()

        # Extract the text from the result
        extracted_text = ""
        for page in result.pages:
            for line in page.lines:
                extracted_text += line.content + "\n"

        response = extracted_text
    except Exception as e:
        response = f"Error processing with Azure Document Intelligence: {e}"

    processing_time = time.time() - start_time
    return response, processing_time


def process_folder(
    folder_path, openai_client, deployment_name, azure_di_endpoint, azure_di_key
):
    """Process all documents in a folder and measure time"""
    results = []
    supported_extensions = [
        ".pdf",
        ".docx",
        ".doc",
        ".jpg",
        ".jpeg",
        ".png",
        ".bmp",
        ".tiff",
        ".gif",
    ]

    # Count total documents to process
    doc_count = sum(
        1
        for f in os.listdir(folder_path)
        if os.path.isfile(os.path.join(folder_path, f))
        and os.path.splitext(f)[1].lower() in supported_extensions
    )

    if doc_count == 0:
        print("No supported documents found in the folder.")
        return pd.DataFrame()

    print(f"Found {doc_count} documents to process.")
    processed_count = 0

    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)

        if os.path.isfile(file_path):
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension in supported_extensions:
                processed_count += 1
                print(f"[{processed_count}/{doc_count}] Processing {filename}...")

                try:
                    # Extract document text for GPT-4o
                    document_text = extract_text_from_document(file_path)

                    # Process with GPT-4o
                    print("  - Processing with GPT-4o...")
                    gpt4o_output, gpt4o_time = process_with_gpt4o(
                        document_text, file_path, openai_client, deployment_name
                    )

                    # Process with Azure Document Intelligence
                    print("  - Processing with Azure Document Intelligence...")
                    azure_output, azure_time = process_with_azure_document_intelligence(
                        file_path, azure_di_endpoint, azure_di_key
                    )

                    results.append(
                        {
                            "Document Name": filename,
                            "GPT-4o Processing Time (s)": round(gpt4o_time, 2),
                            "Azure DI Processing Time (s)": round(azure_time, 2),
                            "GPT-4o Output": gpt4o_output,
                            "Azure DI Output": azure_output,
                        }
                    )

                    print(
                        f"  - Completed: GPT-4o: {gpt4o_time:.2f}s, Azure DI: {azure_time:.2f}s"
                    )
                except Exception as e:
                    print(f"  - Error processing {filename}: {e}")
                    results.append(
                        {
                            "Document Name": filename,
                            "GPT-4o Processing Time (s)": 0,
                            "Azure DI Processing Time (s)": 0,
                            "GPT-4o Output": f"Error: {e}",
                            "Azure DI Output": f"Error: {e}",
                        }
                    )

    # Convert results to DataFrame
    results_df = pd.DataFrame(results)
    return results_df


def main():
    print("Document Processing Benchmark: GPT-4o vs. Azure Document Intelligence")
    print("================================================================")

    try:
        # Load API keys
        print("Loading API keys...")
        (
            openai_client,
            azure_openai_deployment,
            azure_di_endpoint,
            azure_di_key,
        ) = load_api_keys()

        # Get folder path from user
        folder_path = input("Enter the folder path containing documents: ")

        if not os.path.isdir(folder_path):
            print("Invalid folder path.")
            return

        # Process documents
        results_df = process_folder(
            folder_path,
            openai_client,
            azure_openai_deployment,
            azure_di_endpoint,
            azure_di_key,
        )

        if results_df.empty:
            print("No documents were processed.")
            return

        # Save results to Excel and CSV
        # Save results to Excel and CSV
        output_excel = os.path.join(folder_path, "time_processing_results.xlsx")
        results_df.to_excel(output_excel, index=False)

        # output_csv = os.path.join(folder_path, "processing_results.csv")
        # results_df.to_csv(output_csv, index=False)

        # Create a summary file
        summary_df = results_df[
            [
                "Document Name",
                "GPT-4o Processing Time (s)",
                "Azure DI Processing Time (s)",
            ]
        ]
        summary_csv = os.path.join(folder_path, "processing_summary.csv")
        summary_df.to_csv(summary_csv, index=False)

        print("\nProcessing complete!")
        print(f"Detailed results saved to: {output_excel}")
        # print(f"CSV results saved to: {output_csv}")
        # print(f"Summary saved to: {summary_csv}")

        print("\nProcessing Summary:")
        print(summary_df.to_string(index=False))

        # Calculate average processing times
        avg_gpt4o = results_df["GPT-4o Processing Time (s)"].mean()
        avg_azure = results_df["Azure DI Processing Time (s)"].mean()
        print("\nAverage processing times:")
        print(f"GPT-4o: {avg_gpt4o:.2f} seconds")
        print(f"Azure Document Intelligence: {avg_azure:.2f} seconds")

    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()
