"""
Mapping functions for OCR processing.
"""
from datetime import datetime
from typing import Di<PERSON>, <PERSON><PERSON>, List
import pandas as pd
from loguru import logger
from copy import deepcopy

def map_claimno_docfile(df: pd.DataFrame) -> Tuple[Dict, Dict]:
    claimno2docfile = {}
    docfile2claimno = {}

    for claimno, docfile in zip(df["ClaimNumber"], df["DocFile"]):
        docfile = docfile.lower()
        if claimno not in claimno2docfile:
            claimno2docfile[claimno] = docfile.lower()

        if docfile not in docfile2claimno:
            docfile2claimno[docfile.lower()] = claimno

    return claimno2docfile, docfile2claimno

def parse_upm_info(df: pd.DataFrame) -> Dict:
    ans = {}

    for info in df.to_dict(orient="records"):
        claim_no = info["ClaimNo"]
        invoice_no = info["InvoiceNo"]
        service_provider = info["ServiceProviderName"]
        service_provider_no = info["ServiceProviderNo"]
        date_treatment = info["DateTreatment"]
        treatment = info["TreatmentDrugDescription"]
        # treatment_code = info["TreatmentDrugCode"]
        amount = info["AmountInclVat"]
        invoice_date = info.get("DateInvoice", "")
        invoice_total = info.get("InvoiceAmount", 0.)

        if claim_no not in ans:
            ans[claim_no] = {
                "claim_no": claim_no,
                "invoice_no": invoice_no,
                "invoice_date": invoice_date,
                "invoice_total": invoice_total,
                "service_provider": service_provider,
                "service_provider_no": service_provider_no,
                "treatments": [
                    {
                        "date_treatment": date_treatment,
                        "treatment": treatment,
                        # "treatment_code": treatment_code,
                        "amount": amount,
                    }
                ]
            }
        else:
            ans[claim_no]["treatments"].append(
                {
                    "date_treatment": date_treatment,
                    "treatment": treatment,
                    # "treatment_code": treatment_code,
                    "amount": amount,
                }
            )
    return ans

def parse_truuth_info(df: pd.DataFrame) -> Dict:
    ans = {}

    for info in df.to_dict(orient="records"):
        claim_no = info["ClaimNumber"]
        ocr_confidence = info["OCRDocumentConfidence"]
        service_provider = info["ServiceProvider"]
        service_provider_no = info["ServiceProviderNo"]
        service_provider_conf = info["ServiceProvider_conf"]
        invoice_date = info["InvoiceDate"]
        invoice_date_conf = info["InvoiceDate_conf"]
        invoice_no = info["InvoiceNumber"]
        invoice_no_conf = info["InvoiceNumber_conf"]
        treatment_date = info["TreatmentDate"]
        treatment_date_conf = info["TreatmentDate_conf"]
        treatment_description = info["TreatmentDescription"]
        treatment_description_conf = info["TreatmentDescription_conf"]
        treatment_amount = info["TreatmentAmount"]
        treatment_amount_conf = info["TreatmentAmount_conf"]
        total_amount = info["TotalAmount"]
        total_amount_conf = info["TotalAmount_conf"]
        truuth_error = info["Truuth_Error"]
 
        if claim_no not in ans:
            ans[claim_no] = {
                "claim_no": claim_no,
                "ocr_confidence": ocr_confidence,
                "service_provider": service_provider,
                "service_provider_no": service_provider_no,
                "service_provider_conf": service_provider_conf,
                "invoice_date": invoice_date,
                "invoice_date_conf": invoice_date_conf,
                "invoice_no": invoice_no,
                "invoice_no_conf": invoice_no_conf,
                "total_amount": total_amount,
                "total_amount_conf": total_amount_conf,
                "truuth_error": truuth_error,

                "treatments": [
                    {
                        "treatment_date": treatment_date,
                        "treatment_date_conf": treatment_date_conf,
                        "treatment_description": treatment_description,
                        "treatment_description_conf": treatment_description_conf,
                        "treatment_amount": treatment_amount,
                        "treatment_amount_conf": treatment_amount_conf,
                    }
                ]
            }
        else:
            ans[claim_no]["treatments"].append(
                {
                    "treatment_date": treatment_date,
                        "treatment_date_conf": treatment_date_conf,
                        "treatment_description": treatment_description,
                        "treatment_description_conf": treatment_description_conf,
                        "treatment_amount": treatment_amount,
                        "treatment_amount_conf": treatment_amount_conf
                }
            )
    return ans

def filter_di_res_dict(di_res_dict: Dict, upm_res_dict: Dict, docfile2claimno: Dict) -> Dict:
    """
    Remove entries from di_res_dict where the corresponding claim numbers don't exist in upm_res_dict.
    """
    filtered_di_res_dict = {}
    removed_count = 0

    # print(docfile2claimno)
    # print(upm_res_dict.keys())
    print(di_res_dict.keys())
    
    for docfile, di_data in di_res_dict.items():
        if docfile.lower() in docfile2claimno:
            claim_no = docfile2claimno[docfile.lower()]
            if claim_no in upm_res_dict:
                filtered_di_res_dict[docfile] = di_data
            else:
                removed_count += 1
        else:
            removed_count += 1
            
    logger.info(f"Removed {removed_count} entries from di_res_dict that had no matching claim numbers in upm_res_dict.")
    logger.info(f"Original di_res_dict size: {len(di_res_dict)}, Filtered di_res_dict size: {len(filtered_di_res_dict)}")
    
    return filtered_di_res_dict

def get_text_similarity(text1, text2):
    """Calculate similarity ratio between two strings"""
    from difflib import SequenceMatcher
    if not text1 or not text2:
        return 0.0
    return SequenceMatcher(None, str(text1).lower(), str(text2).lower()).ratio()

# def gather_all_info(di_res_dict: Dict, 
                   
#                     upm_res_dict: Dict , 
#                     paddleocr_res_dict: Dict, 
#                     claimno2docfile: Dict, 
#                     docfile2claimno: Dict):

#     summary_list = []

#     # Threshold for considering treatments as similar
#     TREATMENT_SIMILARITY_THRESHOLD = 0.7

#     # files processed by Azure Document Intelligence
#     for k, v in di_res_dict.items():
#         docfile = k

#         # [{'service_provider': 'Baldivis Vet Hospital Pty Ltd',
#         # 'invoice_no': '1/928537',
#         # 'invoice_date': datetime.date(2024, 9, 22),
#         # 'invoice_total': 2634.57,
#         # 'service_provider_conf': 0.888,
#         # 'invoice_no_conf': 0.94,
#         # 'invoice_date_conf': 0.941,
#         # 'invoice_total_conf': 0.93,
#         # 'treatments': [{'treatment': 'Consult 19:00 - 23:30',
#         #     'amount': 190.0,
#         #     'treatment_conf': 0.888,
#         #     'amount_conf': 0.889,
#         #     'treatmentline_conf': 0.85},
#         invoices = v

#         mapping_claimno = docfile2claimno[docfile.lower()]

#         if not mapping_claimno:
#             logger.warning(f"No claim number mapping found for docfile: {docfile}. Skipping.")
#             continue

#         logger.info(f"Gather infomation: {mapping_claimno} {docfile}")

#         paddleocr_content = paddleocr_res_dict[docfile.lower()]["content"]

#         if len(invoices) > 1:
#             print(k, mapping_claimno, len(invoices))
#             print("-"*40)
        
#         # Check if the claim number exists in upm_res_dict
#         if mapping_claimno not in upm_res_dict:
#             logger.warning(f"Claim number {mapping_claimno} not found in UPM data. Processing with empty UPM data.")
#             # Create empty UPM info with default values
#             upm_info = {
#                 "claim_no": mapping_claimno,
#                 "invoice_no": "",
#                 "invoice_date": None,
#                 "invoice_total": 0,
#                 "service_provider": "",
#                 "service_provider_no": "",
#                 "treatments": [
#                     {
#                         "date_treatment": "",
#                         "treatment": "",
#                         "amount": "",
#                     }
#                 ]
#             }
#         else:
#             upm_info = upm_res_dict[mapping_claimno]
            
#         for invoice in invoices:
#             # check invoice no
#             di_invoice_no = invoice["invoice_no"]
#             upm_invoice_no = str(upm_info["invoice_no"])
#             di_invoice_no_correct = int(di_invoice_no == upm_invoice_no)
      

#             # check service provider
#             di_service_provider = invoice["service_provider"]
#             di_service_provider_address = invoice["service_provider_address"]
#             upm_service_provider = upm_info["service_provider"]
     
    
#             di_service_provider_correct = int(di_service_provider == upm_service_provider)
            

#             # check invoice date TODO cannot check Truuth results now 15OCT24
#             di_invoice_date = invoice["invoice_date"].isoformat() if not isinstance(invoice["invoice_date"], str) else invoice["invoice_date"]
#             if upm_info["invoice_date"]:
#                 print(upm_info["invoice_date"])
#                 upm_invoice_date = upm_info["invoice_date"].date().isoformat()
#             else:
#                 upm_invoice_date = ""
#             # if upm_info["invoice_date"] != upm_info["invoice_date"].date().isoformat():
#             #     print("invoice_date mismatch", k, mapping_claimno)
#             di_invoice_date_correct = int(di_invoice_date == upm_invoice_date)


#             # check total amount ### TODO
#             di_total_amount = invoice["invoice_total"]
#             upm_total_amount = upm_info["invoice_total"]  
#             # if stat_info.get("invoice_total_upm", -1) != upm_info.get("invoice_total", None):
#             #     print("invoice_total mismatch", k, mapping_claimno)
#             di_total_amount_correct = int(di_total_amount == upm_total_amount)


#             summary = {
#                 "claimno": mapping_claimno,
#                 "docfile": docfile,
#                 "paddleocr_content": paddleocr_content,

#                 "di_conf": min(invoice["invoice_no_conf"], invoice["invoice_date_conf"], invoice["invoice_total_conf"]),

#                 "di_invoice_no": di_invoice_no,
#                 "upm_invoice_no": upm_invoice_no,
#                 "di_invoice_no_correct": di_invoice_no_correct,
#                 "di_invoice_no_conf": invoice["invoice_no_conf"],

#                 "di_service_provider": di_service_provider,
#                 "di_service_provider_address": di_service_provider_address,
#                 "upm_service_provider": upm_service_provider,
#                 "di_service_provider_correct": di_service_provider_correct,
        
#                 "di_service_provider_conf": invoice["service_provider_conf"],
#                 "di_abn": invoice["ABN"],

#                 "di_invoice_date": di_invoice_date,
#                 "upm_invoice_date": upm_invoice_date,
#                 "di_invoice_date_correct": di_invoice_date_correct,
#                 "di_invoice_date_conf": invoice["invoice_date_conf"],

#                 "di_total_amount": di_total_amount,
          
#                 "di_total_amount_correct": di_total_amount_correct,
         
#                 "di_total_amount_conf": invoice["invoice_total_conf"],

#                 "rule_res": invoice["rule_res"],
#                 "if_fallout": int("FALL OUT" in invoice["rule_res"])
#             }

#             # DI treatment 
#             # "treatments": [{'treatment_date': '2024-09-17',
#             # 'treatment': ' Veterinary Consultation & Examination: Health Assessment',
#             # 'amount': 104.0,
#             # 'treatment_date_conf': 0.947,
#             # 'treatment_conf': 0.8899999110000089,
#             # 'amount_conf': 0.888,
#             # 'treatmentline_conf': 0.914},]
#             # UPM treatment
#             # "treatments": [
#             #     {
#             #         "date_treatment": date_treatment,
#             #         "treatment": treatment,
#             #         "amount": amount,
#             #     }
#             # ]
#             sorted_di_treatments = sorted(invoice["treatments"], key=lambda x: x["amount"])
#             sorted_upm_treatments = sorted(upm_info["treatments"], key=lambda x: x["amount"])
#             treatments = []
#             di_pointer, upm_pointer = 0, 0
#             while di_pointer < len(sorted_di_treatments) or upm_pointer < len(sorted_upm_treatments):
#                 try:
#                     di_treatment = sorted_di_treatments[di_pointer]
#                 except IndexError:
#                     assert di_pointer == len(sorted_di_treatments)
#                     di_treatment = {}
#                 try:
#                     upm_treatment = sorted_upm_treatments[upm_pointer]
#                 except IndexError:
#                     assert upm_pointer == len(sorted_upm_treatments)
#                     upm_treatment = {}
#                 # logger.info(di_treatment)

#                 # logger.info(upm_treatment)
#                 if not di_treatment and not upm_treatment:
#                     logger.error(f"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.")
#                     logger.error(f"sorted_di_treatments: {sorted_di_treatments}")
#                     logger.error(f"sorted_upm_treatments: {sorted_upm_treatments}")
#                     logger.error(f"di_pointer: {di_pointer}, upm_pointer: {upm_pointer}")
#                     break

#                 if not di_treatment:
#                     if upm_treatment["date_treatment"]:
#                         upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
#                     treatments.append({
#                         "di_treatment_date": "",
#                         "di_treatment": "",
#                         "di_amount": "",
#                         "di_treatment_date_conf": "",
#                         "di_treatment_conf": "",
#                         "di_amount_conf": "",

#                         "upm_treatment_date": upm_treatment_date,
#                         "upm_treatment": upm_treatment["treatment"],
#                         "upm_amount": upm_treatment["amount"],

#                         "di_treatment_amount_correct" : "",
#                         "di_treatment_date_correct" : ""


#                     })
#                     upm_pointer += 1
#                     continue

#                 if not upm_treatment:
#                     di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]

#                     treatments.append({
#                         "di_treatment_date": di_treatment_date,
#                         "di_treatment": di_treatment["treatment"],
#                         "di_amount": di_treatment["amount"],
#                         "di_treatment_date_conf": di_treatment["treatment_date_conf"],
#                         "di_treatment_conf": di_treatment["treatment_conf"],
#                         "di_amount_conf": di_treatment["amount_conf"],


#                         "upm_treatment_date": "",
#                         "upm_treatment": "",
#                         "upm_amount": "",

#                         "di_treatment_amount_correct" : 0,
#                         "di_treatment_date_correct" : 0

#                     })
#                     di_pointer += 1
#                     continue

#                 if (di_treatment["amount"] == upm_treatment["amount"]) or (di_treatment["amount"] - upm_treatment["amount"]<0.05):
#                     di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]
#                     if upm_treatment["date_treatment"]:
#                         upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
#                     di_treatment_date_correct = int(di_treatment_date == upm_treatment_date )

#                     # Add treatment text similarity calculation
#                     treatment_similarity = get_text_similarity(di_treatment["treatment"], upm_treatment["treatment"])
#                     di_treatment_text_correct = int(treatment_similarity >= TREATMENT_SIMILARITY_THRESHOLD)

#                     treatments.append({
#                         "di_treatment_date": di_treatment_date,
#                         "di_treatment": di_treatment["treatment"],
#                         "di_amount": di_treatment["amount"],
#                         "di_treatment_date_conf": di_treatment["treatment_date_conf"],
#                         "di_treatment_conf": di_treatment["treatment_conf"],
#                         "di_amount_conf": di_treatment["amount_conf"],


#                         "upm_treatment_date": upm_treatment["date_treatment"],
#                         "upm_treatment": upm_treatment_date,
#                         "upm_amount": upm_treatment["amount"],

#                         "di_treatment_amount_correct" : 1,
#                         "di_treatment_date_correct" : di_treatment_date_correct,
#                         "di_treatment_desc_correct": di_treatment_text_correct,
#                         "di_reatment_similarity": treatment_similarity


#                     })
#                     di_pointer += 1
#                     upm_pointer += 1
#                 elif di_treatment["amount"] < upm_treatment["amount"]:
#                     di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]

#                     treatments.append({
#                         "di_treatment_date": di_treatment_date,
#                         "di_treatment": di_treatment["treatment"],
#                         "di_amount": di_treatment["amount"],
#                         "di_treatment_date_conf": di_treatment["treatment_date_conf"],
#                         "di_treatment_conf": di_treatment["treatment_conf"],
#                         "di_amount_conf": di_treatment["amount_conf"],

#                         "upm_treatment_date": "",
#                         "upm_treatment": "",
#                         "upm_amount": "",

#                         "di_treatment_amount_correct" : 0,
#                         "di_treatment_date_correct" : 0,
#                         "di_treatment_desc_correct": 0,
#                         "di_treatment_similarity": 0

#                     })
#                     di_pointer += 1
#                 else:
#                     if upm_treatment["date_treatment"]:
#                         upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
#                     treatments.append({
#                         "di_treatment_date": "",
#                         "di_treatment": "",
#                         "di_amount": "",
#                         "di_treatment_date_conf": "",
#                         "di_treatment_conf": "",
#                         "di_amount_conf": "",

#                         "upm_treatment_date": upm_treatment_date,
#                         "upm_treatment": upm_treatment["treatment"],
#                         "upm_amount": upm_treatment["amount"],

#                         "di_treatment_amount_correct" : "",
#                         "di_treatment_date_correct" : "",
#                         "di_treatment_desc_correct": "",
#                         "di_treatment_similarity": ""


#                     })

#                     upm_pointer += 1
#             summary["treatments"] = treatments
#             summary_list.append(summary)
#     summary_list = sorted(summary_list, key=lambda x: (x["claimno"], x["docfile"]))
#     return summary_list

def gather_all_info_upm_di(di_res_dict: Dict,
                    upm_res_dict: Dict,
                    paddleocr_res_dict: Dict,
                    claimno2docfile: Dict,
                    docfile2claimno: Dict):

    summary_list = []

    # Threshold for considering treatments as similar
    TREATMENT_SIMILARITY_THRESHOLD = 0.7

    # files processed by Azure Document Intelligence
    for k, v in di_res_dict.items():
        docfile = k

        # [{'service_provider': 'Baldivis Vet Hospital Pty Ltd',
        # 'invoice_no': '1/928537',
        # 'invoice_date': datetime.date(2024, 9, 22),
        # 'invoice_total': 2634.57,
        # 'service_provider_conf': 0.888,
        # 'invoice_no_conf': 0.94,
        # 'invoice_date_conf': 0.941,
        # 'invoice_total_conf': 0.93,
        # 'treatments': [{'treatment': 'Consult 19:00 - 23:30',
        #     'amount': 190.0,
        #     'treatment_conf': 0.888,
        #     'amount_conf': 0.889,
        #     'treatmentline_conf': 0.85},
        invoices = v

        mapping_claimno = docfile2claimno[docfile.lower()]

        if not mapping_claimno:
            logger.warning(f"No claim number mapping found for docfile: {docfile}. Skipping.")
            continue

        logger.info(f"Gather infomation: {mapping_claimno} {docfile}")

        paddleocr_content = paddleocr_res_dict[docfile.lower()]["content"]

        if len(invoices) > 1:
            print(k, mapping_claimno, len(invoices))
            print("-"*40)
        
        # Check if the claim number exists in upm_res_dict
        if mapping_claimno not in upm_res_dict:
            logger.warning(f"Claim number {mapping_claimno} not found in UPM data. Processing with empty UPM data.")
            # Create empty UPM info with default values
            upm_info = {
                "claim_no": mapping_claimno,
                "invoice_no": "",
                "invoice_date": None,
                "invoice_total": 0,
                "service_provider": "",
                "service_provider_no": "",
                "service_provider_evidence": "",
                "service_provider2": "",
                "service_provider_no2": "",
                "service_provider_evidence2": "",
                "treatments": [
                    {
                        "date_treatment": "",
                        "treatment": "",
                        "amount": "",
                    }
                ]
            }
        else:
            upm_info = upm_res_dict[mapping_claimno]
            
        for invoice in invoices:
            # check invoice no
            di_invoice_no = invoice["invoice_no"]
            upm_invoice_no = str(upm_info["invoice_no"])
            di_invoice_no_correct = int(di_invoice_no == upm_invoice_no)
    
            # check service provider
            di_service_provider = invoice["service_provider_fm"]
            di_service_provider_no = invoice["service_provider_no_fm"]
            di_service_provider_evidence = invoice["service_provider_evidence"]
            di_service_provider2 = invoice["service_provider_fm2"]
            di_service_provider_no2 = invoice["service_provider_no_fm2"]
            di_service_provider_evidence2 = invoice["service_provider_evidence2"]
            di_service_provider_address = invoice["service_provider_address"]
            upm_service_provider = upm_info["service_provider"]
            upm_service_provider_no = upm_info["service_provider_no"]


            di_service_provider_correct = int(di_service_provider_no == upm_service_provider_no)
            
            # check invoice date TODO cannot check Truuth results now 15OCT24
            di_invoice_date = invoice["invoice_date"].isoformat() if not isinstance(invoice["invoice_date"], str) else invoice["invoice_date"]
            if upm_info["invoice_date"]:
                print(upm_info["invoice_date"])
                upm_invoice_date = upm_info["invoice_date"].date().isoformat()
            else:
                upm_invoice_date = ""
            # if upm_info["invoice_date"] != upm_info["invoice_date"].date().isoformat():
            #     print("invoice_date mismatch", k, mapping_claimno)
            di_invoice_date_correct = int(di_invoice_date == upm_invoice_date)

            # check total amount ### TODO
            di_total_amount = invoice["invoice_total"]
            upm_total_amount = upm_info["invoice_total"]  
            # if stat_info.get("invoice_total_upm", -1) != upm_info.get("invoice_total", None):
            #     print("invoice_total mismatch", k, mapping_claimno)
            di_total_amount_correct = int(abs(di_total_amount - upm_total_amount) < 0.05)

            summary = {
                "claimno": mapping_claimno,
                "docfile": docfile,
                "paddleocr_content": paddleocr_content,

                "di_conf": min(invoice["invoice_no_conf"], invoice["invoice_date_conf"], invoice["invoice_total_conf"]),

                "di_invoice_no": di_invoice_no,
                "upm_invoice_no": upm_invoice_no,
                "di_invoice_no_correct": di_invoice_no_correct,
                "di_invoice_no_conf": invoice["invoice_no_conf"],

                "di_service_provider": di_service_provider,
                "di_service_provider_no": di_service_provider_no,
                "di_service_provider_evidence": di_service_provider_evidence,
                "di_service_provider_fm2": di_service_provider2,
                "di_service_provider_no_fm2": di_service_provider2,
                "di_service_provider_evidence2": di_service_provider_evidence2,
                "di_service_provider_address": di_service_provider_address,
                "upm_service_provider": upm_service_provider,
                "di_service_provider_correct": di_service_provider_correct,
        
                "di_service_provider_conf": invoice["service_provider_conf"],
                "di_abn": invoice["ABN"],

                "di_invoice_date": di_invoice_date,
                "upm_invoice_date": upm_invoice_date,
                "di_invoice_date_correct": di_invoice_date_correct,
                "di_invoice_date_conf": invoice["invoice_date_conf"],

                "di_total_amount": di_total_amount,
                "upm_total_amount": upm_total_amount,

                "di_total_amount_correct": di_total_amount_correct,
        
                "di_total_amount_conf": invoice["invoice_total_conf"],

                "rule_res": invoice["rule_res"],
                "if_fallout": int("FALL OUT" in invoice["rule_res"])
            }

            # DI treatment 
            # "treatments": [{'treatment_date': '2024-09-17',
            # 'treatment': ' Veterinary Consultation & Examination: Health Assessment',
            # 'amount': 104.0,
            # 'treatment_date_conf': 0.947,
            # 'treatment_conf': 0.8899999110000089,
            # 'amount_conf': 0.888,
            # 'treatmentline_conf': 0.914},]
            # UPM treatment
            # "treatments": [
            #     {
            #         "date_treatment": date_treatment,
            #         "treatment": treatment,
            #         "amount": amount,
            #     }
            # ]
            sorted_di_treatments = sorted(invoice["treatments"], key=lambda x: x["amount"])
            sorted_upm_treatments = sorted(upm_info["treatments"], key=lambda x: x["amount"])
            treatments = []
            di_pointer, upm_pointer = 0, 0
            while di_pointer < len(sorted_di_treatments) or upm_pointer < len(sorted_upm_treatments):
                try:
                    di_treatment = sorted_di_treatments[di_pointer]
                except IndexError:
                    assert di_pointer == len(sorted_di_treatments)
                    di_treatment = {}
                try:
                    upm_treatment = sorted_upm_treatments[upm_pointer]
                except IndexError:
                    assert upm_pointer == len(sorted_upm_treatments)
                    upm_treatment = {}
                # logger.info(di_treatment)

                # logger.info(upm_treatment)
                if not di_treatment and not upm_treatment:
                    logger.error(f"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.")
                    logger.error(f"sorted_di_treatments: {sorted_di_treatments}")
                    logger.error(f"sorted_upm_treatments: {sorted_upm_treatments}")
                    logger.error(f"di_pointer: {di_pointer}, upm_pointer: {upm_pointer}")
                    break

                if not di_treatment:
                    if upm_treatment["date_treatment"]:
                        upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",
                        "di_treatment_date_conf": "",
                        "di_treatment_conf": "",
                        "di_amount_conf": "",

                        "upm_treatment_date": upm_treatment_date,
                        "upm_treatment": upm_treatment["treatment"],
                        "upm_amount": upm_treatment["amount"],

                        "di_treatment_amount_correct" : "",
                        "di_treatment_date_correct" : ""
                    })
                    upm_pointer += 1
                    continue

                if not upm_treatment:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "upm_treatment_date": "",
                        "upm_treatment": "",
                        "upm_amount": "",

                        "di_treatment_amount_correct" : 0,
                        "di_treatment_date_correct" : 0
                    })
                    di_pointer += 1
                    continue

                if abs(di_treatment["amount"] - upm_treatment["amount"]) < 0.05:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]
                    if upm_treatment["date_treatment"]:
                        upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
                    di_treatment_date_correct = int(di_treatment_date == upm_treatment_date)

                    # Add treatment text similarity calculation
                    treatment_similarity = get_text_similarity(di_treatment["treatment"], upm_treatment["treatment"])
                    di_treatment_text_correct = int(treatment_similarity >= TREATMENT_SIMILARITY_THRESHOLD)

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "upm_treatment_date": upm_treatment["date_treatment"],
                        "upm_treatment": upm_treatment["treatment"],
                        "upm_amount": upm_treatment["amount"],

                        "di_treatment_amount_correct" : 1,
                        "di_treatment_date_correct" : di_treatment_date_correct,
                        "di_treatment_desc_correct": di_treatment_text_correct,
                        "di_treatment_similarity": treatment_similarity
                    })
                    di_pointer += 1
                    upm_pointer += 1
                elif di_treatment["amount"] < upm_treatment["amount"]:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "upm_treatment_date": "",
                        "upm_treatment": "",
                        "upm_amount": "",

                        "di_treatment_amount_correct" : 0,
                        "di_treatment_date_correct" : 0,
                        "di_treatment_desc_correct": 0,
                        "di_treatment_similarity": 0
                    })
                    di_pointer += 1
                else:
                    if upm_treatment["date_treatment"]:
                        upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",
                        "di_treatment_date_conf": "",
                        "di_treatment_conf": "",
                        "di_amount_conf": "",

                        "upm_treatment_date": upm_treatment_date,
                        "upm_treatment": upm_treatment["treatment"],
                        "upm_amount": upm_treatment["amount"],

                        "di_treatment_amount_correct" : "",
                        "di_treatment_date_correct" : "",
                        "di_treatment_desc_correct": "",
                        "di_treatment_similarity": ""
                    })

                    upm_pointer += 1
         
            # If there are treatments, loop through each one and create a flat record 
            # by combining the invoice summary with the treatment details.
            if treatments:
                for treatment_item in treatments:
                    row = summary.copy()
                    row.update(treatment_item)
                    summary_list.append(row)
            # If there are no treatments, add the main summary record as is.
            # When converted to a DataFrame, treatment columns will be empty (NaN).
            else:
                summary_list.append(summary)

    summary_list = sorted(summary_list, key=lambda x: (x["claimno"], x["docfile"]))
    return summary_list

def gather_all_info_truuth_di(di_res_dict: Dict,
                    truuth_info_dict: Dict,
                    paddleocr_res_dict: Dict,
                    claimno2docfile: Dict,
                    docfile2claimno: Dict):

    summary_list = []

    # Threshold for considering treatments as similar
    TREATMENT_SIMILARITY_THRESHOLD = 0.7

    # files processed by Azure Document Intelligence
    for k, v in di_res_dict.items():
        docfile = k
        invoices = v

        mapping_claimno = docfile2claimno[docfile.lower()]

        if not mapping_claimno:
            logger.warning(f"No claim number mapping found for docfile: {docfile}. Skipping.")
            continue

        logger.info(f"Gather infomation: {mapping_claimno} {docfile}")

        paddleocr_content = paddleocr_res_dict[docfile.lower()]["content"]

        if len(invoices) > 1:
            print(k, mapping_claimno, len(invoices))
            print("-"*40)
        
        # Check if the claim number exists in truuth_res_dict
        if mapping_claimno not in truuth_info_dict:
            logger.warning(f"Claim number {mapping_claimno} not found in Truuth data. Processing with empty Truuth data.")
            # Create empty Truuth info with default values
            truuth_info = {
                "claim_no": mapping_claimno,
                "invoice_no": "",
                "invoice_date": None,
                "invoice_total": 0,
                "service_provider": "",
                "service_provider_no": "",
                "service_provider_evidence": "",
                "service_provider2": "",
                "service_provider_no2": "",
                "service_provider_evidence2": "",
                "treatments": [
                    {
                        "date_treatment": "",
                        "treatment": "",
                        "amount": "",
                    }
                ]
            }
        else:
            truuth_info = truuth_info_dict[mapping_claimno]
            
        for invoice in invoices:
            # check invoice no
            di_invoice_no = invoice["invoice_no"]
            truuth_invoice_no = str(truuth_info["invoice_no"])
            di_invoice_no_correct = int(di_invoice_no == truuth_invoice_no)
    
            # check service provider
            di_service_provider = invoice["service_provider_fm"]
            di_service_provider_no = invoice["service_provider_no_fm"]
            di_service_provider_evidence = invoice["service_provider_evidence"]
            di_service_provider2 = invoice["service_provider_fm2"]
            di_service_provider_no2 = invoice["service_provider_no_fm2"]
            di_service_provider_evidence2 = invoice["service_provider_evidence2"]
            di_service_provider_address = invoice["service_provider_address"]
            truuth_service_provider = truuth_info["service_provider"]
            truuth_service_provider_no = truuth_info["service_provider_no"]


            di_service_provider_correct = int(di_service_provider_no == truuth_service_provider_no)
            
            # check invoice date TODO cannot check Truuth results now 15OCT24
            di_invoice_date = invoice["invoice_date"].isoformat() if not isinstance(invoice["invoice_date"], str) else invoice["invoice_date"]
            if truuth_info["invoice_date"]:
                print(truuth_info["invoice_date"])
                parsed_date = datetime.fromisoformat(truuth_info["invoice_date"])
                truuth_invoice_date = parsed_date.date().isoformat()
            else:
                truuth_invoice_date = ""
            # if truuth_info["invoice_date"] != truuth_info["invoice_date"].date().isoformat():
            #     print("invoice_date mismatch", k, mapping_claimno)
            di_invoice_date_correct = int(di_invoice_date == truuth_invoice_date)

            # check total amount ### TODO
            di_total_amount = invoice["invoice_total"]
            truuth_total_amount = truuth_info["total_amount"]  
            # if stat_info.get("invoice_total_truuth", -1) != truuth_info.get("invoice_total", None):
            #     print("invoice_total mismatch", k, mapping_claimno)
            di_total_amount_correct = int(abs(di_total_amount - truuth_total_amount) < 0.05)

            summary = {
                "claimno": mapping_claimno,
                "docfile": docfile,
                # "paddleocr_content": paddleocr_content,

                "di_conf": min(invoice["invoice_no_conf"], invoice["invoice_date_conf"], invoice["invoice_total_conf"]),

                "di_invoice_no": di_invoice_no,
                "truuth_invoice_no": truuth_invoice_no,
                "di_invoice_no_correct": di_invoice_no_correct,
                "di_invoice_no_conf": invoice["invoice_no_conf"],
                "truuth_invoice_no_conf": truuth_info.get("invoice_no_conf", 0.0),

                "di_service_provider": di_service_provider,
                "di_service_provider_conf": invoice["service_provider_conf"],
                "di_service_provider_no": di_service_provider_no,
                "di_service_provider_evidence": di_service_provider_evidence,
                "truuth_service_provider": truuth_service_provider,
                "truuth_service_provider_conf": truuth_info.get("service_provider_conf", 0.0),
                "di_service_provider_correct": di_service_provider_correct,
                "di_service_provider_fm2": di_service_provider2,
                "di_service_provider_no_fm2": di_service_provider2,
                "di_service_provider_evidence2": di_service_provider_evidence2,
                # "di_service_provider_address": di_service_provider_address
                # "di_abn": invoice["ABN"],

                "di_invoice_date": di_invoice_date,
                "truuth_invoice_date": truuth_invoice_date,
                "di_invoice_date_correct": di_invoice_date_correct,
                "di_invoice_date_conf": invoice["invoice_date_conf"],
                "truuth_invoice_date_conf": truuth_info.get("invoice_date_conf", 0.0),

                "di_total_amount": di_total_amount,
                "truuth_total_amount": truuth_total_amount,
                "di_total_amount_correct": di_total_amount_correct,
                "di_total_amount_conf": invoice["invoice_total_conf"],
                "truuth_total_amount_conf": truuth_info.get("total_amount_conf", 0.0),

                "rule_res": invoice["rule_res"],
                "if_fallout": int("FALL OUT" in invoice["rule_res"]),
                "truuth_conf" : truuth_info.get("ocr_confidence",0),
                "truuth_error" : truuth_info.get("truuth_error",0)
            }

            sorted_di_treatments = sorted(invoice["treatments"], key=lambda x: x["amount"])
            sorted_truuth_treatments = sorted(truuth_info["treatments"], key=lambda x: x["treatment_amount"])
            treatments = []
            di_pointer, truuth_pointer = 0, 0
            while di_pointer < len(sorted_di_treatments) or truuth_pointer < len(sorted_truuth_treatments):
                try:
                    di_treatment = sorted_di_treatments[di_pointer]
                except IndexError:
                    assert di_pointer == len(sorted_di_treatments)
                    di_treatment = {}
                try:
                    truuth_treatment = sorted_truuth_treatments[truuth_pointer]
                except IndexError:
                    assert truuth_pointer == len(sorted_truuth_treatments)
                    truuth_treatment = {}
                # logger.info(di_treatment)

                # logger.info(upm_treatment)
                if not di_treatment and not truuth_treatment:
                    logger.error(f"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.")
                    logger.error(f"sorted_di_treatments: {sorted_di_treatments}")
                    logger.error(f"sorted_truuth_treatments: {sorted_truuth_treatments}")
                    logger.error(f"di_pointer: {di_pointer}, truuth_pointer: {truuth_pointer}")
                    break

                if not di_treatment:
                    if truuth_treatment["treatment_date"]:
                        parsed_date = datetime.fromisoformat(truuth_treatment["treatment_date"])
                        truuth_treatment_date = parsed_date.date().isoformat()
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",
                        "di_treatment_date_conf": "",
                        "di_treatment_conf": "",
                        "di_amount_conf": "",

                        "truuth_treatment_date": truuth_treatment_date,
                        "truuth_treatment": truuth_treatment["treatment_description"],
                        "truuth_amount": truuth_treatment["treatment_amount"],
                        "truuth_treatment_date_conf": truuth_treatment["treatment_date_conf"],
                        "truuth_amount_conf": truuth_treatment["treatment_amount_conf"],
                        "truuth_treatment_conf": truuth_treatment["treatment_description_conf"],

                        "di_treatment_amount_correct" : "",
                        "di_treatment_date_correct" : ""
                    })
                    truuth_pointer += 1
                    continue

                if not truuth_treatment:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "truuth_treatment_date": "",
                        "truuth_treatment": "",
                        "truuth_amount": "",
                        "truuth_treatment_date_conf": "",
                        "truuth_amount_conf": "",
                        "truuth_treatment_conf": "",

                        "di_treatment_amount_correct" : 0,
                        "di_treatment_date_correct" : 0
                    })
                    di_pointer += 1
                    continue

                if abs(di_treatment["amount"] - truuth_treatment["treatment_amount"]) < 0.05:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]
                    if truuth_treatment["treatment_date"]:
                        parsed_date = datetime.fromisoformat(truuth_treatment["treatment_date"])
                        truuth_treatment_date = parsed_date.date().isoformat()
                    di_treatment_date_correct = int(di_treatment_date == truuth_treatment_date)

                    # Add treatment text similarity calculation
                    treatment_similarity = get_text_similarity(di_treatment["treatment"], truuth_treatment["treatment_description"])
                    di_treatment_text_correct = int(treatment_similarity >= TREATMENT_SIMILARITY_THRESHOLD)

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "truuth_treatment_date": truuth_treatment_date,
                        "truuth_treatment": truuth_treatment["treatment_description"],
                        "truuth_amount": truuth_treatment["treatment_amount"],
                        "truuth_treatment_date_conf": truuth_treatment["treatment_date_conf"],
                        "truuth_amount_conf": truuth_treatment["treatment_amount_conf"],
                        "truuth_treatment_conf": truuth_treatment["treatment_description_conf"],

                        "di_treatment_amount_correct" : 1,
                        "di_treatment_date_correct" : di_treatment_date_correct,
                        "di_treatment_desc_correct": di_treatment_text_correct,
                        "di_treatment_similarity": treatment_similarity
                    })
                    di_pointer += 1
                    truuth_pointer += 1
                elif di_treatment["amount"] < truuth_treatment["treatment_amount"]:

                    di_treatment_date = parsed_date.date().isoformat()
                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "truuth_treatment_date": "",
                        "truuth_treatment": "",
                        "truuth_amount": "",
                        "truuth_treatment_date_conf": "",
                        "truuth_amount_conf": "",
                        "truuth_treatment_conf": "",

                        "di_treatment_amount_correct" : 0,
                        "di_treatment_date_correct" : 0,
                        "di_treatment_desc_correct": 0,
                        "di_treatment_similarity": 0
                    })
                    di_pointer += 1
                else:
                    if truuth_treatment["treatment_date"]:
                        parsed_date = datetime.fromisoformat(truuth_treatment["treatment_date"])
                        truuth_treatment_date = parsed_date.date().isoformat()
                    else:
                        truuth_treatment_date = ""
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",
                        "di_treatment_date_conf": "",
                        "di_treatment_conf": "",
                        "di_amount_conf": "",

                        "truuth_treatment_date": truuth_treatment_date,
                        "truuth_treatment": truuth_treatment["treatment_description"],
                        "truuth_amount": truuth_treatment["treatment_amount"],
                        "truuth_treatment_date_conf": truuth_treatment["treatment_date_conf"],
                        "truuth_amount_conf": truuth_treatment["treatment_amount_conf"],
                        "truuth_treatment_conf": truuth_treatment["treatment_description_conf"],

                        "di_treatment_amount_correct" : "",
                        "di_treatment_date_correct" : "",
                        "di_treatment_desc_correct": "",
                        "di_treatment_similarity": ""
                    })

                    truuth_pointer += 1
         
            # If there are treatments, loop through each one and create a flat record 
            # by combining the invoice summary with the treatment details.
            if treatments:
                for treatment_item in treatments:
                    row = summary.copy()
                    row.update(treatment_item)
                    summary_list.append(row)
            # If there are no treatments, add the main summary record as is.
            # When converted to a DataFrame, treatment columns will be empty (NaN).
            else:
                summary_list.append(summary)

    summary_list = sorted(summary_list, key=lambda x: (x["claimno"], x["docfile"]))
    return summary_list

def add_pass_column(df):
    """
    Adds a 'Pass' column to the DataFrame. The value is 1 if all rows in a ClaimNo group have All_Correct = 1,
    else 0. Only the first row of each group will be assigned the Pass value; others will be NaN.

    Parameters:
    df (pd.DataFrame): Input DataFrame.

    Returns:
    pd.DataFrame: DataFrame with the added 'Pass' column.
    """
    # Define indicator columns
    indicator_cols = [
        "di_invoice_no_correct", "di_service_provider_correct", "di_invoice_date_correct",
        "di_total_amount_correct", "di_treatment_amount_correct", "di_treatment_date_correct",
        "di_treatment_desc_correct"
    ]
    
    # Compute All_Correct per row
    df['All_Correct'] = (df[indicator_cols] == 1).all(axis=1).astype(int)
    
    # Determine if all rows in each ClaimNo group are correct
    pass_mask = df.groupby('claimno')['All_Correct'].transform('all')
    
    # Initialize column with NaNs
    df['Pass'] = None
    
    # Set Pass = 1 or 0 on the first row of each ClaimNo group
    first_idx = df.groupby('claimno').head(1).index
    df.loc[first_idx, 'Pass'] = df.loc[first_idx, 'claimno'].map(
        lambda x: int(df[df['claimno'] == x]['All_Correct'].all())
    )

    return df