"""
Configuration file for the OCR processing pipeline.
"""
from pathlib import Path
import os

# Root directory
ROOTDIR = Path("/home/<USER>/repos/OCR_in_house")

# Sample prefix
SAMPLE_PREFIX = "test10"

# Data locations
DATA_FOLDER = ROOTDIR / f"data/samples/{SAMPLE_PREFIX}_samples_DI"
RAW_DATA_FILE = ROOTDIR / f"data/input/{SAMPLE_PREFIX}_samples_DI.csv"
DI_OUTPUT_DATA_FOLDER = ROOTDIR / f"data/samples/{SAMPLE_PREFIX}_samples_DI_res"
UPM_GROUND_TRUTH_PATH = ROOTDIR / f"data/sot/{SAMPLE_PREFIX}_SourceOfTruth.xlsx"
PADDLEOCR_RES_PATH = ROOTDIR / f"data/paddleocr/{SAMPLE_PREFIX}_samples_paddleocr.csv"
TRUUTH_PATH = ROOTDIR / f"data/truuth/{SAMPLE_PREFIX}_samples_truuth.csv"
RULE_RES_OUTPUT_UPM_PATH = ROOTDIR / f"data/result/upm/{SAMPLE_PREFIX}_samples_DI_evaluation_upm.xlsx"
RULE_RES_OUTPUT_TRUUTH_PATH = ROOTDIR / f"data/result/truuth/{SAMPLE_PREFIX}_samples_DI_evaluation_truuth.xlsx"
SERVICE_PROVIDER_CSV_PATH = ROOTDIR / f"data/ref_service_provider_updated.csv"
PARSED_DI_OUTPUT_FOLDER = ROOTDIR / f"data/parsed_di/{SAMPLE_PREFIX}_samples_DI_parsed"

# Database configuration
DB_SERVER = "10.3.0.90"
DB_NAME = "BIA"

# Azure Document Intelligence configuration
MAX_RETRY = 3

# PaddleOCR chunk size (number of files per incremental batch).
# Fixed default; adjust here if you want a different baseline.
PADDLE_OCR_CHUNK_SIZE = 2
