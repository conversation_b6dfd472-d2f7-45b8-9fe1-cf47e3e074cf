import os
import json
import time
import pickle as pk
from pathlib import Path
from loguru import logger
import pandas as pd
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.exceptions import HttpResponseError
from azure.core.credentials import AzureKeyCredential

def run_document_intelligence(
    input_directory: str,
    output_directory: str,
    model_id: str = "prebuilt-invoice",
    max_retry: int = 3,
    resume: bool = True,
    build_aggregate: bool = True,
):
    """Analyze documents with Azure Document Intelligence with resumable streaming.

    Behavior:
    - Ensures output directory exists.
    - Detects already processed files (presence of <stem>.json) and skips them if resume=True.
    - Processes only missing files, writing per-file JSON/PK immediately.
    - Maintains a manifest (index.csv) with status per file (success, error).
    - Optionally (build_aggregate=True) rebuilds aggregated ans.pk/ans.json at end from per-file JSONs.

    Args:
        input_directory: Folder containing documents to analyze.
        output_directory: Folder where results and manifest will be stored.
        model_id: Azure DI model identifier.
        max_retry: Retry attempts per file on transient errors.
        resume: If True, skip already processed files.
        build_aggregate: If True, (re)build aggregated result after processing.
    """
    output_path = Path(output_directory)
    input_path = Path(input_directory)

    # Initialize Azure Document Intelligence client
    endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPONT")
    key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
    doc_analysis_client = DocumentAnalysisClient(
        endpoint=endpoint, credential=AzureKeyCredential(key)
    )

    if not output_path.exists():
        logger.info(f"Output directory '{output_path}' not found. Creating.")
        os.makedirs(output_path)

    # Manifest path
    manifest_path = output_path / "index.csv"
    manifest_rows = []
    processed = set()
    if resume:
        # Determine already processed files by presence of json outputs
        existing_jsons = {Path(f).stem for f in os.listdir(output_path) if f.endswith(".json") and f != "ans.json"}
        processed = existing_jsons
        if existing_jsons:
            logger.info(f"Resume enabled: {len(existing_jsons)} files already processed will be skipped.")

    doc_list = sorted([f for f in os.listdir(input_path) if not f.startswith('.')])
    total_docs = len(doc_list)
    remaining_files = [f for f in doc_list if Path(f).stem not in processed]
    logger.info(f"DI: total={total_docs}, already_processed={len(processed)}, remaining={len(remaining_files)}")

    for i, file_name in enumerate(remaining_files):
        retry_num = 0
        file_path = input_path / file_name
        file_stem = file_path.stem
        logger.info(f"Processing document {i+1}/{len(remaining_files)}: '{file_name}'")
        status = ""
        error_msg = ""
        while retry_num < max_retry:
            try:
                with open(file_path, "rb") as f:
                    poller = doc_analysis_client.begin_analyze_document(model_id=model_id, document=f)
                    result = poller.result()
                result_dict = result.to_dict()
                # Save individual results
                with open(output_path / f"{file_stem}.pk", "wb") as fout:
                    pk.dump(result_dict, fout)
                with open(output_path / f"{file_stem}.json", "w") as fout:
                    json.dump(result_dict, fout, indent=4, default=str)
                status = "success"
                logger.success(f"Saved '{file_name}'")
                time.sleep(1)
                break
            except HttpResponseError as hre:
                retry_num += 1
                status = "retry"
                error_msg = str(hre)
                logger.warning(f"HTTP error attempt {retry_num}/{max_retry} for '{file_name}': {hre}")
                time.sleep(5 * retry_num)
            except Exception as e:
                status = "error"
                error_msg = str(e)
                logger.exception(f"Unexpected error for '{file_name}': {e}")
                break
        if status != "success" and retry_num == max_retry:
            status = "failed"
        manifest_rows.append({"file_name": file_name, "stem": file_stem, "status": status, "error": error_msg})
        # Write/append manifest atomically after each file
        tmp_manifest = f"{manifest_path}.tmp"
        pd.DataFrame(manifest_rows).to_csv(tmp_manifest, index=False)
        os.replace(tmp_manifest, manifest_path)

    # Build aggregated results from per-file JSONs
    if build_aggregate:
        all_results = []
        json_files = [f for f in os.listdir(output_path) if f.endswith('.json') and f not in ("ans.json",)]
        for jf in sorted(json_files):
            stem = Path(jf).stem
            with open(output_path / jf, 'r') as fin:
                try:
                    invoice = json.load(fin)
                except Exception as e:
                    logger.warning(f"Skipping corrupt json '{jf}': {e}")
                    continue
            # Use original filename if available from manifest
            file_name_match = next((r["file_name"] for r in manifest_rows if r["stem"] == stem), f"{stem}")
            all_results.append({"file_path": file_name_match, "invoice": invoice})
        logger.info(f"Aggregating {len(all_results)} DI documents into ans.*")
        # Atomic writes
        tmp_pk = output_path / "ans.pk.tmp"
        with open(tmp_pk, "wb") as fout:
            pk.dump(all_results, fout)
        os.replace(tmp_pk, output_path / "ans.pk")
        tmp_json = output_path / "ans.json.tmp"
        with open(tmp_json, "w") as fout:
            json.dump(all_results, fout, indent=4, default=str)
        os.replace(tmp_json, output_path / "ans.json")
        logger.info("Document intelligence aggregation complete.")

    logger.info("Document intelligence processing finished.")